import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/views/common/appbar.dart';
import 'package:the_bakers_bar_user/views/common/footer.dart';
import 'package:the_bakers_bar_user/views/homepage/custom_drawer.dart';
import 'views/common/quote.dart';
import 'views/common/responsive.dart';

final wrapperScafKey = GlobalKey<ScaffoldState>();

class Wrapper extends StatefulWidget {
  const Wrapper({
    super.key,
    required this.body,
    this.scafKey,
    this.endDrawer,
    this.scrollController,
    this.footer = true,
    this.allowScroll = true,
    this.showBackToTop = true,
    this.showFilter = false,
  });

  final Widget body;
  final Widget? endDrawer;
  final ScrollController? scrollController;
  final GlobalKey<ScaffoldState>? scafKey;
  final bool footer;
  final bool showFilter;
  final bool allowScroll;
  final bool showBackToTop;

  @override
  State<Wrapper> createState() => _WrapperState();
}

class _WrapperState extends State<Wrapper> {
  final wrapperScrlCtrl = ScrollController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  bool draweropen = false;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final screenwidth = MediaQuery.sizeOf(context).width;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      drawerScrimColor: Colors.transparent,
      key: _scaffoldKey,
      drawer: screenwidth < desktopMinSize
          ? Padding(
              padding: const EdgeInsets.only(top: 115.0),
              child: CustomDrawer(
                scafKey: wrapperScafKey,
                onTapFunct: () {
                  _scaffoldKey.currentState?.closeDrawer();
                  // setState(() {});
                },
              ),
            )
          : null,
      backgroundColor: Colors.white,
      onDrawerChanged: (isOpened) {
        // print(isOpened);
        draweropen = isOpened;
        setState(() {});
      },
      body: NestedScrollView(
        // key: ValueKey(DateTime),
        physics: widget.allowScroll
            ? null
            : const NeverScrollableScrollPhysics(),
        controller: widget.scrollController ?? wrapperScrlCtrl,
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              title: const Quote(),
              pinned: true,
              automaticallyImplyLeading: false,
              snap: true,
              scrolledUnderElevation: 12,
              elevation: 6,
              shadowColor: Colors.black12,
              floating: true,
              foregroundColor: Colors.white,
              surfaceTintColor: themeColor,
              backgroundColor: themeColor,
              forceElevated: true,

              // forceElevated: innerBoxIsScrolled,
              expandedHeight: 0, //(kIsWeb ? 80 : 60) + 32,
              bottom: PreferredSize(
                preferredSize: Size.fromHeight(
                  (kIsWeb ? (size.width >= mobileMinsize ? 115 : 115) : 64),
                ), //100
                child: MenuAppBar(
                  // drawerOpen: _scaffoldKey.currentState?.isDrawerOpen ?? true,
                  drawerOpen: draweropen,
                  onTapFunct: () {
                    _scaffoldKey.currentState?.openDrawer();
                    // setState(() {});
                  },
                ),
              ),
            ),
          ];
        },
        body: ListView(
          padding: EdgeInsets.zero,
          physics: widget.allowScroll
              ? const ClampingScrollPhysics()
              : const NeverScrollableScrollPhysics(),
          scrollDirection: Axis.vertical,
          shrinkWrap: true,
          children: [
            widget.body,
            if (widget.footer) Footer(size: size),
          ],
        ),
      ),
    );
  }
}
