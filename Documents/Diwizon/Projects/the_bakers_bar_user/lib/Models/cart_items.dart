import 'package:cloud_firestore/cloud_firestore.dart';

class CartItemsModel {
  final String id;
  final String pId;
  final String name;
  final String selectedflavour;
  final String weightSelected;
  final String deliveryTime;
  final String messageOnCake;
  // final String area;
  final DateTime delDate;
  final bool isLateNight;
  final num designPrice;
  final num taxPercent;
  num qty;
  final num price;
  final String delivery;
  final String? itemFulfilled;

  CartItemsModel({
    required this.id,
    required this.delivery,
    required this.pId,
    required this.qty,
    required this.name,
    required this.selectedflavour,
    required this.weightSelected,
    required this.deliveryTime,
    required this.messageOnCake,
    // required this.area,
    required this.delDate,
    required this.isLateNight,
    required this.price,
    required this.designPrice,
    required this.taxPercent,
    required this.itemFulfilled,
  });

  factory CartItemsModel.fromJson(id, Map<String, dynamic> json) {
    return CartItemsModel(
      id: id,
      pId: json['pId'],
      weightSelected: json['weightSelected'],
      price: json['price'],
      qty: json['qty'],
      messageOnCake: json['messageOnCake'],
      selectedflavour: json['selectedflavour'],
      name: json['name'],
      delivery: json['delivery'],
      deliveryTime: json['deliveryTime'],
      // area: json['area'],
      delDate: DateTime.tryParse(json['delDate']) ?? DateTime.now(),
      isLateNight: json['isLateNight'],
      designPrice: json['designPrice'] ?? 0,
      taxPercent: json['taxPercent'] ?? 0,
      itemFulfilled: json['itemFulfilled'] ?? "",
    );
  }

  toJson() {
    return {
      'id': id,
      'pId': pId,
      'delivery': delivery,
      'qty': qty,
      'price': price,
      'name': name,
      'selectedflavour': selectedflavour,
      'weightSelected': weightSelected,
      'deliveryTime': deliveryTime,
      'messageOnCake': messageOnCake,
      // 'area': area,
      'delDate': delDate.toIso8601String(),
      'isLateNight': isLateNight,
      'designPrice': designPrice,
      'taxPercent': taxPercent,
      'itemFulfilled': itemFulfilled,
    };
  }

  factory CartItemsModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;

    return CartItemsModel(
      id: data['id'],
      delivery: data['delivery'],
      pId: data['pId'],
      qty: data['qty'],
      price: data['price'],
      name: data['name'],
      selectedflavour: data['selectedflavour'],
      weightSelected: data['weightSelected'],
      deliveryTime: data['deliveryTime'],
      messageOnCake: data['messageOnCake'],
      // area: data['area'],
      delDate: (data['delDate'] as Timestamp).toDate(),
      isLateNight: data['isLateNight'],
      designPrice: data['designPrice'] ?? 0,
      taxPercent: data['taxPercent'] ?? 0,
      itemFulfilled: data['taxPeitemFulfilledrcent'] ?? "",
    );
  }
}

// address: (snapshot['address'] as Map<String, dynamic>)
//           .entries
//           .map((e) => AddressModel.fromJson(e.key, e.value))
//           .toList(),


//  cartitems: (snapshot['cartitems'] as Map<String, dynamic>)
//           .entries
//           .map((e) => CartItemsModel.fromJson(e.key, e.value))
//           .toList(),