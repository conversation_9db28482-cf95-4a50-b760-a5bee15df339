// import 'package:cloud_firestore/cloud_firestore.dart';

// class ProductModel {
//   final String docId;
//   final String name;
//   final String type;
//   final String tags;
//   final List<String>? flavour;
//   final DateTime createdAt;
//   final String sku;
//   final String? weightRangeDocId;
//   final List<String> images;
//   final String desc;
//   final bool notavailable;
//   final String? minOrderTime;
//   final String? designCostdocId;
//   // final bool isdessert;
//   // final bool isflower;
//   final num? fixedprice;
//   final bool tier;
//   final String? hyperlink;

//   ProductModel({
//     required this.tier,
//     required this.hyperlink,
//     required this.docId,
//     required this.name,
//     required this.type,
//     required this.tags,
//     this.flavour,
//     required this.createdAt,
//     required this.sku,
//     this.weightRangeDocId,
//     required this.images,
//     required this.desc,
//     required this.notavailable,
//     this.minOrderTime,
//     this.designCostdocId,
//     // required this.isdessert,
//     // required this.isflower,
//     this.fixedprice,
//   });

//   factory ProductModel.fromJson(Map<String, dynamic> json) {
//     return ProductModel(
//       docId: json['docId'],
//       name: json['name'],
//       type: json['type'],
//       tags: json['tags'],
//       // isdessert: json['isdessert'],
//       // isflower: json['isflower'],
//       fixedprice: json['fixedprice'],
//       flavour: List<String>.from(json['flavour']),
//       createdAt: DateTime.parse(json['createdAt']),
//       sku: json['sku'],
//       weightRangeDocId: json['weightRangeDocId'],
//       images: List<String>.from(json['images']),
//       desc: json['desc'],
//       notavailable: json['notavailable'],
//       minOrderTime: json['minOrderTime'],
//       designCostdocId: json['designCostdocId'],
//       tier: json['tier'] ?? false,
//       hyperlink: json['hyperlink'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'docId': docId,
//       'name': name,
//       'type': type,
//       'tags': tags,
//       // 'isdessert': isdessert,
//       // 'isflower': isflower,
//       'fixedprice': fixedprice,
//       'flavour': flavour,
//       'createdAt': createdAt.toIso8601String(),
//       'sku': sku,
//       'weightRangeDocId': weightRangeDocId,
//       'images': images,
//       'desc': desc,
//       'notavailable': notavailable,
//       'minOrderTime': minOrderTime,
//       'designCostdocId': designCostdocId,
//       'tier': tier,
//       'hyperlink': hyperlink,
//     };
//   }

//   factory ProductModel.fromSnapshot(DocumentSnapshot snapshot) {
//     Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
//     return ProductModel(
//       docId: snapshot.id,
//       name: data['name'],
//       type: data['type'],
//       tags: data['tags'],
//       // isdessert: data['isdessert'],
//       // isflower: data['isflower'],
//       fixedprice: data['fixedprice'],
//       flavour: List<String>.from(data['flavour']),
//       createdAt: (data['createdAt'] as Timestamp).toDate(),
//       sku: data['sku'],
//       weightRangeDocId: data['weightRangeDocId'],
//       images: List<String>.from(data['images']),
//       desc: data['desc'],
//       notavailable: data['notavailable'],
//       minOrderTime: data['minOrderTime'],
//       designCostdocId: data['designCostdocId'],
//       tier: data['tier'] ?? false,
//       hyperlink: data['hyperlink'],
//     );
//   }
// }

import 'package:cloud_firestore/cloud_firestore.dart';

class ProductType {
  static const dessert = "Dessert";
  static const regularCake = "Regular Cake";
  static const designerCake = "Designer Cake";
  static const trendingcake = "Trending Cake";
  static const flower = "Flower";
  static const addon = "Add-On";
}

class ProductModel {
  final String docId;
  final String name;
  final String type;
  final List<String> tags;
  final List<String>? catNameList;
  final List<String>? titleTag;
  final List<String>? flavour;
  final DateTime createdAt;
  final String sku;
  final String? weightRangeDocId;
  final String? lower;
  final List<String> images;
  final String desc;
  final bool notavailable;
  final String? minOrderTime;
  final String? designCostdocId;
  // final bool isdessert;
  // final bool isflower;
  final num? fixedprice;
  final bool tier;
  final String? hyperlink;

  ProductModel({
    required this.tier,
    this.hyperlink,
    this.lower,
    required this.docId,
    required this.name,
    required this.type,
    required this.tags,
    this.catNameList,
    this.titleTag,
    this.flavour,
    required this.createdAt,
    required this.sku,
    this.weightRangeDocId,
    required this.images,
    required this.desc,
    required this.notavailable,
    this.minOrderTime,
    this.designCostdocId,
    // required this.isdessert,
    // required this.isflower,
    this.fixedprice,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      docId: json['docId'],
      name: json['name'],
      type: json['type'],
      tags: List<String>.from(json['tags']),
      titleTag: List<String>.from(json['titleTag'] ?? []),
      lower: json['lower'],
      // isdessert: json['isdessert'],
      // isflower: json['isflower'],
      catNameList: List<String>.from(json['catNameList']),
      fixedprice: json['fixedprice'],
      flavour: List<String>.from(json['flavour']),
      createdAt: DateTime.parse(json['createdAt']),
      sku: json['sku'],
      weightRangeDocId: json['weightRangeDocId'],
      images: List<String>.from(json['images']),
      desc: json['desc'],
      notavailable: json['notavailable'],
      minOrderTime: json['minOrderTime'],
      designCostdocId: json['designCostdocId'],
      tier: json['tier'] ?? false,
      hyperlink: json['hyperlink'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'type': type,
      'tags': tags,
      // 'titleTag': titleTag,
      // 'isdessert': isdessert,
      // 'isflower': isflower,
      'fixedprice': fixedprice,
      'lower': lower,
      'catNameList': catNameList,
      'flavour': flavour,
      'createdAt': createdAt.toIso8601String(),
      'sku': sku,
      'weightRangeDocId': weightRangeDocId,
      'images': images,
      'desc': desc,
      'notavailable': notavailable,
      'minOrderTime': minOrderTime,
      'designCostdocId': designCostdocId,
      'tier': tier,
      'hyperlink': hyperlink,
    };
  }

  factory ProductModel.fromSnapshot(DocumentSnapshot snapshot) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    return ProductModel(
      docId: snapshot.id,
      name: data['name'],
      type: data['type'],
      tags: List<String>.from(data['tags']),
      // titleTag: List<String>.from(data['titleTag'] ?? []),
      lower: data['lower'],
      // isdessert: data['isdessert'],
      // isflower: data['isflower'],
      fixedprice: data['fixedprice'],
      catNameList: List<String>.from(data['catNameList'] ?? []),
      flavour: List<String>.from(data['flavour'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      sku: data['sku'],
      weightRangeDocId: data['weightRangeDocId'],
      images: List<String>.from(data['images']),
      desc: data['desc'],
      notavailable: data['notavailable'],
      minOrderTime: data['minOrderTime'],
      designCostdocId: data['designCostdocId'],
      tier: data['tier'] ?? false,
      hyperlink: data['hyperlink'],
    );
  }
}
