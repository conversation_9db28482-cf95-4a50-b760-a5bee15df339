import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:the_bakers_bar_user/Models/addressmodel.dart';
import 'package:the_bakers_bar_user/Models/cart_items.dart';

class Userdetails {
  final String docId;
  // final String country;
  final String fname;
  final String lname;
  final String contact;
  final List<AddressModel> address;
  final List<CartItemsModel> cartitems;

  Userdetails({
    required this.docId,
    // required this.country,
    required this.contact,
    required this.fname,
    required this.lname,
    required this.address,
    required this.cartitems,
  });

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      // 'country': country,
      'contact': contact,
      'fname': fname,
      'lname': lname,
      'address': address.map((e) => e.toJson()).toList(),
      'cartitems': cartitems.map((e) => e.toJson()).toList(),
    };
  }

  factory Userdetails.fromJson(Map<String, dynamic> json) {
    return Userdetails(
      docId: json['docId'],
      // country: json['country'],
      contact: json['contact'],
      fname: json['fname'],
      lname: json['lname'],
      address: (json['address'] as List<dynamic>)
          .map((e) => AddressModel.fromJson(e, e as Map<String, dynamic>))
          .toList(),
      cartitems: (json['cartitems'] as List<dynamic>)
          .map((e) => CartItemsModel.fromJson(e, e as Map<String, dynamic>))
          .toList(),
    );
  }
  factory Userdetails.fromSnapshot(DocumentSnapshot snapshot) {
    // print(snapshot['cartitems']);
    // print(Userdetails);
    // print(snapshot.id);

    return Userdetails(
      docId: snapshot.id,
      // country: snapshot['country'],
      contact: snapshot['contact'],
      fname: snapshot['fname'],
      lname: snapshot['lname'],
      address: Map.castFrom(
        snapshot['address'],
      ).entries.map((e) => AddressModel.fromJson(e.key, e.value)).toList(),

      cartitems: Map.castFrom(
        snapshot['cartitems'],
      ).entries.map((e) => CartItemsModel.fromJson(e.key, e.value)).toList(),

      // List<CartItemsModel>.from(
      //     snapshot['cartitems'].map((e) => CartItemsModel.fromJson(e))),
      // (snapshot['cartitems'] as List<dynamic>)
      //     .map((e) => CartItemsModel.fromJson(e as Map<String, dynamic>))
      //     .toList(),
    );
  }
}
