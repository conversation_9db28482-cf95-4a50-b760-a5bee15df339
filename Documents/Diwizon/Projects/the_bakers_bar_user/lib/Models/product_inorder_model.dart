class ProductInOrder {
  final String id;
  final DateTime delTime;

  ProductInOrder({
    required this.id,
    required this.delTime,
  });

  factory ProductInOrder.fromJson(Map<String, dynamic> json) {
    return ProductInOrder(
      id: json['id'],
      delTime: DateTime.parse(json['delTime']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'delTime': delTime.toIso8601String(),
    };
  }
}
