import 'package:cloud_firestore/cloud_firestore.dart';

class MainCategoryModel {
  final String docId;
  final String name;
  final List<String> catSet;
  final List<String> tags;
  final bool special;

  MainCategoryModel({
    required this.docId,
    required this.name,
    required this.catSet,
    required this.tags,
    required this.special,
  });

  factory MainCategoryModel.fromJson(Map<String, dynamic> json) {
    return MainCategoryModel(
        docId: json['docId'],
        name: json['name'],
        catSet: List<String>.from(json['catSet']),
        tags: List<String>.from(json['tags']),
        special: json['special']);
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'catSet': catSet,
      'tags': tags,
      'special': special
    };
  }

  factory MainCategoryModel.fromSnapshot(DocumentSnapshot snapshot) {
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    return MainCategoryModel(
      docId: snapshot.id,
      name: data['name'] ?? '',
      catSet: List<String>.from(data['catSet'] ?? []),
      tags: List<String>.from(data['tags'] ?? []),
      special: data['special'] ?? false,
    );
  }
}
