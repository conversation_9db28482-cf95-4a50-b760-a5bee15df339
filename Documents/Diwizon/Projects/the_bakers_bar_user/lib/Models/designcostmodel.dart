import 'package:cloud_firestore/cloud_firestore.dart';

class DesignCostModel {
  final String docId;
  final String code;
  final num basePrice;
  final num increment;

  DesignCostModel({
    required this.docId,
    required this.code,
    required this.basePrice,
    required this.increment,
  });

  factory DesignCostModel.fromJson(Map<String, dynamic> json) {
    return DesignCostModel(
      docId: json['docId'],
      code: json['code'],
      basePrice: json['basePrice'],
      increment: json['increment'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'code': code,
      'basePrice': basePrice,
      'increment': increment,
    };
  }

  factory DesignCostModel.fromSnapshot(DocumentSnapshot snapshot) {
    Map<String, dynamic> json = snapshot.data() as Map<String, dynamic>;
    return DesignCostModel(
      docId: snapshot.id,
      code: json['code'],
      basePrice: json['basePrice'],
      increment: json['increment'],
    );
  }
}
