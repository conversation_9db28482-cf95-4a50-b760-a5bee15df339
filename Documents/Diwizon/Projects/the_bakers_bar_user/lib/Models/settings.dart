import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:the_bakers_bar_user/Models/catsetmodel.dart';
import 'package:the_bakers_bar_user/Models/delivery_charge_model.dart';
import 'package:the_bakers_bar_user/Models/discount_coupon.dart';

class SettingsModel {
  final int fixtimedelivery;
  final int midnightdelivery;
  final String contact;
  final List<DelchargeModel> delcharges;
  final List<CatSetModel> catset;
  final List<DiscountCouponModel> coupons;

  final num tax;
  final num designerTime;
  final num regularTime;
  final num orderNo;

  SettingsModel({
    required this.fixtimedelivery,
    required this.midnightdelivery,
    required this.contact,
    required this.delcharges,
    required this.coupons,
    required this.catset,
    required this.tax,
    required this.orderNo,
    required this.designerTime,
    required this.regularTime,
  });

  // Map<String, dynamic> toJson() {
  //   return {
  //     'fixtimedelivery': fixtimedelivery,
  //     'midnightdelivery': midnightdelivery,
  //     'contact': contact,
  //     'delcharges': delcharges.map((charge) => charge.toJson()).toList(),
  //     'catset': catset.map((cat) => cat.toJson()).toList(),
  //   };
  // }

  // // Method to create a Settings object from JSON
  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      tax: json['tax'],
      designerTime: json['designerTime'],
      regularTime: json['regularTime'],
      orderNo: json['orderNo'],
      fixtimedelivery: json['fixtimedelivery'],
      midnightdelivery: json['midnightdelivery'],
      contact: json['contact'],
      delcharges: Map.from(
        json['delcharges'],
      ).entries.map((e) => DelchargeModel.fromJson(e.key, e.value)).toList(),
      coupons: Map.from(json['coupons']).entries
          .map((e) => DiscountCouponModel.fromJson(e.key, e.value))
          .toList(),
      catset: Map.from(
        json['catset'],
      ).entries.map((e) => CatSetModel.fromJson(e.key, e.value)).toList(),
      // catSet: (json['catSet'] as List)
      //     .map((catJson) => CatSetModel.fromJson(catJson))
      //     .toList(),
    );
  }
  factory SettingsModel.fromSnap(DocumentSnapshot json) {
    return SettingsModel(
      tax: json['tax'],
      designerTime: json['designerTime'],
      regularTime: json['regularTime'],
      orderNo: json['orderNo'],
      fixtimedelivery: json['fixtimedelivery'],
      midnightdelivery: json['midnightdelivery'],
      contact: json['contact'],
      delcharges: Map.from(
        json['delcharges'],
      ).entries.map((e) => DelchargeModel.fromJson(e.key, e.value)).toList(),
      catset: Map.from(
        json['catset'],
      ).entries.map((e) => CatSetModel.fromJson(e.key, e.value)).toList(),
      coupons: Map.from(json['coupons']).entries
          .map((e) => DiscountCouponModel.fromJson(e.key, e.value))
          .toList(),
      // catSet: (json['catSet'] as List)
      //     .map((catJson) => CatSetModel.fromJson(catJson))
      //     .toList(),
    );
  }
}
