import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:the_bakers_bar_user/Models/cart_items.dart';
import 'package:the_bakers_bar_user/Models/catsetmodel.dart';
import 'package:the_bakers_bar_user/Models/designcostmodel.dart';
import 'package:the_bakers_bar_user/Models/discount_coupon.dart';
import 'package:the_bakers_bar_user/Models/flavourmodel.dart';
import 'package:the_bakers_bar_user/Models/maincategorymodel.dart';
import 'package:the_bakers_bar_user/Models/order_model.dart';
import 'package:the_bakers_bar_user/Models/productmodel.dart';
import 'package:the_bakers_bar_user/Models/settings.dart';
import 'package:the_bakers_bar_user/Models/user_details.dart';
import 'package:the_bakers_bar_user/Models/weight_range_model.dart';
import 'package:the_bakers_bar_user/shared/firebase.dart';
import 'package:the_bakers_bar_user/shared/router.dart';

class ProductCtrl extends GetxController {
  num totalPrice = 0;
  num finalPrice = 0;
  num couponvalue = 0;

  DiscountCouponModel? coupon;
  List<ProductModel> products = <ProductModel>[];
  List<FlavourModel> flavours = <FlavourModel>[];
  List<DesignCostModel> designCost = <DesignCostModel>[];
  List<WeightRangeModel> weightRange = <WeightRangeModel>[];
  List<CartItemsModel> orderProduct = <CartItemsModel>[];
  Userdetails? userDetails;
  List<String> subCategoriesList = [];
  int wasOnPageNo = 0;
  double recentScrollOffset = 0;

  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? productStr;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? weightStr;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? designStr;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? flavourStr;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? userStr;

  // StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? orderStr;
  // List<OrderModel> orders = <OrderModel>[];
  // FlavourModel? selectedFlavour;
  List<MainCategoryModel> maincategory = <MainCategoryModel>[];
  // List<CatSetModel> catset = <CatSetModel>[];
  // List<SettingsModel> setting = <SettingsModel>[];
  SettingsModel? settings;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? catstr;
  // StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? setstr;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? setstr;
  @override
  void onInit() {
    super.onInit();
    setupAuthStream();
    flavourStream();
    productStream();
    weightrangeStream();
    designCostStream();
    mainCategoryStream();

    settingStream();
  }

  setupAuthStream() async {
    try {
      FBAuth.auth.authStateChanges().listen((user) async {
        await Future.delayed(const Duration(milliseconds: 500));
        if (user != null) {
          userStream();
        }
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  userStream() async {
    try {
      userStr?.cancel();
      userStr = FBFireStore.users
          .doc(FBAuth.auth.currentUser?.uid)
          .snapshots()
          .listen((event) {
            if (event.exists) {
              // print("...............userstream up......");

              try {
                userDetails = Userdetails.fromSnapshot(event);
                userDetails?.address.sort((a, b) => a.docId.compareTo(b.docId));
              } on Exception catch (e) {
                print(e.toString());
              }
              // print("...........userstream........down.");
            } else {
              userDetails = null;
            }
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  productStream() async {
    try {
      productStr?.cancel();
      productStr = FBFireStore.product
          .limit(10)
          .where('notavailable', isEqualTo: false)
          .snapshots()
          .listen((event) {
            print(event.docs.map((e) => e.id));
            products = event.docs
                .map((e) => ProductModel.fromSnapshot(e))
                .toList();
            print(products.length);

            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  weightrangeStream() async {
    try {
      weightStr?.cancel();
      weightStr = FirebaseFirestore.instance
          .collection('weightrange')
          // .where('isAdmin', isEqualTo: true)
          // .orderBy('createdAt', descending: true)
          .snapshots()
          .listen((event) {
            weightRange = event.docs
                .map((e) => WeightRangeModel.fromSnapshot(e))
                .toList();
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  designCostStream() async {
    try {
      designStr?.cancel();
      designStr = FirebaseFirestore.instance
          .collection('designcost')
          // .where('isAdmin', isEqualTo: true)
          // .orderBy('createdAt', descending: true)
          .snapshots()
          .listen((event) {
            // print(event.size);
            designCost = event.docs
                .map((e) => DesignCostModel.fromSnapshot(e))
                .toList();
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  flavourStream() async {
    try {
      flavourStr?.cancel();
      flavourStr = FirebaseFirestore.instance
          .collection('flavour')
          // .where('isAdmin', isEqualTo: true)
          // .orderBy('createdAt', descending: true)
          .snapshots()
          .listen((event) {
            // print(event.size);
            flavours = event.docs
                .map((doc) => FlavourModel.fromSnapshot(doc))
                .toList();
            update();
            print(flavours.length);
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  mainCategoryStream() {
    try {
      catstr?.cancel();
      FBFireStore.mainCategory.snapshots().listen((event) {
        maincategory = event.docs
            .map((e) => MainCategoryModel.fromSnapshot(e))
            .toList();

        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  // categorySetStream() {
  //   try {
  //     FBFireStore.categorySet.snapshots().listen((event) {
  //       catset = event.docs.map((e) => CatSetModel.fromSnapshot(e)).toList();
  //     });
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }

  settingStream() {
    try {
      setstr?.cancel();
      // FBFireStore.setting.snapshots().listen((event) {
      //   setting = event.docs.map((e) => SettingsModel.fromSnap(e)).toList();
      // });
      setstr = FBFireStore.setting.doc('sets').snapshots().listen((event) {
        settings = SettingsModel.fromSnap(event);
        for (CatSetModel element in settings?.catset ?? []) {
          subCategoriesList.addAll(element.subCats.map((e) => e).toList());
        }
        // var x=settings?.catset.map((e) => e.subCats.map((e) => e)).toList();
        // print(subCategoriesList);
      });
      update();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  //   onPaymentSuccess(
  //       final String orderDocId, PaymentSuccessResponse? response) async {
  //     try {
  //       // return;
  //       final orderDocRef = FBFireStore.order.doc(orderDocId);
  //       final setsDocRef = FBFireStore.setting.doc('sets');
  //       await FBFireStore.fb.runTransaction((transaction) async {
  //         final orderDoc = await transaction.get(orderDocRef);
  //         final orderData = OrderModel.fromJson(orderDoc.id, orderDoc.data()!);

  //         final setsDoc = await transaction.get(setsDocRef);
  //         final newOrderNo = setsDoc['orderNo'] + 1;

  //         List<PendingTransaction> pendingTrans = <PendingTransaction>[];

  //         for (var pTrans in pendingTrans) {
  //           transaction.update(pTrans.documentReference, pTrans.data);
  //         }
  //         transaction.update(setsDoc.reference, {'orderNo': newOrderNo});
  //         // Update Order Data
  //         orderData.orderNo = newOrderNo;

  //         orderData.isPaid = true;

  //         orderData.transId = response?.paymentId ?? "";

  //         transaction.update(orderDocRef, orderData.toJson());
  //         userDetails?.cartitems.clear();
  //         userDetails?.cartitems.clear();
  //         await FBFunctions.ff.httpsCallable('orderPlacedEmailSMS').call(
  //           {
  //             'email': orderData.email,
  //             'phone': orderData.phone,
  //             'name':
  //                 "${orderData.name?.split(" ").first} ${orderData.name?.split(" ").last}",
  //             'orderNo': orderData.orderNo.toString().padLeft(4, "0"),
  //           },
  //         );
  //       });
  //       goToOrders();
  //     } catch (e) {
  //       debugPrint(e.toString());
  //     }
  //   }

  //   goToOrders() {
  //     try {
  //       appRouter.configuration.navigatorKey.currentContext!.go(Routes.account);
  //     } catch (e) {
  //       debugPrint(e.toString());
  //     }
  //   }
}

// class PendingTransaction {
//   final DocumentReference documentReference;
//   final Map<String, dynamic> data;

//   PendingTransaction({required this.documentReference, required this.data});
// }

//   orderstream() async {
//     try {
//       orderStr?.cancel();
//       orderStr = FirebaseFirestore.instance
//           .collection('order')
//           .snapshots()
//           .listen((event) {
//         orders = event.docs.map((e) => OrderModel.fromSnapshot(e)).toList();
//         update();
//       });
//     } catch (e) {
//       debugPrint(e.toString());
//     }
//   }


  // setupAuthStream() async {
  //   try {
  //     FBAuth.auth.authStateChanges().listen((user) {
  //       // print(user);
  //       // if (user != null)
  //       userStream(FBAuth.auth.currentUser?.uid);

  //       // else
  //       //   userStr?.cancel();
  //     });
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }

  // // getUserData() {
  // //   if (isLoggedIn()) {
  // //     userStream();
  // //   } else {
  // //     userDetails = null;
  // //   }
  // // }
  // userStream(String? uid) async {
  //   try {
  //     userStr?.cancel();
  //     userStr = FBFireStore.users.doc(uid).snapshots().listen((event) {
  //       print("exists -=-=${event.exists}");
  //       print("event-----${event.data()}");
  //       // print('wefbnwhoeiuf $uid');
  //       if (event.exists) {
  //         userDetails = Userdetails.fromSnapshot(event);
  //         update();

  //         // print(
  //         //     'werofib.................................................................................$userDetails');
  //       } else {
  //         userDetails = null;
  //       }
  //       // print("uSer details skrdgrdnmsrlg............................ ");
  //     });
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }