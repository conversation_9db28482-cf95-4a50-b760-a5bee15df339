// import 'dart:async';

// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:the_bakers_bar_user/Models/maincategorymodel.dart';
// import 'package:the_bakers_bar_user/Models/settings.dart';

// import '../shared/firebase.dart';

// class CategoryCtrl extends GetxController {

//   @override
//   void onInit() {
//     super.onInit();
//     mainCategoryStream();
//     //  categorySetStream();
//     settingStream();
//   }

//   mainCategoryStream() {
//     try {
//       catstr?.cancel();
//       FBFireStore.mainCategory.snapshots().listen((event) {
//         maincategory =
//             event.docs.map((e) => MainCategoryModel.fromSnapshot(e)).toList();

//         update();
//       });
//     } catch (e) {
//       debugPrint(e.toString());
//     }
//   }

//   // categorySetStream() {
//   //   try {
//   //     FBFireStore.categorySet.snapshots().listen((event) {
//   //       catset = event.docs.map((e) => CatSetModel.fromSnapshot(e)).toList();
//   //     });
//   //   } catch (e) {
//   //     debugPrint(e.toString());
//   //   }
//   // }

//   settingStream() {
//     try {
//       setstr?.cancel();
//       // FBFireStore.setting.snapshots().listen((event) {
//       //   setting = event.docs.map((e) => SettingsModel.fromSnap(e)).toList();
//       // });
//       setstr = FBFireStore.setting.doc('sets').snapshots().listen((event) {
//         settings = SettingsModel.fromSnap(event);
//       });
//       update();
//     } catch (e) {
//       debugPrint(e.toString());
//     }
//   }
// }
