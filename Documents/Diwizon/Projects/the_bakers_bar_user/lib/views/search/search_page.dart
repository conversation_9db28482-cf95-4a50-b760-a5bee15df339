import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:the_bakers_bar_user/Models/productmodel.dart';
import 'package:the_bakers_bar_user/shared/const.dart';
import 'package:the_bakers_bar_user/shared/firebase.dart';
import 'package:the_bakers_bar_user/views/SubCat_page/subcat_Products.dart';
import 'package:the_bakers_bar_user/views/SubCat_page/subcat_banner.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';
import 'package:the_bakers_bar_user/wrapper.dart';

Future<List<ProductModel>> fromSnapshot(
  List<DocumentSnapshot<Map<String, dynamic>>> value,
) async {
  // Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;

  return value
      .map(
        (data) => ProductModel(
          docId: data.id,
          name: data['name'],
          type: data['type'],
          tags: List<String>.from(data['tags']),
          titleTag: List<String>.from(data['titleTag'] ?? []),
          lower: data['lower'],
          // isdessert: data['isdessert'],
          // isflower: data['isflower'],
          fixedprice: data['fixedprice'],
          flavour: List<String>.from(data['flavour'] ?? []),
          createdAt: (data['createdAt'] as Timestamp).toDate(),
          sku: data['sku'],
          weightRangeDocId: data['weightRangeDocId'],
          images: List<String>.from(data['images']),
          desc: data['desc'],
          notavailable: data['notavailable'],
          minOrderTime: data['minOrderTime'],
          designCostdocId: data['designCostdocId'],
          tier: data['tier'] ?? false,
          hyperlink: data['hyperlink'],
        ),
      )
      .toList();
}

const onePageCount = 24;

class SearchPage extends StatefulWidget {
  const SearchPage({super.key, required this.search});
  final String search;

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  List<String> namelist = [];
  List<String> combi = [];
  List<ProductModel> products = [];
  List<ProductModel> allproducts = [];
  bool loaded = false;
  String? previousName;
  num totalCount = 0;
  final NumberPaginatorController paginatorController =
      NumberPaginatorController();
  int _currentPage = 0;
  int pageNum = 0;
  final ScrollController scrolCtrl = ScrollController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    namelist = splitString(widget.search.toLowerCase().trim());
    namelist.remove("cakes");
    namelist.remove("cake");
    namelist.removeWhere((element) => element == "");
    previousName = widget.search;
    fetch();
  }

  fetch() async {
    allproducts.clear();
    loaded = true;
    if (mounted) setState(() {});

    List<String> matchedCat = [];
    matchedCat.addAll(
      categories
          .where(
            (element) =>
                (namelist.contains(element.toLowerCase()) ||
                widget.search.toLowerCase().contains(element.toLowerCase())),
          )
          .toList(),
    );

    if (widget.search.length > 3) {
      allproducts.addAll(
        await FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where(
              Filter.and(
                Filter(
                  'lower',
                  isGreaterThanOrEqualTo: widget.search.toLowerCase(),
                ),
                Filter(
                  'lower',
                  isLessThanOrEqualTo: '${widget.search.toLowerCase()}\uf7ff',
                ),
              ),
            )
            // .where('lower', arrayContains: widget.search.toLowerCase())
            // .orderBy('showFirst', descending: true)
            .get()
            .then((value) async {
              List<DocumentSnapshot<Map<String, dynamic>>> val = [];
              for (var doc in value.docs) {
                val.addIf(
                  allproducts
                      .where((element) => element.docId.contains(doc.id))
                      .isEmpty,
                  doc,
                );
              }

              return await compute(fromSnapshot, val);
            }),
      );
      if (allproducts.isEmpty) {
        allproducts.addAll(
          await FBFireStore.product
              .where('notavailable', isEqualTo: false)
              .where('titleTag', isEqualTo: namelist)
              // .orderBy('showFirst', descending: true)
              .get()
              .then((value) async {
                List<DocumentSnapshot<Map<String, dynamic>>> val = [];
                for (var doc in value.docs) {
                  val.addIf(
                    allproducts
                        .where((element) => element.docId.contains(doc.id))
                        .isEmpty,
                    doc,
                  );
                }

                return await compute(fromSnapshot, val);
              }),
        );
      }
    }

    if (allproducts.length < 20) {
      if (matchedCat.isNotEmpty) {
        final data = await FBFireStore.product
            .where('notavailable', isEqualTo: false)
            .where(
              'catNameList',
              arrayContainsAny: matchedCat.map((e) => e.toLowerCase()).toList(),
            )
            // .orderBy('showFirst', descending: true)
            .get();

        allproducts = await compute(fromSnapshot, data.docs);
      }
    }
    if (namelist.isNotEmpty) {
      if (allproducts.length < 20) {
        allproducts.addAll(
          await FBFireStore.product
              .where('notavailable', isEqualTo: false)
              .where('tags', arrayContainsAny: namelist)
              // .orderBy('showFirst', descending: true)
              .get()
              .then((value) async {
                List<DocumentSnapshot<Map<String, dynamic>>> val = [];
                for (var doc in value.docs) {
                  val.addIf(
                    allproducts
                        .where((element) => element.docId.contains(doc.id))
                        .isEmpty,
                    doc,
                  );
                }

                return await compute(fromSnapshot, val);
              }),
        );
      }
    }
    allproducts.sort((a, b) {
      // Determine if 'a' matches any element in the 'namelist'
      final bool aMatches =
          a.titleTag?.any((element) => namelist.contains(element)) ?? false;
      // Determine if 'b' matches any element in the 'namelist'
      final bool bMatches =
          b.titleTag?.any((element) => namelist.contains(element)) ?? false;

      // If both match or both don't match, return 0 (no change in order)
      if (aMatches == bMatches) {
        return 0;
      }

      // If 'a' matches and 'b' doesn't, return -1 to prioritize 'a'
      // If 'b' matches and 'a' doesn't, return 1 to prioritize 'b'
      return aMatches ? -1 : 1;
    });
    loaded = false;

    if (mounted) setState(() {});
  }

  List<String> splitString(String myString) {
    List<String> splittedString = myString
        .split(" ")
        .map((str) => str.trim())
        .toList();
    splittedString.remove("cakes");

    return splittedString;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.search != previousName) {
      _currentPage = 0;
      namelist = splitString(widget.search.toLowerCase().trim());
      namelist.remove("cakes");
      namelist.remove("cake");
      namelist.removeWhere((element) => element == "");
      previousName = widget.search;

      fetch();
    }

    final noofPages = (allproducts.length / onePageCount).ceil();

    List<ProductModel> subList = allproducts.isEmpty
        ? allproducts
        : allproducts.length <= onePageCount
        ? allproducts.getRange(0, allproducts.length).toList()
        : allproducts
              .getRange(
                (_currentPage * onePageCount),
                _currentPage == (noofPages - 1)
                    ? allproducts.length
                    : (_currentPage * onePageCount) + onePageCount,
              )
              .toList();

    final size = MediaQuery.sizeOf(context);
    return Wrapper(
      scrollController: scrolCtrl,
      body: loaded
          ? const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 100.0),
                child: Text(
                  "Loading...",
                  style: TextStyle(
                    fontSize: 24,
                    color: Color.fromARGB(255, 217, 137, 132),
                  ),
                ),
              ),
            )
          : ResponsiveWid(
              mobile: subList.isEmpty
                  ? Column(
                      children: [
                        SubCatBanner(name: widget.search),
                        const SizedBox(height: 65),
                        Padding(
                          padding: EdgeInsets.only(
                            top: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                ? 130
                                : 170,
                            bottom: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                ? 130
                                : 160,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "No Product Available!",
                                style: TextStyle(
                                  fontSize: size.width <= 510 ? 18 : 28,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 65),
                      ],
                    )
                  : Column(
                      children: [
                        SubCatBanner(name: widget.search),
                        const SizedBox(height: 20),
                        SubcatProducts(
                          // scrolCtrl: scrolCtrl,
                          productlist: subList,
                        ),
                        subList.isEmpty
                            ? const SizedBox()
                            : Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20.0,
                                  vertical: 10,
                                ),
                                child: NumberPaginator(
                                  onPageChange: (p0) {
                                    _currentPage = p0;
                                    if (mounted) setState(() {});
                                  },
                                  prevButtonBuilder: (context) {
                                    return IconButton(
                                      onPressed: () {
                                        if (_currentPage > 0) {
                                          paginatorController.prev();
                                        }
                                        if (mounted) setState(() {});
                                      },
                                      icon: const Icon(
                                        CupertinoIcons.chevron_back,
                                      ),
                                    );
                                  },
                                  nextButtonBuilder: (context) {
                                    return IconButton(
                                      onPressed: () {
                                        if (_currentPage != noofPages - 1) {
                                          paginatorController.next();
                                        }
                                        if (mounted) setState(() {});
                                      },
                                      icon: const Icon(
                                        CupertinoIcons.chevron_forward,
                                      ),
                                    );
                                  },
                                  controller: paginatorController,
                                  numberPages: noofPages,
                                ),
                              ),
                      ],
                    ),

              //tablet
              tablet: subList.isEmpty
                  ? Column(
                      children: [
                        SubCatBanner(name: widget.search),
                        const SizedBox(height: 105),
                        Padding(
                          padding: EdgeInsets.only(
                            top: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                ? 180
                                : 170,
                            bottom: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                ? 180
                                : 160,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "No Product Available!",
                                style: TextStyle(
                                  fontSize: size.width <= 510 ? 18 : 28,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 105),
                      ],
                    )
                  : Column(
                      children: [
                        SubCatBanner(name: widget.search),
                        SubcatProducts(
                          // scrolCtrl: scrolCtrl,
                          productlist: subList,
                        ),
                        subList.isEmpty
                            ? const SizedBox()
                            : Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 50.0,
                                  vertical: 10,
                                ),
                                child: NumberPaginator(
                                  onPageChange: (p0) {
                                    _currentPage = p0;

                                    if (mounted) setState(() {});
                                  },
                                  prevButtonBuilder: (context) {
                                    return IconButton(
                                      onPressed: () {
                                        if (_currentPage > 0) {
                                          paginatorController.prev();
                                        }
                                        if (mounted) setState(() {});
                                      },
                                      icon: const Icon(
                                        CupertinoIcons.chevron_back,
                                      ),
                                    );
                                  },
                                  nextButtonBuilder: (context) {
                                    return IconButton(
                                      onPressed: () {
                                        if (_currentPage != noofPages - 1) {
                                          paginatorController.next();
                                        }
                                        if (mounted) setState(() {});
                                      },
                                      icon: const Icon(
                                        CupertinoIcons.chevron_forward,
                                      ),
                                    );
                                  },
                                  controller: paginatorController,
                                  numberPages: noofPages,
                                ),
                              ),
                        const SizedBox(height: 30),
                      ],
                    ),

              //desktop
              desktop: subList.isEmpty
                  ? Column(
                      children: [
                        SubCatBanner(name: widget.search),
                        const SizedBox(height: 55),
                        Padding(
                          padding: EdgeInsets.only(
                            top: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                ? 130
                                : 170,
                            bottom: size.width <= 510
                                ? 100
                                : size.width <= 1400
                                ? 130
                                : 160,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "No Product Available!",
                                style: TextStyle(
                                  fontSize: size.width <= 510 ? 18 : 28,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 55),
                      ],
                    )
                  : Column(
                      children: [
                        SubCatBanner(name: widget.search),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 4,
                              child: SubcatProducts(
                                // scrolCtrl: scrolCtrl,
                                productlist: subList,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 30),
                        if (allproducts.length > onePageCount)
                          allproducts.isEmpty
                              ? const SizedBox()
                              : Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 50.0,
                                    vertical: 10,
                                  ),
                                  child: NumberPaginator(
                                    onPageChange: (p0) {
                                      _currentPage = p0;
                                      scrolCtrl.jumpTo(0);
                                      if (mounted) setState(() {});
                                    },
                                    prevButtonBuilder: (context) {
                                      return IconButton(
                                        onPressed: () {
                                          if (_currentPage > 0) {
                                            paginatorController.prev();
                                          }
                                          scrolCtrl.jumpTo(0);
                                          if (mounted) setState(() {});
                                        },
                                        icon: const Icon(
                                          CupertinoIcons.chevron_back,
                                        ),
                                      );
                                    },
                                    nextButtonBuilder: (context) {
                                      return IconButton(
                                        onPressed: () {
                                          if (_currentPage != noofPages - 1) {
                                            paginatorController.next();
                                          }
                                          scrolCtrl.jumpTo(0);
                                          if (mounted) setState(() {});
                                        },
                                        icon: const Icon(
                                          CupertinoIcons.chevron_forward,
                                        ),
                                      );
                                    },
                                    controller: paginatorController,
                                    numberPages: noofPages,
                                  ),
                                ),
                      ],
                    ),
            ),
    );
  }
}
