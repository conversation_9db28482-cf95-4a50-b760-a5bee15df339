import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/firebase.dart';
import 'package:the_bakers_bar_user/shared/methods.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/wrapper.dart';

final _authScafKey = GlobalKey<ScaffoldState>();

class AuthPage extends StatefulWidget {
  const AuthPage({super.key, this.goTo});

  final String? goTo;

  @override
  State<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage>
    with SingleTickerProviderStateMixin {
  bool loading = false;
  late TabController tabCtrl;
  final mobileCtrl = TextEditingController();
  final emailCtrl = TextEditingController();
  final mobileFormKey = GlobalKey<FormState>();
  final emailFormKey = GlobalKey<FormState>();
  final otpFormKey = GlobalKey<FormState>();
  final otpCtrl = TextEditingController();
  ConfirmationResult? confirmationResult;
  int? newUserEmailOtp;
  DateTime? newUserEmailOtpSentOn;

  @override
  void initState() {
    super.initState();
    tabCtrl = TabController(vsync: this, length: 2);
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      showBackToTop: false,
      scafKey: _authScafKey,
      body: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(20.0),
        child: isLoggedIn()
            ? const Text("Already logged in!")
            : Padding(
                padding: const EdgeInsets.symmetric(vertical: 105.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 400),
                        child: Column(
                          children: [
                            const SizedBox(height: 20),
                            _loginText(),
                            const SizedBox(height: 20),
                            _desc(),
                            const SizedBox(height: 12),
                            const Divider(),
                            const SizedBox(height: 12),
                            _tabBar(),
                            const SizedBox(height: 40),
                            _formWids[tabCtrl.index],
                            const SizedBox(height: 32),
                            if (tabCtrl.index == 0 &&
                                confirmationResult != null)
                              _otpInputField(),
                            if (tabCtrl.index != 0 && newUserEmailOtp != null)
                              _otpInputField(),
                            _submitButton(),
                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Column _otpInputField() {
    return Column(
      children: [
        Form(
          key: otpFormKey,
          child: TextFormField(
            keyboardType: TextInputType.number,
            controller: otpCtrl,
            validator: (value) => value!.isNumericOnly && value.length == 10
                ? null
                : "Invalid OTP!",
            onFieldSubmitted: (value) => tabCtrl.index == 0
                ? _confirmOTP(otpCtrl.text)
                : _confirmEmailOTP(otpCtrl.text),
            decoration: InputDecoration(
              prefixIcon: const Padding(
                padding: EdgeInsets.only(left: 12.0, right: 8),
                child: Icon(Icons.password_rounded),
              ),
              hintText: "One Time Password",
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  String verificationCode = "";
  int? resendToken;
  _onSubmit() async {
    try {
      if (loading) return;
      if (tabCtrl.index == 0) {
        if (mobileFormKey.currentState!.validate()) {
          setState(() => loading = true);
          confirmationResult = await FBAuth.auth.signInWithPhoneNumber(
            '+91${mobileCtrl.text}',
          );
          showAppSnack("OTP Sent!");
        }
        setState(() => loading = false);
      } else {
        _authWithEmail();
      }
    } catch (e) {
      setState(() => loading = false);
      debugPrint(e.toString());
    }
  }

  _authWithEmail() async {
    try {
      if (emailFormKey.currentState!.validate()) {
        setState(() => loading = true);
        final otp = Random().nextInt(999999);
        final res = await FBFireStore.users
            .where('authEmail', isEqualTo: emailCtrl.text)
            .get();
        if (res.docs.isNotEmpty) {
          // // Document Exist
          debugPrint("Old User");
          // Save Otp
          newUserEmailOtp = otp;
          newUserEmailOtpSentOn = DateTime.now();
          await FBFireStore.users.doc(res.docs.first.id).update({
            'otp': otp,
            'otpTime': FieldValue.serverTimestamp(),
          });
          // Send OTP Email
          await FBFunctions.ff.httpsCallable('sendOtpEmail').call({
            'email': emailCtrl.text,
            'otp': otp,
          });
          showAppSnack("OTP Sent!");
        } else {
          // // New User
          debugPrint("New User");
          newUserEmailOtp = otp;
          newUserEmailOtpSentOn = DateTime.now();
          await FBFunctions.ff.httpsCallable('sendOtpEmail').call({
            'email': emailCtrl.text,
            'otp': otp,
          });
          showAppSnack("OTP Sent!");
        }
      }
      setState(() => loading = false);
    } catch (e) {
      setState(() => loading = false);
      debugPrint(e.toString());
    }
  }

  _confirmEmailOTP(String otp) async {
    try {
      if (loading) return;
      setState(() => loading = true);
      final res = await FBFireStore.users
          .where('authEmail', isEqualTo: emailCtrl.text)
          .get();
      if (res.docs.isNotEmpty) {
        // // Doc Found
        debugPrint("Old User");
        final userDocData = res.docs.first;
        if (userDocData['otp'].toString() == otp &&
            (userDocData['otpTime']
                    ?.toDate()
                    .add(const Duration(minutes: 5))
                    .isAfter(DateTime.now()) ??
                false)) {
          await FBAuth.auth.signInWithEmailAndPassword(
            email: emailCtrl.text,
            password: res.docs.first['password'],
          );
          //
          if (context.mounted) {
            context.go(
              widget.goTo != null
                  ? widget.goTo!
                  : routeHistory.reversed.elementAt(1),
            );
          }
        }
      } else {
        // // New User
        debugPrint("New User");
        if (otp == newUserEmailOtp.toString() &&
            (newUserEmailOtpSentOn
                    ?.add(const Duration(minutes: 5))
                    .isAfter(DateTime.now()) ??
                false)) {
          final newPass = getRandomId(8);
          final newUserCred = await FBAuth.auth.createUserWithEmailAndPassword(
            email: emailCtrl.text,
            password: newPass,
          );
          var data = {
            'phone': "",
            'email': emailCtrl.text,
            'firstOrderDone': false,
            'authEmail': emailCtrl.text,
            'defaultAddressId': null,
            'addresses': {},
            'cartData': {},
            'password': newPass,
          };
          await FBFireStore.users.doc(newUserCred.user?.uid).set(data);
          //
          if (context.mounted) {
            context.go(
              widget.goTo != null
                  ? widget.goTo!
                  : routeHistory.reversed.elementAt(1),
            );
          }
        } else {
          showAppSnack("OTP Invalid or Expired!");
        }
      }
      setState(() => loading = false);
    } on FirebaseAuthException catch (e) {
      debugPrint(e.toString());
      debugPrint(e.code.toString());
      if (e.code == 'invalid-verification-code') {
        showAppSnack("Invalid OTP!");
      } else {
        showAppSnack(e.message?.toString() ?? "Something went wrong!");
      }
      setState(() => loading = false);
    } catch (e) {
      debugPrint(e.toString());
      setState(() => loading = false);
    }
  }

  _confirmOTP(String otp) async {
    bool isnew = true;
    // bool loaded = false;
    try {
      if (loading) return;
      setState(() => loading = true);
      UserCredential? userCredential = await confirmationResult?.confirm(otp);
      if (userCredential != null && context.mounted) {
        FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).snapshots().listen((
          event,
        ) {
          isnew = !event.exists;
          // loaded=true
          // print(isnew);
        });

        await Future.delayed(const Duration(seconds: 5));
        // if (loaded) {}
        if (context.mounted) {
          if (!isnew) {
            var cartdata = <String, dynamic>{};
            final temp = Get.find<ProductCtrl>().orderProduct;
            for (var e in temp) {
              final cartitem = <String, dynamic>{
                e.id: {
                  "pId": e.pId,
                  "name": e.name,
                  "selectedflavour": e.selectedflavour,
                  "weightSelected": e.weightSelected,
                  "deliveryTime": e.deliveryTime,
                  "delivery": e.delivery,
                  "price": e.price,
                  "messageOnCake": e.messageOnCake,
                  // "area": e.area,
                  "delDate": deldatefromat(e.delDate),
                  "isLateNight": e.isLateNight,
                  'qty': e.qty,
                },
              };
              cartdata.addAll(cartitem);
            }
            DocumentSnapshot documentSnapshot = await FBFireStore.users
                .doc(FBAuth.auth.currentUser?.uid)
                .get();
            if (documentSnapshot.exists) {
              Map<String, dynamic> tempMap = documentSnapshot.get('cartitems');

              tempMap.removeWhere((key, value) {
                String dateString = value['delDate'];
                DateTime parsedDate = DateTime.parse(dateString);

                return DateTime(
                      DateTime.now().year,
                      DateTime.now().month,
                      DateTime.now().day,
                      0,
                      0,
                    ).compareTo(parsedDate) ==
                    1;
              });

              cartdata.addAll(tempMap);
            } else {
              // print('Document does not exist.');
            }
            await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
              "cartitems": cartdata,
            });
            Get.find<ProductCtrl>().orderProduct.clear();
          }
          if (context.mounted) {
            context.go(
              widget.goTo != null
                  ? widget.goTo!
                  : isnew
                  ? tabCtrl.index == 0
                        ? '${Routes.signup}/${mobileCtrl.text}'
                        : '${Routes.signup}/${emailCtrl.text}'
                  : (Get.find<ProductCtrl>().userDetails?.cartitems.isEmpty ??
                        true)
                  ? Routes.home
                  : Routes.checkout,
            );
          } //routeHistory.reversed.elementAt(1));
        }
      }
      setState(() => loading = false);
    } on FirebaseAuthException catch (e) {
      debugPrint(e.toString());
      if (e.code == 'invalid-verification-code') {
        showAppSnack("Invalid OTP!");
      } else {
        showAppSnack(e.message?.toString() ?? "Something went wrong!");
      }
      Get.find<ProductCtrl>().setupAuthStream();
      setState(() => loading = false);
    } catch (e) {
      debugPrint(e.toString());
      setState(() => loading = false);
    }
  }

  Text _loginText() {
    return const Text(
      "Login with OTP",
      style: TextStyle(fontSize: 20, fontWeight: FontWeight.w700),
    );
  }

  Text _desc() {
    return const Text(
      "Please login to access your saved sizes if you've shopped Online or In Store with us.",
      textAlign: TextAlign.center,
      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
    );
  }

  ElevatedButton _submitButton() {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color.fromARGB(255, 237, 176, 171),
        padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      onPressed: () => tabCtrl.index == 0
          ? confirmationResult == null
                ? _onSubmit()
                : _confirmOTP(otpCtrl.text)
          : newUserEmailOtp == null
          ? _authWithEmail()
          : _confirmEmailOTP(otpCtrl.text),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            tabCtrl.index == 0
                ? confirmationResult == null
                      ? "Send OTP"
                      : "Verify OTP"
                : newUserEmailOtp == null
                ? "Send OTP"
                : "Verify OTP",
            style: const TextStyle(color: Colors.white),
          ),
          if (loading)
            const Padding(
              padding: EdgeInsets.only(left: 12.0),
              child: SizedBox(
                height: 12,
                width: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation(Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<Form> get _formWids {
    return [
      Form(
        key: mobileFormKey,
        child: TextFormField(
          keyboardType: TextInputType.phone,
          controller: mobileCtrl,
          validator: (value) => value!.isNumericOnly && value.length == 10
              ? null
              : "Require a valid number!",
          onFieldSubmitted: (value) => _onSubmit(),
          decoration: InputDecoration(
            prefixIcon: const Padding(
              padding: EdgeInsets.only(left: 12.0, right: 8),
              child: Icon(CupertinoIcons.phone_fill),
            ),
            prefixText: "+91",
            hintText: "Mobile Number",
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
      Form(
        key: emailFormKey,
        child: TextFormField(
          controller: emailCtrl,
          validator: (value) =>
              value!.isEmail ? null : "Require a valid email!",
          decoration: InputDecoration(
            prefixIcon: const Padding(
              padding: EdgeInsets.only(left: 12.0, right: 8),
              child: Icon(CupertinoIcons.mail_solid),
            ),
            hintText: "<EMAIL>",
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    ];
  }

  Widget _tabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        splashBorderRadius: BorderRadius.circular(8),
        indicatorPadding: EdgeInsets.zero,
        padding: EdgeInsets.zero,
        labelPadding: EdgeInsets.zero,
        controller: tabCtrl,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: const Color.fromARGB(255, 237, 176, 171),
        ),
        onTap: (v) => setState(() {}),
        dividerColor: Colors.transparent,
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Mobile No.",
                  style: TextStyle(
                    color: tabCtrl.index == 0 ? Colors.white : Colors.black,
                  ),
                ),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "E-mail",
                  style: TextStyle(
                    color: tabCtrl.index == 1 ? Colors.white : Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
