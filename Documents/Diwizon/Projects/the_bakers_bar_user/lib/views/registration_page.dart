import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/views/login_page/login_page.dart';
import 'package:the_bakers_bar_user/wrapper.dart';

class RegistrationPage extends StatelessWidget {
  const RegistrationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      body: SizedBox(
        height: 600,
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 500),
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CreateAcctitle(),
              FirstName(),
              LastName(),
              CreateAccEmail(),
              <PERSON>reateAccPass(),
              CreateAccbutton(),
            ],
          ),
        ),
      ),
    );
  }
}

class CreateAccPass extends StatelessWidget {
  const CreateAccPass({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 10.0,
        top: 10,
        left: 50,
        right: 50,
      ),
      child: Container(
        width: 600,
        decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: const BorderRadius.all(Radius.zero),
        ),
        child: const Row(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  left: 10.0,
                  right: 2,
                  top: 2,
                  bottom: 2,
                ),
                child: TextField(
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    labelText: "Password",
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CreateAccEmail extends StatelessWidget {
  const CreateAccEmail({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 10.0,
        top: 10,
        left: 50,
        right: 50,
      ),
      child: Container(
        width: 600,
        decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: const BorderRadius.all(Radius.zero),
        ),
        child: const Row(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  left: 10.0,
                  right: 2,
                  top: 2,
                  bottom: 2,
                ),
                child: TextField(
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    labelText: "E-mail",
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LastName extends StatelessWidget {
  const LastName({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 10.0,
        top: 10,
        left: 50,
        right: 50,
      ),
      child: Container(
        // width: MediaQuery.sizeOf(context).width,
        width: 600,
        decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: const BorderRadius.all(Radius.zero),
        ),
        child: const Row(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  left: 10.0,
                  right: 2,
                  top: 2,
                  bottom: 2,
                ),
                child: TextField(
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    labelText: "LAST NAME",
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FirstName extends StatelessWidget {
  const FirstName({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 10.0,
        top: 50,
        left: 50,
        right: 50,
      ),
      child: Container(
        // width: MediaQuery.sizeOf(context).width,
        width: 600,
        decoration: BoxDecoration(
          border: Border.all(),
          borderRadius: const BorderRadius.all(Radius.zero),
        ),
        child: const Row(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  left: 10.0,
                  right: 2,
                  top: 2,
                  bottom: 2,
                ),
                child: TextField(
                  decoration: InputDecoration(
                    focusedBorder: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    labelText: "FIRST NAME",
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CreateAcctitle extends StatelessWidget {
  const CreateAcctitle({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      // width: 500,
      alignment: Alignment.center,
      child: Text(
        "CREATE ACCOUNT",
        style: GoogleFonts.roboto(
          color: Colors.black,
          fontSize: 40,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }
}
