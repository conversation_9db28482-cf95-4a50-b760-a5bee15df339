import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/Models/addressmodel.dart';
import 'package:the_bakers_bar_user/Models/cart_items.dart';
import 'package:the_bakers_bar_user/Models/user_details.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';
import 'checkout_items_col.dart';

class CheckOutShipping extends StatefulWidget {
  const CheckOutShipping({
    super.key,
    required this.id,
    required this.instructions,
  });
  final String id;
  final String instructions;
  @override
  State<CheckOutShipping> createState() => _CheckOutShippingState();
}

class _CheckOutShippingState extends State<CheckOutShipping> {
  bool loading = false;
  Userdetails? userdetails;
  AddressModel? addressDetail;
  int shippingPrice = 0;
  final List<String> pricechargelist = ["A"];
  String? selectedprice = 'A';
  num shippinareaperice = 0;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<ProductCtrl>(
      builder: (pctrl) {
        // shippingCalc();
        addressDetail = pctrl.userDetails?.address.firstWhere(
          (element) => element.docId == widget.id,
        );
        final selectedareDetail = pctrl.settings?.delcharges.firstWhereOrNull((
          element,
        ) {
          print(
            "-------${element.name.toLowerCase().trim()}--${addressDetail?.area.toLowerCase().trim()}-",
          );
          return element.name.toLowerCase().trim() ==
              addressDetail?.area.toLowerCase().trim();
        });
        shippinareaperice = selectedareDetail?.price ?? 0;

        final x = (1920 - size.width) / 2;
        List<CartItemsModel> test = [];
        test.addAll(Get.find<ProductCtrl>().userDetails?.cartitems ?? []);
        List<CartItem> cartItems = [];
        for (int i = 0; i < test.length; i++) {
          if (test[i].deliveryTime != "null-null") {
            cartItems.add(
              CartItem(
                (i + 1).toString(),
                'Product$i',
                DateTime(
                  test[i].delDate.year,
                  test[i].delDate.month,
                  test[i].delDate.day,
                ),
                test[i].deliveryTime,
                test[i].delivery,
              ),
            );
          }
        } // Standard Delivery
        // print(cartItems.first.product);

        DeliveryPricing pricing = DeliveryPricing(
          areaPrice: shippinareaperice.toInt(),
        );
        shippingPrice = pricing.calculateShippingPrice(cartItems);

        userdetails = pctrl.userDetails;
        return Scaffold(
          body: loading
              ? const CircularProgressIndicator()
              : Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            SizedBox(
                              height: size.width <= 510 ? 100 : 200,
                              width: double.infinity,
                              child: Stack(
                                fit: StackFit.expand,
                                children: [
                                  Image.asset(
                                    "assets/images/common-banner 3.png",
                                    fit: BoxFit.fill,
                                  ),
                                  Align(
                                    child: Text(
                                      "CHECK OUT SHIPPING",
                                      style: GoogleFonts.crimsonPro(
                                        fontSize: size.width <= 510 ? 24 : 34,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            ResponsiveWid(
                              mobile: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      left: 20,
                                      top: 20,
                                      bottom: 20,
                                      right: 20,
                                    ),
                                    child: shippingCol(context),
                                  ),
                                  ExpansionTile(
                                    initiallyExpanded: true,
                                    title: Row(
                                      children: [
                                        Text(
                                          "Show Order Summary",
                                          style: GoogleFonts.aBeeZee(
                                            fontSize: 12,
                                          ),
                                        ),
                                        // const Icon(Icons.keyboard_arrow_down),
                                      ],
                                    ),
                                    leading: const Icon(
                                      CupertinoIcons.cart,
                                      color: Colors.black,
                                      weight: Checkbox.width,
                                    ),
                                    // trailing: Text(
                                    //   "Rs.",
                                    //   style: GoogleFonts.b612Mono(fontSize: 20),
                                    // ),
                                    children: [
                                      Container(
                                        color: const Color(0xfff5f5f5),
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                            left: 20.0,
                                            top: 20,
                                            bottom: 20,
                                            right: 20,
                                          ),
                                          child: CartItems(
                                            from: "2",
                                            shippingprice: shippingPrice,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              tablet: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(
                                      left: x < 280 ? 300.0 - x : 50,
                                      top: 20,
                                      bottom: 20,
                                      right: 50,
                                    ),
                                    child: shippingCol(context),
                                  ),
                                  ExpansionTile(
                                    initiallyExpanded: true,
                                    title: Row(
                                      children: [
                                        Text(
                                          "Show Order Summary",
                                          style: GoogleFonts.aBeeZee(
                                            fontSize: 20,
                                          ),
                                        ),
                                        const Icon(Icons.keyboard_arrow_down),
                                      ],
                                    ),
                                    leading: const Icon(
                                      CupertinoIcons.cart,
                                      color: Colors.black,
                                      weight: Checkbox.width,
                                    ),
                                    trailing: Text(
                                      "Rs.",
                                      style: GoogleFonts.b612Mono(fontSize: 20),
                                    ),
                                    children: [
                                      Container(
                                        color: const Color(0xfff5f5f5),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                            left: 50.0,
                                            top: 20,
                                            bottom: 20,
                                            right: x < 380 ? 400 - x : 100,
                                          ),
                                          child: CartItems(
                                            from: "2",
                                            shippingprice: shippingPrice,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              desktop: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        left: x < 280 ? 300.0 - x : 50,
                                        top: 20,
                                        bottom: 20,
                                        right: 50,
                                      ),
                                      child: shippingCol(context),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      constraints: const BoxConstraints(
                                        minHeight: 800,
                                      ),
                                      color: const Color(0xfff5f5f5),
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          left: 50.0,
                                          top: 20,
                                          bottom: 20,
                                          right: x < 380 ? 400 - x : 100,
                                        ),
                                        child: CartItems(
                                          from: "2",
                                          shippingprice: shippingPrice,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: size.width >= 1024 ? 15 : 12,
                      ),
                      decoration: const BoxDecoration(
                        color: themeColorLite,
                        boxShadow: [
                          BoxShadow(color: Colors.black12, blurRadius: 6),
                        ],
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          if (size.width >= 1024) const Spacer(flex: 1),
                          InkWell(
                            onTap: () {
                              context.go(Routes.checkout);
                            },
                            child: const Row(
                              children: [
                                Icon(
                                  color: Color(0xffe9867f),
                                  Icons.arrow_back_ios_outlined,
                                  size: 15,
                                ),
                                SizedBox(width: 5),
                                Text(
                                  "Return to Information",
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xffe9867f),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(flex: 2),
                          ElevatedButton(
                            style: ButtonStyle(
                              backgroundColor: const WidgetStatePropertyAll(
                                Color(0xffe9867f),
                              ),
                              padding: const WidgetStatePropertyAll(
                                EdgeInsets.symmetric(
                                  vertical: 20,
                                  horizontal: 20,
                                ),
                              ),
                              shape: WidgetStatePropertyAll(
                                RoundedRectangleBorder(
                                  side: const BorderSide(
                                    color: Color(0xffe9867f),
                                  ),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                              ),
                            ),
                            onPressed: () async {
                              if (userdetails?.cartitems.isEmpty ?? true) {
                                Fluttertoast.showToast(msg: "Cart is Empty!!");
                                return;
                              }
                              context.go(
                                "${Routes.checkoutpayment}/${widget.id}",
                                extra: widget.instructions,
                              );
                            },
                            child: const Text(
                              "Continue Payment",
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                          if (size.width >= 1024) const Spacer(flex: 1),
                        ],
                      ),
                    ),
                  ],
                ),
        );
      },
    );
  }

  Column shippingCol(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            InkWell(
              onTap: () {
                context.go(Routes.cart);
              },
              child: Text(
                "Cart",
                style: TextStyle(
                  color: const Color(0xffe9867f),
                  fontSize: size.width <= 510 ? 12 : 20,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 8),
            InkWell(
              onTap: () {
                context.go(Routes.checkout);
              },
              child: Text(
                "Information",
                style: TextStyle(
                  color: const Color(0xffe9867f),
                  fontSize: size.width <= 510 ? 12 : 20,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 8),
            Text(
              "Shippping",
              style: TextStyle(fontSize: size.width <= 510 ? 12 : 20),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 8),
            InkWell(
              onTap: () {
                if (userdetails?.cartitems.isEmpty ?? true) {
                  Fluttertoast.showToast(msg: "Cart is Empty!!");
                  return;
                }
                context.go(
                  "${Routes.checkoutpayment}/${widget.id}",
                  extra: widget.instructions,
                );
              },
              child: Text(
                "Payment",
                style: TextStyle(
                  color: const Color(0xffe9867f),
                  fontSize: size.width <= 510 ? 12 : 20,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 30),
        Container(
          decoration: BoxDecoration(border: Border.all(color: Colors.grey)),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10.0,
                ),
                child: Row(
                  children: [
                    const Text("Contact"),
                    const SizedBox(width: 50),
                    Text(
                      addressDetail?.phone ?? "",
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: () {
                        context.go(Routes.checkout);
                      },
                      child: const Text(
                        "Change",
                        style: TextStyle(color: Color(0xffe9867f)),
                      ),
                    ),
                  ],
                ),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.0),
                child: Divider(thickness: .3, color: Colors.grey),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10.0,
                ),
                child: Row(
                  children: [
                    const Text("Ship to"),
                    const SizedBox(width: 50),
                    Text(
                      "${addressDetail?.area}, ${addressDetail?.pincode}",
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: () {
                        context.go(Routes.checkout);
                      },
                      child: const Text(
                        "Change",
                        style: TextStyle(color: Color(0xffe9867f)),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 40),
        const Text("Shipping method", style: TextStyle(fontSize: 20)),
        const SizedBox(height: 30),
        Container(
          decoration: BoxDecoration(border: Border.all(color: Colors.grey)),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Radio(
                  fillColor: const WidgetStatePropertyAll(Color(0xffe9867f)),
                  value: pricechargelist[0],
                  groupValue: selectedprice,
                  onChanged: (value) {
                    selectedprice = value;
                  },
                ),
                const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Shipping"),
                    Text(
                      "Special Price Charge",
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                const Spacer(),
                shippingPrice == 0
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: themeColor,
                        ),
                      )
                    : Text("₹$shippingPrice"),
              ],
            ),
          ),
        ),
        const SizedBox(height: 20),
        // Row(
        //   crossAxisAlignment: CrossAxisAlignment.center,
        //   children: [
        //     InkWell(
        //       onTap: () {
        //         context.go(Routes.checkout);
        //       },
        //       child: const Row(
        //         children: [
        //           Icon(
        //             color: Color(0xffe9867f),
        //             Icons.arrow_back_ios_outlined,
        //             size: 15,
        //           ),
        //           SizedBox(width: 5),
        //           Text(
        //             "Return to Information",
        //             style: TextStyle(
        //                 fontWeight: FontWeight.w500, color: Color(0xffe9867f)),
        //           ),
        //         ],
        //       ),
        //     ),
        //     const Spacer(),
        //     ElevatedButton(
        //         style: ButtonStyle(
        //             backgroundColor:
        //                 const WidgetStatePropertyAll(Color(0xffe9867f)),
        //             padding: const WidgetStatePropertyAll(
        //                 EdgeInsets.symmetric(vertical: 20, horizontal: 20)),
        //             shape: WidgetStatePropertyAll(RoundedRectangleBorder(
        //                 side: const BorderSide(color: Color(0xffe9867f)),
        //                 borderRadius: BorderRadius.circular(5)))),
        //         onPressed: () async {
        //           if (userdetails?.cartitems.isEmpty ?? true) {
        //             Fluttertoast.showToast(msg: "Cart is Empty!!");
        //             return;
        //           }
        //           context.go("${Routes.checkoutpayment}/${widget.id}",
        //               extra: widget.instructions);
        //         },
        //         child: const Text(
        //           "Continue Payment",
        //           style: TextStyle(color: Colors.white),
        //         ))
        //   ],
        // ),
        // const SizedBox(height: 40),
        const Divider(color: Color.fromARGB(255, 220, 217, 217)),
        const SizedBox(height: 10),
        Row(
          children: [
            InkWell(
              onTap: () => context.go(Routes.returnandrefundPolicy),
              child: const Text(
                "Refund policy",
                style: TextStyle(color: Color(0xffe9867f)),
              ),
            ),
            const SizedBox(width: 20),
            InkWell(
              onTap: () => context.go(Routes.privacy),
              child: const Text(
                "Privacy policy",
                style: TextStyle(color: Color(0xffe9867f)),
              ),
            ),
            const SizedBox(width: 20),
            InkWell(
              onTap: () => context.go(Routes.terms),
              child: const Text(
                "Terms of service",
                style: TextStyle(color: Color(0xffe9867f)),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Column cartCol(ProductCtrl pctrl) {
  //   getProductPrice();
  //   return Column(
  //     mainAxisAlignment: MainAxisAlignment.start,
  //     children: [
  //       ...List.generate(
  //         pctrl.userDetails?.cartitems.length ?? 0,
  //         (index) {
  //           return Padding(
  //             padding: const EdgeInsets.only(top: 20.0),
  //             child: CheckOutdetails(index: index),
  //           );
  //         },
  //       ),
  //       const SizedBox(height: 20),
  //       Row(
  //         children: [
  //           Expanded(
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 TextField(
  //                     // controller: tagctrl,

  //                     // onChanged: (value) {
  //                     //   tag = value;
  //                     //   descriptionctrl.text = tag;
  //                     // },
  //                     decoration: textfieldDecoration()
  //                         .copyWith(hintText: 'Discount code')),
  //               ],
  //             ),
  //           ),
  //           const SizedBox(width: 20),
  //           ElevatedButton(
  //               style: ButtonStyle(
  //                   backgroundColor: const WidgetStatePropertyAll(
  //                       Color.fromARGB(255, 205, 200, 200)),
  //                   padding: const WidgetStatePropertyAll(
  //                       EdgeInsets.symmetric(vertical: 20, horizontal: 20)),
  //                   shape: WidgetStatePropertyAll(RoundedRectangleBorder(
  //                       side: const BorderSide(
  //                           color: Color.fromARGB(255, 205, 200, 200)),
  //                       borderRadius: BorderRadius.circular(5)))),
  //               onPressed: () {},
  //               child: const Text(
  //                 "Apply",
  //                 style: TextStyle(color: Colors.white),
  //               ))
  //         ],
  //       ),
  //       const Padding(
  //         padding: EdgeInsets.symmetric(vertical: 20.0),
  //         child: Divider(
  //           color: Color.fromARGB(255, 202, 199, 199),
  //           indent: 20,
  //           endIndent: 20,
  //         ),
  //       ),
  //       Row(
  //         children: [
  //           const Text("Subtotal"),
  //           const Spacer(),
  //           Text(
  //             "Rs.$totalPrice",
  //             style: GoogleFonts.b612Mono(
  //                 fontSize: 15, fontWeight: FontWeight.bold),
  //           )
  //         ],
  //       ),
  //       const SizedBox(
  //         height: 10,
  //       ),
  //       const Row(
  //         children: [Text("Shipping"), Spacer(), Text("Free")],
  //       ),
  //       const Padding(
  //         padding: EdgeInsets.symmetric(vertical: 20.0),
  //         child: Divider(
  //           color: Color.fromARGB(255, 202, 199, 199),
  //           indent: 20,
  //           endIndent: 20,
  //         ),
  //       ),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.start,
  //         crossAxisAlignment: CrossAxisAlignment.center,
  //         children: [
  //           const Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Text(
  //                 "Total",
  //                 style: TextStyle(fontSize: 18),
  //               ),
  //               Text("Including ₹57.05 in taxes"),
  //             ],
  //           ),
  //           const Spacer(),
  //           Text(
  //             "Rs.$totalPrice",
  //             style: GoogleFonts.b612Mono(
  //                 fontSize: 25, fontWeight: FontWeight.bold),
  //           )
  //         ],
  //       ),
  //       // const SizedBox(
  //       //   height: 400,
  //       // )
  //     ],
  //   );
  // }
}

class CartItem {
  final String id;
  final String product;
  final DateTime deliveryDate;
  final String deliveryTime;
  final String delivery;

  CartItem(
    this.id,
    this.product,
    this.deliveryDate,
    this.deliveryTime,
    this.delivery,
  );
}

class DeliveryPricing {
  late int standardPrice;
  // late int fixedPrice;
  late int midnightPrice;
  DeliveryPricing({required this.areaPrice}) {
    standardPrice = areaPrice;
    // fixedPrice = 100 + standardPrice;
    midnightPrice =
        (Get.find<ProductCtrl>().settings?.midnightdelivery ?? 0) +
        standardPrice;
  }
  final int areaPrice;
  // static int standardPrice = Get.find<ProductCtrl>()
  //     .settings!
  //     .delcharges
  //     .firstWhereOrNull((element) => element.name==areaName)?.price.toInt()??0;
  // static  int fixedPrice = 100+standardPrice;
  // static const int midnightPrice = 250+standardPrice;

  int getDeliveryPrice(String type) {
    print(type);
    switch (type) {
      case "Standard Delivery":
        return standardPrice;
      // case "Fix Time Delivery":
      //   return fixedPrice;
      case "Midnight Delivery":
        return midnightPrice;
      default:
        return 0;
    }
  }

  int parseTime(String time) {
    if (time.contains("AM")) {
      int hour = int.parse(time.split("AM").first);
      return hour == 12 ? 0 : hour;
    } else {
      int hour = int.parse(time.split("PM").first);
      return hour == 12 ? 12 : hour + 12;
    }
  }

  bool isTimeOverlapping(String time1, String time2) {
    int start1 = parseTime(time1.split("-").first);
    int end1 = parseTime(time1.split("-").last);
    int start2 = parseTime(time2.split("-").first);
    int end2 = parseTime(time2.split("-").last);

    return !(end1 <= start2 || end2 <= start1);
  }

  int calculateShippingPrice(List<CartItem> cartItems) {
    cartItems.sort((b, a) => a.delivery.compareTo(b.delivery));

    // for (var element in cartItems) {
    //   print(element.deliveryTime);
    // }
    int shippingPrice = 0;
    List<int> processedIndexes = [];
    // for (int i = 0; i < cartItems.length; i++) {
    //   for (int j = i + 1; j < cartItems.length; j++) {}
    // }

    for (int i = 0; i < cartItems.length; i++) {
      if (processedIndexes.contains(i)) {
        continue; // Skip already processed items
      }

      CartItem item1 = cartItems[i];
      int price1 = getDeliveryPrice(item1.delivery);
      print("--$price1");
      for (int j = i + 1; j < cartItems.length; j++) {
        if (processedIndexes.contains(j)) {
          continue; // Skip already processed items
        }
        CartItem item2 = cartItems[j];

        int price2 = getDeliveryPrice(item2.delivery);

        if (item1.deliveryDate == item2.deliveryDate) {
          // if (item1.deliveryDate.compareTo(item2.deliveryDate) == 0) {
          // Check if times overlap

          if (item1.deliveryTime == item2.deliveryTime) {
            // Removing the duplicate delivery
            processedIndexes.add(j);
            continue;
          }
          if ((isTimeOverlapping(item1.deliveryTime, item2.deliveryTime) ||
              (isTimeOverlapping(item2.deliveryTime, item1.deliveryTime)))) {
            shippingPrice += price1;
            processedIndexes.add(j);

            // }
          }
        }
      }

      if (!processedIndexes.contains(i)) {
        shippingPrice += price1;
      }
    }

    return shippingPrice;
  }
}
