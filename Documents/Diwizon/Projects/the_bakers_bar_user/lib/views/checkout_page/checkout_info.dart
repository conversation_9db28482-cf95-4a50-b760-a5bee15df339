import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/Models/settings.dart';
import 'package:the_bakers_bar_user/Models/user_details.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/firebase.dart';
import 'package:the_bakers_bar_user/shared/methods.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';
import 'checkout_items_col.dart';

class CheckOut extends StatefulWidget {
  const CheckOut({super.key});

  @override
  State<CheckOut> createState() => _CheckOutState();
}

class _CheckOutState extends State<CheckOut> {
  String? startTime;
  String? endTime;
  bool ismidnight = false;
  bool isfirst = true;
  bool issecond = true;
  // int selectedindexfixedtime = 0;
  // int selectedindexstandarddel = 0;
  List<bool> onclicked = [true, false, false];
  List<int> radiolist = [0, 1, 2];
  int selectedradio = 0;
  DateTime? orderdate;
  SettingsModel? settingsModel;
  num standardprice = 0;
  Userdetails? userdetails;
  String? selectedaddress;
  String? selectedcity = "VADODARA";
  // String? selectedcountry = "India";
  String? selectedstate = "GUJARAT";
  String instructions = "";
  String? selectedarea;
  List<String> savedadd = ['New User'];

  String? selectedid;
  final TextEditingController fnamectrl = TextEditingController();
  final TextEditingController delievryinstrCtrl = TextEditingController();
  final TextEditingController lnamectrl = TextEditingController();
  final TextEditingController addressctrl = TextEditingController();
  final TextEditingController pincodectrl = TextEditingController();
  final TextEditingController phonectrl = TextEditingController();
  final TextEditingController emailctrl = TextEditingController();

  List<String>? areaList = [];
  String radiogrpvalue = "-1";
  @override
  void initState() {
    super.initState();

    // Get.put(CategoryCtrl());
    settingsModel = Get.find<ProductCtrl>().settings;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProductCtrl>(
      builder: (pctrl) {
        final size = MediaQuery.sizeOf(context);
        final x = (1920 - size.width) / 2;
        if (pctrl.settings != null) {
          settingsModel = pctrl.settings;

          areaList?.clear();
          areaList = pctrl.settings?.delcharges.map((e) => e.name).toList();
          savedadd.clear();
          savedadd = ['New User'];
          if (pctrl.userDetails != null) {
            if (pctrl.userDetails!.address.isNotEmpty) {
              for (var element in pctrl.userDetails?.address ?? []) {
                savedadd.add(element.name);
              }
            }
          }
          userdetails = pctrl.userDetails;
        }

        return Scaffold(
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                        height: size.width <= 510 ? 100 : 200,
                        width: double.infinity,
                        child: Stack(
                          fit: StackFit.expand,
                          children: [
                            Image.asset(
                              "assets/images/common-banner 3.png",
                              fit: BoxFit.cover,
                            ),
                            Align(
                              child: Text(
                                "CHECK OUT INFO",
                                style: GoogleFonts.crimsonPro(
                                  fontSize: size.width <= 510 ? 24 : 34,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      ( /*Get.find<CategoryCtrl>().settings == null ||*/ pctrl
                                  .userDetails ==
                              null)
                          ? const Padding(
                              padding: EdgeInsets.only(top: 50.0),
                              child: CircularProgressIndicator(),
                            )
                          : ResponsiveWid(
                              //mobile
                              mobile: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      left: 20,
                                      top: 20,
                                      bottom: 20,
                                      right: 20,
                                    ),
                                    child: infoCol(context, pctrl),
                                  ),
                                  ExpansionTile(
                                    initiallyExpanded: true,
                                    // subtitle: Icon(CupertinoIcons.add),
                                    title: Row(
                                      children: [
                                        Text(
                                          "Show Order Summary",
                                          style: GoogleFonts.aBeeZee(
                                            fontSize: 14,
                                          ),
                                        ),
                                        // const Icon(Icons.keyboard_arrow_down),
                                      ],
                                    ),
                                    leading: const Icon(
                                      CupertinoIcons.cart,
                                      color: Colors.black,
                                      weight: Checkbox.width,
                                    ),
                                    // trailing: Text(
                                    // "Rs.",
                                    // style: GoogleFonts.b612Mono(fontSize: 14),
                                    // ),
                                    children: [
                                      Container(
                                        color: const Color(0xfff5f5f5),
                                        child: const Padding(
                                          padding: EdgeInsets.only(
                                            left: 10.0,
                                            top: 20,
                                            bottom: 20,
                                            right: 10,
                                          ),

                                          // x < 380 ? 400 - x : 100),
                                          child: CartItems(from: "1"),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),

                              //tablet
                              tablet: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(
                                      left: x < 280 ? 300.0 - x : 50,
                                      top: 20,
                                      bottom: 20,
                                      right: 50,
                                    ),
                                    child: infoCol(context, pctrl),
                                  ),
                                  ExpansionTile(
                                    initiallyExpanded: true,
                                    // subtitle: Icon(CupertinoIcons.add),
                                    title: Row(
                                      children: [
                                        Text(
                                          "Show Order Summary",
                                          style: GoogleFonts.aBeeZee(
                                            fontSize: 20,
                                          ),
                                        ),
                                        const Icon(Icons.keyboard_arrow_down),
                                      ],
                                    ),
                                    leading: const Icon(
                                      CupertinoIcons.cart,
                                      color: Colors.black,
                                      weight: Checkbox.width,
                                    ),
                                    trailing: Text(
                                      "Rs.",
                                      style: GoogleFonts.b612Mono(fontSize: 20),
                                    ),
                                    children: [
                                      Container(
                                        color: const Color(0xfff5f5f5),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                            left: 50.0,
                                            top: 20,
                                            bottom: 20,
                                            right: x < 380 ? 400 - x : 100,
                                          ),
                                          child: const CartItems(from: "1"),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),

                              //desktop
                              desktop: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.only(
                                            left: x < 280 ? 300.0 - x : 50,
                                            top: 20,
                                            bottom: 20,
                                            right: 50,
                                          ),
                                          child: infoCol(context, pctrl),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      constraints: const BoxConstraints(
                                        minHeight: 800,
                                      ),
                                      color: const Color(0xfff5f5f5),
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          left: 50.0,
                                          top: 20,
                                          bottom: 20,
                                          right: x < 380 ? 400 - x : 100,
                                        ),
                                        child: const CartItems(from: "1"),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                    ],
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: size.width >= 1024 ? 15 : 12,
                ),
                decoration: const BoxDecoration(
                  color: themeColorLite,
                  // color: Color.fromARGB(255, 255, 255, 255),
                  boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6)],
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (size.width >= 1024) const Spacer(flex: 1),
                    InkWell(
                      onTap: () {
                        context.go(Routes.cart);
                      },
                      child: const Row(
                        children: [
                          Icon(
                            color: Color(0xffe9867f),
                            Icons.arrow_back_ios_outlined,
                            size: 15,
                          ),
                          SizedBox(width: 5),
                          Text(
                            "Return to Cart",
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Color(0xffe9867f),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(flex: 2),
                    ElevatedButton(
                      style: ButtonStyle(
                        backgroundColor: const WidgetStatePropertyAll(
                          Color(0xffe9867f),
                        ),
                        padding: WidgetStatePropertyAll(
                          EdgeInsets.symmetric(
                            vertical: size.width <= 510 ? 20 : 20,
                            horizontal: size.width <= 510 ? 10 : 20,
                          ),
                        ),
                        shape: WidgetStatePropertyAll(
                          RoundedRectangleBorder(
                            side: const BorderSide(color: Color(0xffe9867f)),
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                      ),
                      onPressed: () async {
                        if (userdetails?.cartitems.isEmpty ?? true) {
                          Fluttertoast.showToast(msg: "Cart is Empty!!");
                          return;
                        }
                        //   <String, dynamic>{
                        //   selectedid!: {
                        //     "name": "${fnamectrl.text} ${lnamectrl.text}",
                        //     "area": selectedarea,
                        //     "city": selectedcity,
                        //     "state": selectedstate,
                        //     "address": selectedaddress,
                        //     "pincode": pincodectrl.text,
                        //     "phone": phonectrl.text,
                        //   }
                        // }
                        // print(radiogrpvalue);
                        if (fnamectrl.text.isNotEmpty &&
                            lnamectrl.text.isNotEmpty &&
                            selectedarea != null &&
                            selectedcity != null &&
                            selectedstate != null &&
                            addressctrl.text.isNotEmpty &&
                            phonectrl.text.isNotEmpty &&
                            pincodectrl.text.isNotEmpty) {
                          if (context.mounted) {
                            context.go(
                              "${Routes.checkoutshipping}/$selectedid",
                              extra: instructions,
                            );
                          }
                        } else {
                          Fluttertoast.showToast(msg: "Address not selected!");
                        }
                      },
                      child: const Text(
                        "Continue to Shipping",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    if (size.width >= 1024) const Spacer(flex: 1),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Column infoCol(BuildContext context, ProductCtrl pctrl) {
    final size = MediaQuery.sizeOf(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            InkWell(
              onTap: () {
                context.go(Routes.cart);
              },
              child: Text(
                "Cart",
                style: TextStyle(
                  color: const Color(0xffe9867f),
                  fontSize: size.width <= 510 ? 12 : 20,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 8),
            Text(
              "Information",
              style: TextStyle(fontSize: size.width <= 510 ? 12 : 20),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 8),
            InkWell(
              onTap: () {
                if (userdetails?.cartitems.isEmpty ?? true) {
                  Fluttertoast.showToast(msg: "Cart is Empty!!");
                  return;
                }
                if (fnamectrl.text.isNotEmpty &&
                    lnamectrl.text.isNotEmpty &&
                    selectedarea != null &&
                    selectedcity != null &&
                    selectedstate != null &&
                    addressctrl.text.isNotEmpty &&
                    phonectrl.text.isNotEmpty &&
                    pincodectrl.text.isNotEmpty) {
                  // pctrl.area = selectedarea;
                  // pctrl.name = "${fnamectrl.text} ${lnamectrl.text}";
                  // pctrl.pincode = pincodectrl.text;
                  // pctrl.phone = phonectrl.text;
                  context.go("${Routes.checkoutshipping}/$selectedid");
                } else {
                  Fluttertoast.showToast(msg: "Empty Fields!");
                }
              },
              child: Text(
                "Shippping",
                style: TextStyle(
                  color: const Color(0xffe9867f),
                  fontSize: size.width <= 510 ? 12 : 20,
                ),
              ),
            ),
            const SizedBox(width: 7),
            Icon(
              Icons.arrow_forward_ios_outlined,
              size: size.width <= 510 ? 12 : 15,
            ),
            const SizedBox(width: 7),
            InkWell(
              onTap: () {
                // context.go("${Routes.checkoutpayment}/$selectedid");
              },
              child: Text(
                "Payment",
                style: TextStyle(
                  color: const Color(0xffe9867f),
                  fontSize: size.width <= 510 ? 12 : 20,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 30),
        Text(
          "Contact",
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: size.width <= 510 ? 14 : 18,
          ),
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            const Icon(
              CupertinoIcons.profile_circled,
              size: 50,
              color: Colors.blue,
            ),
            if (pctrl.userDetails != null)
              Text(
                "${pctrl.userDetails?.fname ?? ""} ${pctrl.userDetails?.lname ?? ""} (${(pctrl.userDetails?.address.isEmpty ?? true) ? "" : pctrl.userDetails?.address.first.phone ?? ""})",
              ),
          ],
        ),
        const SizedBox(height: 10),
        const SizedBox(height: 30),
        const Text(
          "Shipping address",
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
        ),
        const SizedBox(height: 10),
        StaggeredGrid.extent(
          maxCrossAxisExtent: 600,
          mainAxisSpacing: 10,
          crossAxisSpacing: 20,
          children: [
            //already saved address
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.only(bottom: 4.0),
                  child: Text(
                    'Saved Address',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
                ...List.generate(userdetails!.address.length, (index) {
                  // print(userdetails?.address);
                  return RadioListTile(
                    tileColor: Colors.transparent,
                    overlayColor: const WidgetStatePropertyAll(
                      Colors.transparent,
                    ),
                    hoverColor: Colors.transparent,
                    title: Text(
                      "${userdetails?.address[index].address}, ${pctrl.userDetails?.address[index].city}, ${pctrl.userDetails?.address[index].state} , ${pctrl.userDetails?.address[index].pincode}",
                      style: TextStyle(fontSize: size.width <= 510 ? 12 : 16),
                    ),
                    subtitle: Row(
                      children: [
                        InkWell(
                          onTap: () {
                            fnamectrl.text =
                                userdetails?.address[index].name
                                    .split(" ")
                                    .first ??
                                "";
                            lnamectrl.text =
                                userdetails?.address[index].name
                                    .split(" ")
                                    .last ??
                                "";
                            addressctrl.text =
                                userdetails?.address[index].address ?? "";
                            selectedarea = userdetails?.address[index].area
                                .trim()
                                .toLowerCase();
                            selectedcity = selectedcity;
                            selectedstate = selectedstate;
                            pincodectrl.text =
                                userdetails?.address[index].pincode ?? "";
                            phonectrl.text =
                                userdetails?.address[index].phone ?? "";
                            emailctrl.text =
                                userdetails?.address[index].email ?? "";

                            // radiogrpvalue = value.toString();
                            selectedid = userdetails?.address[index].docId;
                            // setState(() {});
                            addressDialog(
                              context,
                              size,
                              userdetails?.address[index].docId,
                            );
                          },
                          child: Text(
                            "Edit Address",
                            style: TextStyle(
                              color: Colors.blueAccent,
                              fontWeight: FontWeight.w500,
                              fontSize: size.width <= 510 ? 12 : 14,
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        InkWell(
                          onTap: () => showDialog(
                            builder: (context) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                content: Padding(
                                  padding: const EdgeInsets.all(15.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Add delivery instructions",
                                        style: TextStyle(
                                          fontSize: size.width <= 510 ? 12 : 16,
                                        ),
                                      ),
                                      const Text(
                                        "Do we need additional instructions to deliver to this address?",
                                      ),
                                      const SizedBox(height: 10),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          TextField(
                                            controller: delievryinstrCtrl,
                                            decoration: textfieldDecoration()
                                                .copyWith(
                                                  hintText: 'Instructions',
                                                ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                actions: [
                                  ElevatedButton(
                                    style: ButtonStyle(
                                      backgroundColor:
                                          const WidgetStatePropertyAll(
                                            Color(0xffe9867f),
                                          ),
                                      padding: const WidgetStatePropertyAll(
                                        EdgeInsets.symmetric(
                                          vertical: 0,
                                          horizontal: 20,
                                        ),
                                      ),
                                      shape: WidgetStatePropertyAll(
                                        RoundedRectangleBorder(
                                          side: const BorderSide(
                                            color: Color(0xffe9867f),
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            5,
                                          ),
                                        ),
                                      ),
                                    ),
                                    onPressed: () async {
                                      instructions = delievryinstrCtrl.text;
                                      if (context.mounted) {
                                        context.pop();
                                      }
                                    },
                                    child: const Text(
                                      "Save",
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ],
                              );
                            },
                            context: context,
                          ),
                          child: Text(
                            "Add delivery instructions",
                            style: TextStyle(
                              fontSize: size.width <= 510 ? 12 : 14,
                              color: Colors.blueAccent,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    value: index.toString(),
                    groupValue: radiogrpvalue,
                    onChanged: (value) {
                      fnamectrl.text =
                          userdetails?.address[index].name.split(" ").first ??
                          "";
                      lnamectrl.text =
                          userdetails?.address[index].name.split(" ").last ??
                          "";
                      addressctrl.text =
                          userdetails?.address[index].address ?? "";
                      selectedarea = userdetails?.address[index].area
                          .trim()
                          .toLowerCase();
                      selectedcity = selectedcity;
                      selectedstate = selectedstate;
                      pincodectrl.text =
                          userdetails?.address[index].pincode ?? "";
                      phonectrl.text = userdetails?.address[index].phone ?? "";
                      emailctrl.text = userdetails?.address[index].email ?? "";

                      radiogrpvalue = value.toString();
                      selectedid = userdetails?.address[index].docId;
                      setState(() {});
                    },
                  );
                }),
                // RadioListTile(
                //   title: Text(
                //       "${pctrl.userDetails?.address ?? savedadd} $selectedcity $selectedstate ${pincodectrl.text}"),
                //   subtitle: const Row(
                //     children: [
                //       Text("Edit Address"),
                //       SizedBox(width: 5),
                //       Text("Add delivery instructions")
                //     ],
                //   ),
                //   value: 0,
                //   groupValue: 0,
                //   onChanged: (value) {},
                // ),
                const SizedBox(height: 10),

                //addnewadress
                RadioListTile(
                  selectedTileColor: Colors.transparent,
                  tileColor: Colors.transparent,
                  overlayColor: const WidgetStatePropertyAll(
                    Colors.transparent,
                  ),
                  hoverColor: Colors.transparent,
                  title: const Text("Add new Address"),
                  value: "NEW",
                  groupValue: radiogrpvalue,
                  onChanged: (value) {
                    radiogrpvalue = value!;
                    setState(() {});
                    fnamectrl.clear();
                    lnamectrl.clear();
                    addressctrl.clear();
                    pincodectrl.clear();
                    phonectrl.clear();
                    emailctrl.clear();

                    addressDialog(context, size, null);
                  },
                ),
              ],
            ),
            // const SizedBox(height: 40),
            // Row(
            //   crossAxisAlignment: CrossAxisAlignment.center,
            //   children: [
            //     InkWell(
            //       onTap: () {
            //         context.go(Routes.cart);
            //       },
            //       child: const Row(
            //         children: [
            //           Icon(
            //             color: Color(0xffe9867f),
            //             Icons.arrow_back_ios_outlined,
            //             size: 15,
            //           ),
            //           SizedBox(width: 5),
            //           Text(
            //             "Return to Cart",
            //             style: TextStyle(
            //                 fontWeight: FontWeight.w500,
            //                 color: Color(0xffe9867f)),
            //           ),
            //         ],
            //       ),
            //     ),
            //     const Spacer(),
            //     ElevatedButton(
            //         style: ButtonStyle(
            //             backgroundColor:
            //                 WidgetStatePropertyAll(Color(0xffe9867f)),
            //             padding: WidgetStatePropertyAll(EdgeInsets.symmetric(
            //                 vertical: size.width <= 510 ? 20 : 20,
            //                 horizontal: size.width <= 510 ? 10 : 20)),
            //             shape: WidgetStatePropertyAll(RoundedRectangleBorder(
            //                 side: const BorderSide(color: Color(0xffe9867f)),
            //                 borderRadius: BorderRadius.circular(5)))),
            //         onPressed: () async {
            //           if (userdetails?.cartitems.isEmpty ?? true) {
            //             Fluttertoast.showToast(msg: "Cart is Empty!!");
            //             return;
            //           }
            //           //   <String, dynamic>{
            //           //   selectedid!: {
            //           //     "name": "${fnamectrl.text} ${lnamectrl.text}",
            //           //     "area": selectedarea,
            //           //     "city": selectedcity,
            //           //     "state": selectedstate,
            //           //     "address": selectedaddress,
            //           //     "pincode": pincodectrl.text,
            //           //     "phone": phonectrl.text,
            //           //   }
            //           // }
            //           // print(radiogrpvalue);
            //           if (fnamectrl.text.isNotEmpty &&
            //               lnamectrl.text.isNotEmpty &&
            //               selectedarea != null &&
            //               selectedcity != null &&
            //               selectedstate != null &&
            //               addressctrl.text.isNotEmpty &&
            //               phonectrl.text.isNotEmpty &&
            //               pincodectrl.text.isNotEmpty) {
            //             if (context.mounted) {
            //               context.go("${Routes.checkoutshipping}/$selectedid",
            //                   extra: instructions);
            //             }
            //           } else {
            //             Fluttertoast.showToast(msg: "Address not selected!");
            //           }
            //         },
            //         child: const Text(
            //           "Continue to Shipping",
            //           style: TextStyle(color: Colors.white),
            //         ))
            //   ],
            // ),
            // const SizedBox(height: 40),
            const Divider(color: Color.fromARGB(255, 220, 217, 217)),
            const SizedBox(height: 10),
            Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () => context.go(Routes.returnandrefundPolicy),
                  child: Text(
                    "Refund policy",
                    style: TextStyle(
                      color: const Color(0xffe9867f),
                      fontSize: size.width <= 510 ? 12 : 14,
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                InkWell(
                  onTap: () => context.go(Routes.privacy),
                  child: Text(
                    "Privacy policy",
                    style: TextStyle(
                      color: const Color(0xffe9867f),
                      fontSize: size.width <= 510 ? 12 : 14,
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                InkWell(
                  onTap: () => context.go(Routes.terms),
                  child: Text(
                    "Terms of service",
                    style: TextStyle(
                      color: const Color(0xffe9867f),
                      fontSize: size.width <= 510 ? 12 : 14,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Future<dynamic> addressDialog(
    BuildContext context,
    Size size,
    String? docID,
  ) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return SingleChildScrollView(
          child: AlertDialog(
            backgroundColor: Colors.white,
            actions: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 5,
                  vertical: 20,
                ),
                child: Column(
                  children: [
                    // Column(
                    //   crossAxisAlignment:
                    //       CrossAxisAlignment.start,
                    //   children: [
                    //     const Padding(
                    //       padding:
                    //           EdgeInsets.only(bottom: 4.0),
                    //       child: Text(
                    //         'Country',
                    //         style: TextStyle(
                    //             fontWeight: FontWeight.w600),
                    //       ),
                    //     ),
                    //     DropdownButtonFormField(
                    //       decoration:
                    //           textfieldDecoration().copyWith(
                    //         hintText: 'Country',
                    //       ),
                    //       value: selectedcountry,
                    //       items: List.generate(1, (index) {
                    //         const String country = "India";
                    //         return const DropdownMenuItem(
                    //           value: country,
                    //           child: Text(country),
                    //         );
                    //       }),
                    //       onChanged: (value) {
                    //         selectedcountry = value;
                    //         // weightrangeController.text = value?.docId ?? "";
                    //       },
                    //     ),
                    //   ],
                    // ),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add a New Address',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: size.width <= 510 ? 16 : 30,
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              context.pop();
                            },
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 25),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 4.0),
                                child: Text(
                                  'First Name',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: size.width <= 510 ? 12 : 16,
                                  ),
                                ),
                              ),
                              TextField(
                                controller: fnamectrl,

                                // onChanged: (value) {
                                //   tag = value;
                                //   descriptionctrl.text = tag;
                                // },
                                decoration: textfieldDecoration().copyWith(
                                  hintText: 'First Name',
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 4.0),
                                child: Text(
                                  'Last Name',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: size.width <= 510 ? 12 : 16,
                                  ),
                                ),
                              ),
                              TextField(
                                controller: lnamectrl,

                                // onChanged: (value) {
                                //   tag = value;
                                //   descriptionctrl.text = tag;
                                // },
                                decoration: textfieldDecoration().copyWith(
                                  hintText: 'Last Name',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: size.width <= 510 ? 15 : 25),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Address',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: size.width <= 510 ? 12 : 16,
                            ),
                          ),
                        ),
                        TextField(
                          controller: addressctrl,

                          // onChanged: (value) {
                          //   tag = value;
                          //   descriptionctrl.text = tag;
                          // },
                          decoration: textfieldDecoration().copyWith(
                            hintText: 'Address',
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: size.width <= 510 ? 15 : 25),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Area',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: size.width <= 510 ? 12 : 16,
                            ),
                          ),
                        ),
                        DropdownButtonFormField(
                          dropdownColor: Colors.white,
                          decoration: textfieldDecoration().copyWith(
                            contentPadding: EdgeInsets.only(left: 8, right: 2),
                            hintText: 'Area',
                          ),
                          value: selectedarea,
                          items: List.generate(areaList?.length ?? 0, (index) {
                            areaList?.sort((a, b) => a.compareTo(b));
                            String area =
                                areaList?[index].trim().toLowerCase() ?? "";
                            return DropdownMenuItem(
                              value: area,
                              child: Text(area),
                            );
                          }),
                          onChanged: (value) {
                            selectedarea = value?.trim().toLowerCase();
                            setState(() {});
                            // weightrangeController.text = value?.docId ?? "";
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: size.width <= 510 ? 15 : 25),
                    if (size.width <= 510)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(bottom: 4.0),
                            child: Text(
                              'City',
                              style: TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                          DropdownButtonFormField(
                            decoration: textfieldDecoration().copyWith(
                              contentPadding: EdgeInsets.only(
                                left: 8,
                                right: 3,
                              ),
                              hintText: 'City',
                            ),
                            value: selectedcity,
                            items: List.generate(1, (index) {
                              const String city = "VADODARA";
                              return const DropdownMenuItem(
                                value: city,
                                child: Text(city),
                              );
                            }),
                            onChanged: (value) {
                              selectedcity = value;
                              // weightrangeController.text = value?.docId ?? "";
                            },
                          ),
                        ],
                      ),
                    if (size.width <= 510) const SizedBox(height: 20),
                    if (size.width <= 510)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(bottom: 4.0),
                            child: Text(
                              'State',
                              style: TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                          DropdownButtonFormField(
                            decoration: textfieldDecoration().copyWith(
                              contentPadding: EdgeInsets.only(
                                left: 8,
                                right: 3,
                              ),
                              hintText: 'State',
                            ),
                            value: selectedstate,
                            items: List.generate(1, (index) {
                              const state = "GUJARAT";
                              return const DropdownMenuItem(
                                value: state,
                                child: Text(state),
                              );
                            }),
                            onChanged: (value) {
                              selectedstate = value;
                              // weightrangeController.text = value?.docId ?? "";
                            },
                          ),
                        ],
                      ),
                    if (size.width <= 510) const SizedBox(height: 20),
                    if (size.width <= 510)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(bottom: 4.0),
                            child: Text(
                              'Pincode',
                              style: TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                          TextField(
                            controller: pincodectrl,

                            // onChanged: (value) {
                            //   tag = value;
                            //   descriptionctrl.text = tag;
                            // },
                            decoration: textfieldDecoration().copyWith(
                              hintText: 'Pincode',
                            ),
                          ),
                        ],
                      ),
                    if (size.width > 510)
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(bottom: 4.0),
                                  child: Text(
                                    'City',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                DropdownButtonFormField(
                                  decoration: textfieldDecoration().copyWith(
                                    hintText: 'City',
                                  ),
                                  value: selectedcity,
                                  items: List.generate(1, (index) {
                                    const String city = "VADODARA";
                                    return const DropdownMenuItem(
                                      value: city,
                                      child: Text(city),
                                    );
                                  }),
                                  onChanged: (value) {
                                    selectedcity = value;
                                    // weightrangeController.text = value?.docId ?? "";
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(bottom: 4.0),
                                  child: Text(
                                    'State',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                DropdownButtonFormField(
                                  decoration: textfieldDecoration().copyWith(
                                    hintText: 'State',
                                  ),
                                  value: selectedstate,
                                  items: List.generate(1, (index) {
                                    const state = "GUJARAT";
                                    return const DropdownMenuItem(
                                      value: state,
                                      child: Text(state),
                                    );
                                  }),
                                  onChanged: (value) {
                                    selectedstate = value;
                                    // weightrangeController.text = value?.docId ?? "";
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(bottom: 4.0),
                                  child: Text(
                                    'Pincode',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                TextField(
                                  controller: pincodectrl,

                                  // onChanged: (value) {
                                  //   tag = value;
                                  //   descriptionctrl.text = tag;
                                  // },
                                  decoration: textfieldDecoration().copyWith(
                                    hintText: 'Pincode',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    SizedBox(height: size.width <= 510 ? 15 : 25),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Phone',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: phonectrl,

                          // onChanged: (value) {
                          //   tag = value;
                          //   descriptionctrl.text = tag;
                          // },
                          decoration: textfieldDecoration().copyWith(
                            hintText: 'Phone',
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: size.width <= 510 ? 15 : 25),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'E-mail (Optional)',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: emailctrl,

                          // onChanged: (value) {
                          //   tag = value;
                          //   descriptionctrl.text = tag;
                          // },
                          decoration: textfieldDecoration().copyWith(
                            hintText: 'E- mail',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 15),
              //addnewaddresbutton
              ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: const WidgetStatePropertyAll(
                    Color(0xffe9867f),
                  ),
                  padding: const WidgetStatePropertyAll(
                    EdgeInsets.symmetric(vertical: 0, horizontal: 8),
                  ),
                  shape: WidgetStatePropertyAll(
                    RoundedRectangleBorder(
                      side: const BorderSide(color: Color(0xffe9867f)),
                      borderRadius: BorderRadius.circular(5),
                    ),
                  ),
                ),
                onPressed: () async {
                  if (fnamectrl.text.isNotEmpty &&
                      lnamectrl.text.isNotEmpty &&
                      selectedarea != null &&
                      selectedcity != null &&
                      selectedstate != null &&
                      // selectedaddress != null &&
                      addressctrl.text.isNotEmpty &&
                      phonectrl.text.isNotEmpty &&
                      pincodectrl.text.isNotEmpty) {
                    // pctrl.area = selectedarea;
                    // pctrl.name =
                    //     "${fnamectrl.text} ${lnamectrl.text}";
                    // pctrl.pincode = pincodectrl.text;
                    // pctrl.phone = phonectrl.text;

                    // pctrl.delDate = onclicked[0]
                    // ? DateTime.now()
                    // : onclicked[1]
                    // ? DateTime.now().add(const Duration(days: 1))
                    // : orderdate;
                    //     pctrl.delDate = onclicked[0]
                    // ? DateTime.now().toString().split(" ").first
                    // : onclicked[1]
                    //     ? DateTime.now()
                    //         .add(const Duration(days: 1))
                    //         .toString()
                    //         .split(" ")
                    //         .first
                    //     : orderdate.toString().split(" ").first;
                    // pctrl.isLateNight = ismidnight;
                    // pctrl.deliveryTime = "$startTime-$endTime";
                    // pctrl.delivery = radiolist[0] == selectedradio
                    // ? "Fix Time Delivery"
                    // : radiolist[1] == selectedradio
                    // ? "Standard Delivery"
                    // : "Midnight Delivery";
                    // if (selectedaddress == savedadd[0]) {
                    selectedid = getRandomId(6);
                    final newadd = <String, dynamic>{
                      selectedid ?? "": {
                        "name": "${fnamectrl.text} ${lnamectrl.text}",
                        "area": selectedarea?.trim().toLowerCase(),
                        "city": selectedcity,
                        "state": selectedstate,
                        "address": addressctrl.text,
                        "pincode": pincodectrl.text,
                        "phone": phonectrl.text,
                        "email": emailctrl.text,
                      },
                    };
                    final oldadd = <String, dynamic>{
                      "name": "${fnamectrl.text} ${lnamectrl.text}",
                      "area": selectedarea?.trim().toLowerCase(),
                      "city": selectedcity,
                      "state": selectedstate,
                      "address": addressctrl.text,
                      "pincode": pincodectrl.text,
                      "phone": phonectrl.text,
                      "email": emailctrl.text,
                    };

                    DocumentSnapshot documentSnapshot = await FBFireStore.users
                        .doc(FBAuth.auth.currentUser?.uid)
                        .get();
                    if (docID == null) {
                      if (documentSnapshot.exists) {
                        Map<String, dynamic> tempaddMap = documentSnapshot.get(
                          'address',
                        );

                        newadd.addAll(tempaddMap);
                      } else {
                        print('Document does not exist.');
                      }
                      await FBFireStore.users
                          .doc(FBAuth.auth.currentUser?.uid)
                          .update({"address": newadd});
                      if (context.mounted) {
                        context.pop();
                      }

                      if (context.mounted) {
                        context.go("${Routes.checkoutshipping}/$selectedid");
                      }
                    } else {
                      if (documentSnapshot.exists) {
                        Map<String, dynamic> tempaddMap = documentSnapshot.get(
                          'address',
                        );

                        tempaddMap[docID] = oldadd;
                        await FBFireStore.users
                            .doc(FBAuth.auth.currentUser?.uid)
                            .update({"address": tempaddMap});
                        if (context.mounted) {
                          context.pop();
                        }

                        if (context.mounted) {
                          context.go(
                            "${Routes.checkoutshipping}/$docID",
                            extra: instructions,
                          );
                        }
                      } else {
                        print('Document does not exist.');
                      }
                    }
                    // }
                  } else {
                    Fluttertoast.showToast(msg: "Empty Fields!");
                  }
                },
                child: const Text(
                  "Use this Address",
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
