import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:the_bakers_bar_user/Models/productmodel.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/firebase.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';
import 'package:the_bakers_bar_user/views/homepage/flowers_card.dart';

class ShopflowersRow extends StatefulWidget {
  const ShopflowersRow({super.key});

  @override
  State<ShopflowersRow> createState() => _ShopflowersRowState();
}

class _ShopflowersRowState extends State<ShopflowersRow> {
  final scrlCtrl = ScrollController();
  List<ProductModel> productlistflower = [];

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    return StreamBuilder(
      stream: FBFireStore.product
          .where('notavailable', isEqualTo: false)
          .where('type', isEqualTo: ProductType.flower)
          .limit(10)
          .snapshots()
          .map(
            (event) =>
                event.docs.map((e) => ProductModel.fromSnapshot(e)).toList(),
          ),
      // .map((value) => value.docs.isEmpty
      //     ? null
      //     : ProductModel.fromSnapshot(value.docs.first)),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Text("Facing some issue");
        }
        if (snapshot.hasData) {
          productlistflower = snapshot.data ?? [];
          return Column(
            children: [
              SingleChildScrollView(
                physics: const NeverScrollableScrollPhysics(),
                controller: scrlCtrl,
                scrollDirection: Axis.horizontal,
                child: GestureDetector(
                  onHorizontalDragUpdate: (details) {
                    try {
                      if (details.primaryDelta != null) {
                        scrlCtrl.position.pointerScroll(-details.primaryDelta!);
                      }
                    } catch (e) {
                      debugPrint(e.toString());
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: size.width <= mobileMinsize ? 20 : 60,
                    ),
                    child: Row(
                      children: [
                        ...List.generate(
                          productlistflower.length,
                          (index) =>
                              Flowerscard(product: productlistflower[index]),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(height: 20),
              if (size.width > 510)
                Card(
                  color: Colors.transparent,
                  shadowColor: Colors.transparent,
                  surfaceTintColor: Colors.transparent,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(0.0),
                        child: ElevatedButton(
                          onPressed: () {
                            context.go('${Routes.allproducts}/Flower');
                          },
                          style: ButtonStyle(
                            backgroundColor: WidgetStatePropertyAll(
                              Colors.pink[300],
                            ),
                            shape: const WidgetStatePropertyAll(
                              LinearBorder(side: BorderSide.none),
                            ),
                          ),
                          child: const Text(
                            "VIEW ALL",
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          );
        } else {
          return Center(
            child: SizedBox(
              height: 30,
              width: 30,
              child: CircularProgressIndicator(),
            ),
          );
        }
      },
    );
  }
}
