import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';

class ClientsDiary extends StatefulWidget {
  const ClientsDiary({super.key, required this.buttonCarouselController});

  final CarouselSliderController buttonCarouselController;

  @override
  State<ClientsDiary> createState() => _ClientsDiaryState();
}

class _ClientsDiaryState extends State<ClientsDiary> {
  final scrlCtrl = ScrollController();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    return ResponsiveWid(
      //mobile
      mobile:
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.center,
          //   children: [
          //     Padding(
          //       padding: const EdgeInsets.all(20.0),
          //       child: Row(
          //         mainAxisAlignment: MainAxisAlignment.center,
          //         children: [
          //           InkWell(
          //             child: Container(
          //               decoration: BoxDecoration(
          //                   border: Border.all(style: BorderStyle.solid)),
          //               height: 30,
          //               width: 30,
          //               child: IconButton(
          //                   iconSize: 10,
          //                   highlightColor: Colors.transparent,
          //                   hoverColor: Colors.transparent,
          //                   icon: const Icon(Icons.arrow_back_ios),
          //                   onPressed: () =>
          //                       widget.buttonCarouselController.previousPage(
          //                         duration: const Duration(
          //                           milliseconds: 300,
          //                         ),
          //                       )),
          //             ),
          //           ),
          //         ],
          //       ),
          //     ),
          //     Expanded(
          //       // flex: 2,
          //       child: Padding(
          //         padding: const EdgeInsets.symmetric(horizontal: 30.0),
          //         child: Column(
          //           children: [
          //             CarouselSlider(
          //                 carouselController: widget.buttonCarouselController,
          //                 items: [
          //                   Column(
          //                     mainAxisAlignment: MainAxisAlignment.center,
          //                     crossAxisAlignment: CrossAxisAlignment.center,
          //                     children: [
          //                       const Expanded(
          //                         child: SizedBox(
          //                           height: 400,
          //                           width: 100,
          //                           child: Image(
          //                               fit: BoxFit.cover,
          //                               image:
          //                                   AssetImage('assets/images/5-min.png')),
          //                         ),
          //                       ),
          //                       const SizedBox(height: 10),
          //                       SizedBox(
          //                         width: 50,
          //                         child: Row(
          //                           mainAxisAlignment:
          //                               MainAxisAlignment.spaceBetween,
          //                           crossAxisAlignment: CrossAxisAlignment.center,
          //                           children: [
          //                             Icon(
          //                                 size: 7,
          //                                 CupertinoIcons.star_fill,
          //                                 color: Colors.pink[200]),
          //                             Icon(CupertinoIcons.star_fill,
          //                                 size: 7, color: Colors.pink[200]),
          //                             Icon(CupertinoIcons.star_fill,
          //                                 size: 7, color: Colors.pink[200]),
          //                             Icon(CupertinoIcons.star_fill,
          //                                 size: 7, color: Colors.pink[200]),
          //                             Icon(CupertinoIcons.star_fill,
          //                                 size: 7, color: Colors.pink[200]),
          //                           ],
          //                         ),
          //                       ),
          //                       const SizedBox(height: 10),
          //                       Expanded(
          //                         child: Container(
          //                           color: Colors.blueGrey.shade50,
          //                           child: Padding(
          //                             padding: const EdgeInsets.all(10.0),
          //                             child: Column(
          //                               children: [
          //                                 const SizedBox(
          //                                   height: 10,
          //                                 ),
          //                                 Expanded(
          //                                   child: SingleChildScrollView(
          //                                     physics:
          //                                         const NeverScrollableScrollPhysics(),
          //                                     controller: scrlCtrl,
          //                                     child: GestureDetector(
          //                                       onVerticalDragUpdate: (details) {
          //                                         // Handle horizontal drag here if needed
          //                                         // You can use details.primaryDelta to get the horizontal movement
          //                                         try {
          //                                           if (details.primaryDelta !=
          //                                               null) {
          //                                             scrlCtrl.position
          //                                                 .pointerScroll(-details
          //                                                     .primaryDelta!);
          //                                           }
          //                                         } catch (e) {
          //                                           debugPrint(e.toString());
          //                                         }
          //                                       },
          //                                       child: Text(
          //                                         // overflow: TextOverflow.ellipsis,
          //                                         textAlign: TextAlign.center,
          //                                         "simply dummy text of the printing and typesetting industry. Lorem Ipsum has book. It look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).",
          //                                         style: GoogleFonts.aBeeZee(
          //                                           wordSpacing: 3,
          //                                           fontSize: 10,
          //                                         ),
          //                                       ),
          //                                     ),
          //                                   ),
          //                                 ),
          //                               ],
          //                             ),
          //                           ),
          //                         ),
          //                       )
          //                     ],
          //                   ),
          //                 ],
          //                 options: CarouselOptions(
          //                     viewportFraction: 1,
          //                     scrollPhysics: const NeverScrollableScrollPhysics())),
          //           ],
          //         ),
          //       ),
          //     ),
          //     Padding(
          //       padding: const EdgeInsets.all(20.0),
          //       child: Row(
          //         mainAxisAlignment: MainAxisAlignment.center,
          //         children: [
          //           InkWell(
          //             child: Container(
          //               decoration: BoxDecoration(
          //                   border: Border.all(style: BorderStyle.solid)),
          //               height: 30,
          //               width: 30,
          //               child: IconButton(
          //                   iconSize: 10,
          //                   highlightColor: Colors.transparent,
          //                   hoverColor: Colors.transparent,
          //                   icon: const Icon(Icons.arrow_forward_ios),
          //                   onPressed: () {
          //                     widget.buttonCarouselController.nextPage(
          //                       duration: const Duration(milliseconds: 300),
          //                     );
          //                   }),
          //             ),
          //           ),
          //         ],
          //       ),
          //     )
          //     //  CarouselSlider(
          //     //     carouselController: widget.buttonCarouselController,
          //     //     items: [
          //     //       Column(
          //     //         mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          //     //         children: [
          //     //           const Expanded(
          //     //             flex: 2,
          //     //             child: Image(
          //     //                 fit: BoxFit.cover,
          //     //                 image: AssetImage('assets/images/cupcakecircle.jpeg')),
          //     //           ),
          //     //           const SizedBox(
          //     //             height: 5,
          //     //           ),
          //     //           Row(
          //     //             mainAxisAlignment: MainAxisAlignment.center,
          //     //             crossAxisAlignment: CrossAxisAlignment.center,
          //     //             children: [
          //     //               Icon(
          //     //                 CupertinoIcons.star_fill,
          //     //                 color: Colors.pink[200],
          //     //               ),
          //     //               Icon(CupertinoIcons.star_fill, color: Colors.pink[200]),
          //     //               Icon(CupertinoIcons.star_fill, color: Colors.pink[200]),
          //     //               Icon(CupertinoIcons.star_fill, color: Colors.pink[200]),
          //     //               Icon(CupertinoIcons.star_fill, color: Colors.pink[200]),
          //     //             ],
          //     //           ),
          //     //           SizedBox(
          //     //             width: 300,
          //     //             // height: 500,
          //     //             child: Text(
          //     //               textAlign: TextAlign.center,
          //     //               "simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. ",
          //     //               style: GoogleFonts.aBeeZee(
          //     //                 fontSize: 10,
          //     //               ),
          //     //             ),
          //     //           ),
          //     //         ],
          //     //       ),
          //     //     ],
          //     //     options: CarouselOptions(viewportFraction: 1)),
          //     // Expanded(
          //     //     child: Row(
          //     //   mainAxisAlignment: MainAxisAlignment.center,
          //     //   children: [
          //     //     InkWell(
          //     //       child: Container(
          //     //         decoration: BoxDecoration(
          //     //             border: Border.all(style: BorderStyle.solid)),
          //     //         height: 30,
          //     //         width: 30,
          //     //         child: Center(
          //     //           child: IconButton(
          //     //               iconSize: 10,
          //     //               highlightColor: Colors.transparent,
          //     //               hoverColor: Colors.transparent,
          //     //               icon: const Icon(Icons.arrow_forward_ios),
          //     //               onPressed: () {
          //     //                 widget.buttonCarouselController.nextPage(
          //     //                   duration: const Duration(milliseconds: 300),
          //     //                 );
          //     //               }),
          //     //         ),
          //     //       ),
          //     //     ),
          //     //   ],
          //     // )),
          //   ],
          // ),
          CarouselSlider(
            carouselController: widget.buttonCarouselController,
            items: [
              mobileCarousal(
                'assets/images/cupcakecircle.jpeg',
                '🎂 Incredible cake! Beautifully crafted and so tasty! Highly recommend! 🍰✨',
              ),
              mobileCarousal(
                'assets/images/cupcakecircle.jpeg',
                '🍰 Absolutely delicious! Perfectly moist and stunning design! 😋🌟',
              ),
              mobileCarousal(
                'assets/images/cupcakecircle.jpeg',
                '🎉 The cake was a hit! Beautifully made and so yummy! 😍🎂',
              ),
            ],
            options: CarouselOptions(viewportFraction: 1),
          ),

      //tablet
      tablet: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(style: BorderStyle.solid),
                    ),
                    height: 30,
                    width: 50,
                    child: IconButton(
                      iconSize: 14,
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: const Icon(Icons.arrow_back_ios),
                      onPressed: () =>
                          widget.buttonCarouselController.previousPage(
                            duration: const Duration(milliseconds: 300),
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              children: [
                CarouselSlider(
                  carouselController: widget.buttonCarouselController,
                  items: [
                    tabletCarousal(
                      'assets/images/5-min.png',
                      '🎂 Incredible cake! Beautifully crafted and so tasty! Highly recommend! 🍰✨',
                    ),
                    tabletCarousal(
                      'assets/images/5-min.png',
                      '🍰 Absolutely delicious! Perfectly moist and stunning design! 😋🌟',
                    ),
                    tabletCarousal(
                      'assets/images/5-min.png',
                      '🎉 The cake was a hit! Beautifully made and so yummy! 😍🎂',
                    ),
                  ],
                  options: CarouselOptions(
                    viewportFraction: 1,
                    scrollPhysics: const NeverScrollableScrollPhysics(),
                  ),
                ),
                // SizedBox(
                //   height: 20,
                // ),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   children: [
                //     ...List.generate(
                //       3,
                //       (index) {
                //         return Padding(
                //           padding: const EdgeInsets.only(right: 3.0),
                //           child: Container(
                //             height: 10,
                //             width: 10,
                //             decoration: BoxDecoration(
                //               border: Border.all(),
                //               shape: BoxShape.circle,
                //               color: Colors.white,
                //             ),
                //           ),
                //         );
                //       },
                //     )
                //   ],
                // )
              ],
            ),
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(style: BorderStyle.solid),
                    ),
                    height: 30,
                    width: 50,
                    child: IconButton(
                      iconSize: 14,
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: const Icon(Icons.arrow_forward_ios),
                      onPressed: () {
                        widget.buttonCarouselController.nextPage(
                          duration: const Duration(milliseconds: 300),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),

      //desktop
      desktop: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(style: BorderStyle.solid),
                    ),
                    height: 60,
                    width: 100,
                    child: IconButton(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: const Icon(Icons.arrow_back_ios),
                      onPressed: () =>
                          widget.buttonCarouselController.previousPage(
                            duration: const Duration(milliseconds: 300),
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                CarouselSlider(
                  carouselController: widget.buttonCarouselController,
                  items: [
                    desktopCarousal(
                      size.width,
                      'assets/images/5-min.png',
                      '🎂 Incredible cake! Beautifully crafted and so tasty! Highly recommend! 🍰✨',
                    ),
                    desktopCarousal(
                      size.width,
                      'assets/images/5-min.png',
                      '🍰 Absolutely delicious! Perfectly moist and stunning design! 😋🌟',
                    ),
                    desktopCarousal(
                      size.width,
                      'assets/images/5-min.png',
                      '🎉 The cake was a hit! Beautifully made and so yummy! 😍🎂',
                    ),
                  ],
                  options: CarouselOptions(
                    viewportFraction: 1,
                    scrollPhysics: const NeverScrollableScrollPhysics(),
                  ),
                ),
                // SizedBox(
                //   height: 20,
                // ),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   children: [
                //     ...List.generate(
                //       3,
                //       (index) {
                //         return Padding(
                //           padding: const EdgeInsets.only(right: 3.0),
                //           child: Container(
                //             height: 10,
                //             width: 10,
                //             decoration: BoxDecoration(
                //               border: Border.all(),
                //               shape: BoxShape.circle,
                //               color: Colors.white,
                //             ),
                //           ),
                //         );
                //       },
                //     )
                //   ],
                // )
              ],
            ),
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(style: BorderStyle.solid),
                    ),
                    height: 60,
                    width: 100,
                    child: IconButton(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: const Icon(Icons.arrow_forward_ios),
                      onPressed: () {
                        widget.buttonCarouselController.nextPage(
                          duration: const Duration(milliseconds: 300),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  desktopCarousal(double size, String img, String txt) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Image(fit: BoxFit.cover, image: AssetImage(img)),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Container(
            color: Color.fromARGB(255, 238, 237, 237),
            // color: Colors.blueGrey.shade50,
            child: Padding(
              padding: const EdgeInsets.all(45.0),
              child: Column(
                children: [
                  SizedBox(
                    width: 140,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.star_fill,
                          color: Colors.pink[200],
                          size: size < 1400 ? 14 : 18,
                        ),
                        Icon(
                          CupertinoIcons.star_fill,
                          color: Colors.pink[200],
                          size: size < 1400 ? 14 : 18,
                        ),
                        Icon(
                          CupertinoIcons.star_fill,
                          color: Colors.pink[200],
                          size: size < 1400 ? 14 : 18,
                        ),
                        Icon(
                          CupertinoIcons.star_fill,
                          color: Colors.pink[200],
                          size: size < 1400 ? 14 : 18,
                        ),
                        Icon(
                          CupertinoIcons.star_fill,
                          color: Colors.pink[200],
                          size: size < 1400 ? 14 : 18,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      controller: scrlCtrl,
                      child: GestureDetector(
                        onVerticalDragUpdate: (details) {
                          // Handle horizontal drag here if needed
                          // You can use details.primaryDelta to get the horizontal movement
                          try {
                            if (details.primaryDelta != null) {
                              scrlCtrl.position.pointerScroll(
                                -details.primaryDelta!,
                              );
                            }
                          } catch (e) {
                            debugPrint(e.toString());
                          }
                        },
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: txt,
                                style: GoogleFonts.aBeeZee(
                                  wordSpacing: 3,
                                  fontSize: size < 1400 ? 12 : 16,
                                ),
                              ),
                              // TextSpan(text: '😍', style: TextStyle()),
                              // TextSpan(text: '😍🎂', style: TextStyle()),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  mobileCarousal(String img, String txt) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          flex: 2,
          child: Image(fit: BoxFit.cover, image: AssetImage(img)),
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(CupertinoIcons.star_fill, color: Colors.pink[200], size: 12),
            Icon(CupertinoIcons.star_fill, color: Colors.pink[200], size: 12),
            Icon(CupertinoIcons.star_fill, color: Colors.pink[200], size: 12),
            Icon(CupertinoIcons.star_fill, color: Colors.pink[200], size: 12),
            Icon(CupertinoIcons.star_fill, color: Colors.pink[200], size: 12),
          ],
        ),
        const SizedBox(height: 10),
        SizedBox(
          width: 300,
          // height: 500,
          child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              children: [
                TextSpan(text: txt, style: GoogleFonts.aBeeZee(fontSize: 10)),
                // TextSpan(text: '😍', style: TextStyle()),
                // TextSpan(text: '😍🎂', style: TextStyle()),
              ],
            ),
          ),
        ),
      ],
    );
  }

  tabletCarousal(String img, String txt) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Image(fit: BoxFit.cover, image: AssetImage(img)),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Container(
            color: Colors.blueGrey.shade50,
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  SizedBox(
                    width: 140,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(
                          size: 10,
                          CupertinoIcons.star_fill,
                          color: Colors.pink[200],
                        ),
                        Icon(
                          CupertinoIcons.star_fill,
                          size: 10,
                          color: Colors.pink[200],
                        ),
                        Icon(
                          CupertinoIcons.star_fill,
                          size: 10,
                          color: Colors.pink[200],
                        ),
                        Icon(
                          CupertinoIcons.star_fill,
                          size: 10,
                          color: Colors.pink[200],
                        ),
                        Icon(
                          CupertinoIcons.star_fill,
                          size: 10,
                          color: Colors.pink[200],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      controller: scrlCtrl,
                      child: GestureDetector(
                        onVerticalDragUpdate: (details) {
                          // Handle horizontal drag here if needed
                          // You can use details.primaryDelta to get the horizontal movement
                          try {
                            if (details.primaryDelta != null) {
                              scrlCtrl.position.pointerScroll(
                                -details.primaryDelta!,
                              );
                            }
                          } catch (e) {
                            debugPrint(e.toString());
                          }
                        },
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: txt,
                                style: GoogleFonts.aBeeZee(
                                  wordSpacing: 3,
                                  fontSize: 8,
                                ),
                              ),
                              // TextSpan(text: '😍', style: TextStyle()),
                              // TextSpan(text: '😍🎂', style: TextStyle()),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
