/*     const SizedBox(
              height: 120,
              width: double.maxFinite,
              // color: Colors.pink[100],
              child: Center(
                child: Text(
                  'PAN INDIA CAKES',
                  style: TextStyle(
                    fontSize: 40,
                    fontWeight: FontWeight.bold,
                    // fontStyle: FontStyle.normal
                  ),
                ),
              ),
            ),
            SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 60),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  const Card(
                    elevation: 5,
                    shape: ContinuousRectangleBorder(),
                    // shape: BeveledRectangleBorder(
                    //     borderRadius: BorderRadius.all(Radius.circular(10))),
                    child: Column(
                      children: [
                        Image(
                            fit: BoxFit.cover,
                            width: 300,
                            height: 300,
                            image: AssetImage(
                                'assets/images/IMG-20210125-WA0150.jpg')),
                        Text("Chocolate Marble Cake"),
                        Text("Rs.600"),
                      ],
                    ),
                  ),
                  const Card(
                    elevation: 5,
                    shape: ContinuousRectangleBorder(),
                    child: Column(
                      children: [
                        Image(
                            fit: BoxFit.cover,
                            width: 300,
                            height: 300,
                            image: AssetImage(
                                'assets/images/IMG-20210125-WA0150.jpg')),
                        Text("Chocolate Marble Cake"),
                        Text("Rs.600"),
                      ],
                    ),
                  ),
                  const Card(
                    elevation: 5,
                    shape: ContinuousRectangleBorder(),
                    child: Column(
                      children: [
                        Image(
                            fit: BoxFit.cover,
                            width: 300,
                            height: 300,
                            image: AssetImage(
                                'assets/images/IMG-20210125-WA0150.jpg')),
                        Text("Chocolate Marble Cake"),
                        Text("Rs.600"),
                      ],
                    ),
                  ),
                  const Card(
                    elevation: 5,
                    shape: ContinuousRectangleBorder(),
                    child: Column(
                      children: [
                        Image(
                            fit: BoxFit.cover,
                            width: 300,
                            height: 300,
                            image: AssetImage(
                                'assets/images/IMG-20210125-WA0150.jpg')),
                        Text("Chocolate Marble Cake"),
                        Text("Rs.600"),
                      ],
                    ),
                  ),
                  const Card(
                    elevation: 5,
                    shape: ContinuousRectangleBorder(),
                    child: Column(
                      children: [
                        Image(
                            fit: BoxFit.cover,
                            width: 300,
                            height: 300,
                            image: AssetImage(
                                'assets/images/IMG-20210125-WA0150.jpg')),
                        Text("Chocolate Marble Cake"),
                        Text("Rs.600"),
                      ],
                    ),
                  ),
                  const SizedBox(
                    width: 70,
                  ),
                  Card(
                    elevation: 0,
                    // shape: ContinuousRectangleBorder(),
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ElevatedButton(
                            onPressed: () {},
                            style: ButtonStyle(
                              backgroundColor:
                                  MaterialStatePropertyAll(Colors.pink[300]),
                              elevation: const MaterialStatePropertyAll(0),
                              shape: const MaterialStatePropertyAll(
                                  LinearBorder(side: BorderSide.none)),
                            ),
                            child: const Text(
                              "VIEW ALL",
                              style: TextStyle(color: Colors.white),
                            ),
                          )
                        ]),
                  ),
                ],
              ),
            ), */