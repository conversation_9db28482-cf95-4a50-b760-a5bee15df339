import 'package:flutter/material.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';

class CircleIcons extends StatelessWidget {
  const CircleIcons({
    super.key,
    required this.scrollCtrl,
    required this.globalkey,
    required this.globalkey1,
    required this.globalkey2,
    required this.globalkey3,
  });
  final ScrollController scrollCtrl;
  final GlobalKey globalkey;
  final GlobalKey globalkey1;
  final GlobalKey globalkey2;
  final GlobalKey globalkey3;

  @override
  Widget build(BuildContext context) {
    return ResponsiveWid(
      //mobile
      mobile: Padding(
        padding: const EdgeInsets.only(top: 12.0, bottom: 5),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                InkWell(
                  onTap: () async {
                    // context.go('${Routes.allproducts}/Regular Cake');
                    const dura = Duration(milliseconds: 40);
                    await scrollCtrl.animateTo(
                      120,
                      duration: dura,
                      curve: Curves.linear,
                    );
                    await Future.delayed(dura);
                    await Future.delayed(const Duration(milliseconds: 10));
                    await Scrollable.ensureVisible(
                      globalkey.currentContext!,
                      curve: Curves.decelerate,
                      alignment: 0.12,
                      duration: const Duration(milliseconds: 300),
                    );
                  },
                  child: const Column(
                    children: [
                      CircleAvatar(
                        maxRadius: 40,
                        backgroundImage: AssetImage(
                          "assets/images/cakecircle.jpeg",
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        "Regular Cakes",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () async {
                    // context.go('${Routes.allproducts}/Trending Cake');
                    const dura = Duration(milliseconds: 40);
                    await scrollCtrl.animateTo(
                      120,
                      duration: dura,
                      curve: Curves.linear,
                    );
                    await Future.delayed(dura);
                    await Future.delayed(const Duration(milliseconds: 10));
                    await Scrollable.ensureVisible(
                      globalkey1.currentContext!,
                      alignment: 0.12,
                      curve: Curves.decelerate,
                      duration: const Duration(milliseconds: 450),
                    );
                  },
                  child: const Column(
                    children: [
                      CircleAvatar(
                        backgroundImage: AssetImage(
                          "assets/images/cupcakecircle.jpeg",
                        ),
                        maxRadius: 40,
                      ),
                      SizedBox(height: 8),
                      Text(
                        "Trending Cakes",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () async {
                    // context.go('${Routes.allproducts}/Add-On');
                    const dura = Duration(milliseconds: 40);
                    await scrollCtrl.animateTo(
                      120,
                      duration: dura,
                      curve: Curves.linear,
                    );
                    await Future.delayed(dura);
                    await Future.delayed(const Duration(milliseconds: 10));
                    await Scrollable.ensureVisible(
                      globalkey2.currentContext!,
                      curve: Curves.decelerate,
                      alignment: 0.12,
                      duration: const Duration(milliseconds: 600),
                    );
                  },
                  child: const Column(
                    children: [
                      CircleAvatar(
                        backgroundImage: AssetImage(
                          "assets/images/dessertcircle3.jpeg",
                        ),
                        maxRadius: 40,
                      ),
                      SizedBox(height: 8),
                      Text(
                        "Our Collection",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () async {
                    // context.go('${Routes.allproducts}/Flower');
                    const dura = Duration(milliseconds: 40);
                    await scrollCtrl.animateTo(
                      120,
                      duration: dura,
                      curve: Curves.linear,
                    );
                    await Future.delayed(dura);
                    await Future.delayed(const Duration(milliseconds: 10));
                    await Scrollable.ensureVisible(
                      globalkey3.currentContext!,
                      curve: Curves.decelerate,
                      alignment: 0.12,
                      duration: const Duration(milliseconds: 800),
                    );
                  },
                  child: const Column(
                    children: [
                      CircleAvatar(
                        backgroundImage: AssetImage(
                          "assets/images/flowerscircle.jpeg",
                        ),
                        maxRadius: 40,
                      ),
                      SizedBox(height: 8),
                      Text(
                        "Flowers",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      tablet: const SizedBox(),
      desktop: const SizedBox(),
    );
  }
}
