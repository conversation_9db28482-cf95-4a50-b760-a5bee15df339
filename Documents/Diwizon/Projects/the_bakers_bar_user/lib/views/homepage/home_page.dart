import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/views/homepage/add_on.dart';
import 'package:the_bakers_bar_user/views/homepage/cakes_scroll_view.dart';
import 'package:the_bakers_bar_user/views/homepage/carousel_banner.dart';
import 'package:the_bakers_bar_user/views/homepage/circle_icons.dart';
import 'package:the_bakers_bar_user/views/homepage/clients_diary.dart';
import 'package:the_bakers_bar_user/views/homepage/discounoffer_scroll_view.dart';
import 'package:the_bakers_bar_user/views/homepage/shop_flowers_row.dart';
import 'package:the_bakers_bar_user/views/common/underline_bar.dart';
import 'package:the_bakers_bar_user/views/homepage/soc_row1.dart';
import 'package:the_bakers_bar_user/views/homepage/tc_scroll_view.dart';
import 'package:the_bakers_bar_user/wrapper.dart';

class Homepage extends StatefulWidget {
  const Homepage({super.key});

  @override
  State<Homepage> createState() => _HomepageState();
}

class _HomepageState extends State<Homepage> {
  // final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey globalkey = GlobalKey();
  final GlobalKey globalkey1 = GlobalKey();
  final GlobalKey globalkey2 = GlobalKey();
  final GlobalKey globalkey3 = GlobalKey();
  final GlobalKey globalkey4 = GlobalKey();
  final scrollCtrl = ScrollController();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Get.find<ProductCtrl>().wasOnPageNo = 0;
  }

  @override
  Widget build(BuildContext context) {
    CarouselSliderController buttonCarouselController =
        CarouselSliderController();
    final size = MediaQuery.sizeOf(context);
    return Wrapper(
      scrollController: scrollCtrl,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Padding(
          //   padding: const EdgeInsets.symmetric(vertical: 0.0),
          //   child: CircleIcons(
          //     globalkey: globalkey,
          //     globalkey1: globalkey1,
          //     globalkey2: globalkey2,
          //     globalkey3: globalkey3,
          //     scrollCtrl: scrollCtrl,
          //   ),
          // ),
          CarouselBanner(scrollCtrl: scrollCtrl, globalkey: globalkey),

          DiscountScrollView(key: globalkey4),
          Padding(
            key: globalkey,
            padding: const EdgeInsets.only(top: 50.0, bottom: 10),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20.0,
                  vertical: 10,
                ),
                child: Row(
                  mainAxisAlignment: size.width <= 510
                      ? MainAxisAlignment.start
                      : MainAxisAlignment.center,
                  children: [
                    Text(
                      'INSTANT CAKES',
                      style: GoogleFonts.crimsonPro(
                        fontSize: size.width <= 510 ? 24 : 42,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (size.width <= 510) const Spacer(),
                    if (size.width <= 510)
                      Card(
                        color: Colors.transparent,
                        shadowColor: Colors.transparent,
                        surfaceTintColor: Colors.transparent,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(0.0),
                              child: ElevatedButton(
                                onPressed: () {
                                  context.go(
                                    '${Routes.allproducts}/Instant Cake',
                                  );
                                },
                                style: ButtonStyle(
                                  backgroundColor: WidgetStatePropertyAll(
                                    Colors.pink[300],
                                  ),
                                  shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none),
                                  ),
                                ),
                                child: const Text(
                                  "VIEW ALL",
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          const UnderlineBar(),
          const SizedBox(height: 50),
          const CakesScrollView(),
          Padding(
            key: globalkey,
            padding: const EdgeInsets.only(top: 50.0, bottom: 10),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20.0,
                  vertical: 10,
                ),
                child: Row(
                  mainAxisAlignment: size.width <= 510
                      ? MainAxisAlignment.start
                      : MainAxisAlignment.center,
                  children: [
                    Text(
                      'REGULAR CAKES',
                      style: GoogleFonts.crimsonPro(
                        fontSize: size.width <= 510 ? 24 : 42,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (size.width <= 510) const Spacer(),
                    if (size.width <= 510)
                      Card(
                        color: Colors.transparent,
                        shadowColor: Colors.transparent,
                        surfaceTintColor: Colors.transparent,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(0.0),
                              child: ElevatedButton(
                                onPressed: () {
                                  context.go(
                                    '${Routes.allproducts}/Regular Cake',
                                  );
                                },
                                style: ButtonStyle(
                                  backgroundColor: WidgetStatePropertyAll(
                                    Colors.pink[300],
                                  ),
                                  shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none),
                                  ),
                                ),
                                child: const Text(
                                  "VIEW ALL",
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          const UnderlineBar(),
          const SizedBox(height: 50),
          const CakesScrollView(),
          Padding(
            key: globalkey1,
            padding: const EdgeInsets.only(top: 50.0, bottom: 10),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20.0,
                  vertical: 10,
                ),
                child: Row(
                  mainAxisAlignment: size.width <= 510
                      ? MainAxisAlignment.start
                      : MainAxisAlignment.center,
                  children: [
                    Text(
                      'TRENDING CAKES',
                      style: GoogleFonts.crimsonPro(
                        fontSize: size.width <= 510 ? 24 : 42,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (size.width <= 510) const Spacer(),
                    if (size.width <= 510)
                      Card(
                        color: Colors.transparent,
                        shadowColor: Colors.transparent,
                        surfaceTintColor: Colors.transparent,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 0.0,
                              ),
                              child: ElevatedButton(
                                onPressed: () {
                                  context.go(
                                    '${Routes.allproducts}/Trending Cake',
                                  );
                                },
                                style: ButtonStyle(
                                  backgroundColor: WidgetStatePropertyAll(
                                    Colors.pink[300],
                                  ),
                                  shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none),
                                  ),
                                ),
                                child: const Text(
                                  "VIEW ALL",
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          const UnderlineBar(),
          const SizedBox(height: 50),
          const TcScrollView(),
          // Padding(
          //   padding: const EdgeInsets.only(top: 50.0, bottom: 10),
          //   child: Center(
          //     child: Text(
          //       'DESSERTS',
          //       style: GoogleFonts.crimsonPro(
          //         fontSize: 42,
          //         fontWeight: FontWeight.w500,
          //       ),
          //     ),
          //   ),
          // ),
          // const UnderlineBar(),
          // const SizedBox(
          //   height: 50,
          // ),
          // const DessertScrollView(),
          Padding(
            key: globalkey2,
            padding: const EdgeInsets.only(top: 50.0, bottom: 10),
            child: Center(
              child: Text(
                textAlign: TextAlign.center,
                'SHOP OUR COLLECTION',
                style: GoogleFonts.crimsonPro(
                  fontSize: size.width <= 510 ? 24 : 40,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const UnderlineBar(),
          const SizedBox(height: 50),
          const SocRow1(),
          Padding(
            key: globalkey3,
            padding: const EdgeInsets.only(top: 50.0, bottom: 10),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20.0,
                  vertical: 10,
                ),
                child: Row(
                  mainAxisAlignment: size.width <= 510
                      ? MainAxisAlignment.start
                      : MainAxisAlignment.center,
                  children: [
                    Text(
                      'SHOP FLOWERS',
                      style: GoogleFonts.crimsonPro(
                        fontSize: size.width <= 510 ? 24 : 42,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (size.width <= 510) const Spacer(),
                    if (size.width <= 510)
                      Card(
                        color: Colors.transparent,
                        shadowColor: Colors.transparent,
                        surfaceTintColor: Colors.transparent,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(0.0),
                              child: ElevatedButton(
                                onPressed: () {
                                  context.go('${Routes.allproducts}/Flower');
                                },
                                style: ButtonStyle(
                                  backgroundColor: WidgetStatePropertyAll(
                                    Colors.pink[300],
                                  ),
                                  shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none),
                                  ),
                                ),
                                child: const Text(
                                  "VIEW ALL",
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          const UnderlineBar(),
          const SizedBox(height: 50),
          const ShopflowersRow(),
          Padding(
            padding: const EdgeInsets.only(top: 50.0, bottom: 10),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20.0,
                  vertical: 10,
                ),
                child: Row(
                  mainAxisAlignment: size.width <= 510
                      ? MainAxisAlignment.start
                      : MainAxisAlignment.center,
                  children: [
                    Text(
                      'ADD ON',
                      style: GoogleFonts.crimsonPro(
                        fontSize: size.width <= 510 ? 24 : 42,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (size.width <= 510) const Spacer(),
                    if (size.width <= 510)
                      Card(
                        color: Colors.transparent,
                        shadowColor: Colors.transparent,
                        surfaceTintColor: Colors.transparent,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(0.0),
                              child: ElevatedButton(
                                onPressed: () {
                                  context.go('${Routes.allproducts}/Add-On');
                                },
                                style: ButtonStyle(
                                  backgroundColor: WidgetStatePropertyAll(
                                    Colors.pink[300],
                                  ),
                                  shape: const WidgetStatePropertyAll(
                                    LinearBorder(side: BorderSide.none),
                                  ),
                                ),
                                child: const Text(
                                  "VIEW ALL",
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          const UnderlineBar(),
          const SizedBox(height: 50),
          const AddOn(),
          const SizedBox(height: 70),
          SizedBox(
            height: size.width <= 510 ? 160 : 450,
            child: const Stack(
              fit: StackFit.expand,
              children: [
                Image(
                  fit: BoxFit.cover,
                  width: double.maxFinite,
                  // image: AssetImage('assets/images/bannerpink.jpeg')),
                  image: AssetImage('assets/images/2-min.png'),
                ),
                // Align(
                //   alignment: const Alignment(-.8, 0.7),
                //   child: AnimatedButton(
                //     height: size.width <= 510 ? 30 : 35,
                //     width: size.width <= 510 ? 120 : 200,
                //     text: 'SHOP NOW',
                //     selectedBackgroundColor: Colors.pinkAccent.shade100,
                //     isReverse: true,
                //     selectedTextColor: Colors.white,
                //     transitionType: TransitionType.CENTER_LR_IN,
                //     backgroundColor: Colors.pink.shade200,
                //     onPress: () {},
                //     animatedOn: AnimatedOn.onHover,
                //     textStyle: TextStyle(fontSize: size.width <= 510 ? 14 : 20),
                //     animationDuration: const Duration(milliseconds: 300),
                //   ),
                // ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 40.0, bottom: 10),
            child: Center(
              child: Text(
                "CLIENT'S DIARY",
                style: GoogleFonts.crimsonPro(
                  fontSize: size.width <= 510 ? 24 : 42,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const UnderlineBar(),
          const SizedBox(height: 50),
          SizedBox(
            child: ClientsDiary(
              buttonCarouselController: buttonCarouselController,
            ),
          ),
          SizedBox(height: size.width <= 510 ? 50 : 120),
        ],
      ),
    );
  }
}
