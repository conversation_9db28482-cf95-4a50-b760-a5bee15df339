import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:the_bakers_bar_user/Models/productmodel.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';

class Flowerscard extends StatelessWidget {
  const Flowerscard({super.key, this.product});
  final ProductModel? product;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    // final designCost = Get.find<ProductCtrl>().designCost.firstWhereOrNull(
    //     (element) => element.docId == product?.designCostdocId);

    return GetBuilder<ProductCtrl>(
      builder: (_) {
        // List<ProductModel> productlistflower = [];

        return InkWell(
          onTap: () => context.go('${Routes.product}/${product?.docId}'),
          mouseCursor: SystemMouseCursors.click,
          child: Card(
            elevation: 0,
            color: themeColorLite,
            shape: const ContinuousRectangleBorder(),
            child: SizedBox(
              height: size.width <= mobileMinsize ? 300 : 460,
              width: size.width <= mobileMinsize ? 150 : 250,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AspectRatio(
                    aspectRatio: .75,
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.asset(
                          "assets/images/TBB-Fix-BG.png",
                          fit: BoxFit.cover,
                          // width: size.width <= mobileMinsize ? 150 : 250,
                          // height: size.width <= mobileMinsize ? 200 : 334,
                        ),
                        // Padding(
                        //   padding: const EdgeInsets.all(0.0),
                        //   child: Image(
                        //     // width: 300,
                        //     // height: 400,
                        //     fit: BoxFit.cover,
                        //     image: NetworkImage(product?.images.isEmpty ?? true
                        //         ? ""
                        //         : product?.images.first ?? ""),
                        //   ),
                        // ),
                        Image.network(
                          product?.images.isEmpty ?? true
                              ? ""
                              : product?.images.first ?? "",
                          fit: BoxFit.cover,
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 15, top: 5),
                    child: Text(
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      product?.name.toUpperCase() ?? "",
                      style: TextStyle(
                        fontSize: size.width <= mobileMinsize ? 12 : 14,
                        letterSpacing: 1,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 15),
                    child: Text(
                      overflow: TextOverflow.ellipsis,
                      product?.desc.trim() ?? "",
                      // ? "Rs.${product?.fixedprice.toString() ?? ""}"
                      // : "Rs.${designCost?.basePrice.toString() ?? ""}",
                      // "Rs.${product?.fixedprice}",
                      // "Rs.${designCost?.basePrice.toString() ?? ""}",
                      style: TextStyle(
                        color: const Color(0xff000000),
                        fontSize: size.width <= mobileMinsize ? 14 : 16,
                        // letterSpacing: 1,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 15, bottom: 10),
                    child: Text(
                      'Rs.${product?.fixedprice}',
                      // product?.designCostdocId ?? "",
                      style: TextStyle(
                        fontSize: size.width <= mobileMinsize ? 14 : 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
