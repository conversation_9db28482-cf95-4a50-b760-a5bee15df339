import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animated_button/flutter_animated_button.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart';
import 'package:the_bakers_bar_user/Models/cart_items.dart';
import 'package:the_bakers_bar_user/Models/delivery_charge_model.dart';
import 'package:the_bakers_bar_user/Models/flavourmodel.dart';
import 'package:the_bakers_bar_user/Models/productmodel.dart';
import 'package:the_bakers_bar_user/Models/settings.dart';
import 'package:the_bakers_bar_user/Models/weight_range_model.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/const.dart';
import 'package:the_bakers_bar_user/shared/firebase.dart';
import 'package:the_bakers_bar_user/shared/methods.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/views/homepage/display_card1.dart';
import 'package:the_bakers_bar_user/views/productpage/product_desc.dart';
import 'package:the_bakers_bar_user/views/productpage/product_flavour.dart';
import 'package:the_bakers_bar_user/views/productpage/product_weight.dart';
import 'package:the_bakers_bar_user/views/productpage/textfield_container.dart';
import 'package:url_launcher/url_launcher.dart';

class ProductDetails extends StatefulWidget {
  const ProductDetails({
    super.key,
    required this.isSmall,
    required this.product,
  });
  final bool isSmall;
  final ProductModel? product;

  @override
  State<ProductDetails> createState() => _ProductDetailsState();
}

class _ProductDetailsState extends State<ProductDetails> {
  bool dataLoading = false;
  bool typeis = true;
  num? selectedweight;
  num? indexcountweight = 0;
  String? selectedFlavourName = "";
  num? flavourprice;
  FlavourModel? selectedFlavour;
  bool ismidnight = false;
  // int time = 0;
  int time = DateTime.now().hour;
  bool isfirst = true;
  bool issecond = true;
  int selectedindexfixedtime = 0;
  int selectedindexstandarddel = 0;
  String? selectedArea;
  List<bool> onclicked = [true, false, false];
  List<int> radiolist = [0, 1, 2];
  int selectedradio = 0;
  DateTime? orderdate;
  num? price;
  WeightRangeModel? productlist;
  SettingsModel? settingsModel;
  final List<FlavourModel?> flrlist = [];
  List<DelchargeModel> delchargeModel = [];
  num standardprice = 0;
  TextEditingController ctrlMessage = TextEditingController();
  bool isintial75 = false;
  int todaySlots = 0;
  int tommorrowSlots = 0;
  int seletedDaySlots = 0;
  bool type = false;
  bool inDeal = false;
  String? timings;
  int regularHours = (Get.find<ProductCtrl>().settings?.regularTime ?? 0)
      .toInt();
  int designerHours = (Get.find<ProductCtrl>().settings?.designerTime ?? 0)
      .toInt();
  String originalPrice = "";

  flavourPrice() {
    flavourprice =
        flrlist
            .firstWhereOrNull(
              (element) => element?.name == (selectedFlavour?.name ?? ""),
            )
            ?.perKgPrice ??
        0;
  }

  redirect(String link) async {
    try {
      final url = Uri.parse(link);
      if (await canLaunchUrl(url)) {
        launchUrl(url);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void initState() {
    super.initState();
    dataLoading = true;
    setState(() {});
    selectedFlavourName = "";
    type =
        (widget.product?.type == "Regular Cake" ||
            widget.product?.type == "Add-On" ||
            widget.product?.type == "Flower")
        ? false
        : true;

    Map<String, List<String>> x = getTimeSlots(
      currentTime: DateTime.now(),
      isDesigner: type,
      kitchenTime: type ? designerHours : regularHours,
    );
    todaySlots = x["Today"]?.length ?? 0;

    tommorrowSlots = x["Tomorrow"]?.length ?? 0;
    seletedDaySlots = x["Custom"]?.length ?? 0;

    if (time >= (type ? 20 : 21)) {
      onclicked = [false, true, false];
    }

    productlist = Get.find<ProductCtrl>().weightRange.firstWhereOrNull(
      (element) => element.docId == widget.product?.weightRangeDocId,
    );
    for (var flr in widget.product?.flavour ?? []) {
      flrlist.add(
        Get.find<ProductCtrl>().flavours.firstWhereOrNull(
          (element) => element.docId == flr,
        ),
      );
    }
    selectedweight = productlist?.weightlist[0];
    settingsModel = Get.find<ProductCtrl>().settings;

    selectedFlavour = Get.find<ProductCtrl>().flavours.firstWhereOrNull(
      (element) => element.name == "Chocolate Truffle",
    );
    final designCost = Get.find<ProductCtrl>().designCost.firstWhereOrNull(
      (element) => element.docId == widget.product?.designCostdocId,
    );
    delchargeModel = Get.find<ProductCtrl>().settings!.delcharges;

    selectedFlavourName = selectedFlavour?.name;
    isintial75 = productlist?.weightlist.first == 0.75;
    flavourPrice();
    if (isintial75) indexcountweight = 1;
    if (isintial75) selectedweight = productlist?.weightlist[1];
    price = priceCalc(
      flavourprice,
      indexcountweight,
      designCost,
      selectedweight,
      widget.product?.fixedprice ?? 0,
      productlist?.weightlist[0] ?? 0,
    );
    typeis =
        widget.product!.type.contains("Flower") ||
        widget.product!.type.contains("Add-On");
    price = typeis ? widget.product?.fixedprice : price;
    print(price);
    originalPrice = price.toString();
    if (widget.product?.catNameList?.contains("discount") ?? false) {
      inDeal = true;
      price = num.tryParse(getFlashDealPrice(price ?? 0)) ?? 0;
    }
    dataLoading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    var buttonStyle = ButtonStyle(
      padding: WidgetStatePropertyAll(
        EdgeInsets.symmetric(horizontal: widget.isSmall ? 15 : 20),
      ),
      overlayColor: const WidgetStatePropertyAll(Color(0xff74c066)),
      elevation: const WidgetStatePropertyAll(0),
      shape: const WidgetStatePropertyAll(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
      ),
    );
    const buttonStyle2 = ButtonStyle(
      overlayColor: WidgetStateColor.transparent,
      backgroundColor: WidgetStatePropertyAll(Colors.transparent),
      shadowColor: WidgetStatePropertyAll(Colors.transparent),
    );
    return GetBuilder<ProductCtrl>(
      builder: (pctrl) {
        delchargeModel = pctrl.settings?.delcharges ?? [];
        final designCost = pctrl.designCost.firstWhereOrNull(
          (element) => element.docId == widget.product?.designCostdocId,
        );

        price = priceCalc(
          flavourprice,
          indexcountweight,
          designCost,
          selectedweight,
          widget.product?.fixedprice ?? 0,
          productlist?.weightlist[0] ?? 0,
        );
        typeis =
            widget.product!.type.contains("Flower") ||
            widget.product!.type.contains("Add-On");
        price = typeis ? widget.product?.fixedprice : price;
        originalPrice = price.toString();
        if (widget.product?.catNameList?.contains("discount") ?? false) {
          inDeal = true;
          price = num.tryParse(getFlashDealPrice(price ?? 0)) ?? 0;
        }

        return Expanded(
          child: Padding(
            padding: EdgeInsets.only(right: widget.isSmall ? 0.0 : 100.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const SizedBox(height: 40),
                    Icon(
                      CupertinoIcons.star_fill,
                      color: Colors.pink[200],
                      size: 16,
                    ),
                    Icon(
                      CupertinoIcons.star_fill,
                      color: Colors.pink[200],
                      size: 16,
                    ),
                    Icon(
                      CupertinoIcons.star_fill,
                      color: Colors.pink[200],
                      size: 16,
                    ),
                    Icon(
                      CupertinoIcons.star_fill,
                      color: Colors.pink[200],
                      size: 16,
                    ),
                    Icon(
                      CupertinoIcons.star_fill,
                      color: Colors.pink[200],
                      size: 16,
                    ),
                    const Spacer(),
                    if (size.width >= desktopMinSize)
                      Padding(
                        padding: const EdgeInsets.only(right: 15.0),
                        child: IconButton(
                          onPressed: () async {
                            final url = '${Uri.base}';
                            print(url);
                            // await Share.share('Check out this product $url');
                            await Clipboard.setData(ClipboardData(text: url));
                            showAppSnackBar("Copied to Clipboard!");
                            // SnackBar(content: Text('Copied to Clipboard!'));
                          },
                          icon: const Icon(Icons.share_outlined),
                        ),
                      ),
                  ],
                ),
                widget.isSmall
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        // mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            (widget.product?.name.toUpperCase() ?? ""),
                            style: GoogleFonts.quicksand(
                              fontSize: 25,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 5),
                          if (widget.product?.type == "Trending Cake")
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 8.0,
                              ),
                              child: OutlinedButton(
                                onPressed: () {
                                  redirect(widget.product?.hyperlink ?? "");
                                },
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 5,
                                  ),
                                  backgroundColor: themeColor.withOpacity(.05),
                                  side: const BorderSide(color: themeColor),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                ),
                                child: Text(
                                  'Product Link',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.pink[200],
                                    letterSpacing: 1,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              (widget.product?.name.toUpperCase() ?? ""),
                              style: GoogleFonts.quicksand(
                                fontSize: 25,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),

                          // InkWell(
                          //   onTap: () {
                          //     redirect(widget.product?.hyperlink ?? "");
                          //   },
                          //   child: Text((widget.product?.hyperlink ?? ""),
                          //       style: const TextStyle(fontSize: 14)),
                          // ),
                        ],
                      ),
                // Text(
                //   (widget.product?.name.toUpperCase() ?? ""),
                //   style: GoogleFonts.quicksand(
                //     fontSize: 25,
                //     fontWeight: FontWeight.bold,
                //   ),
                // ),
                if (widget.product?.desc != null && widget.product?.desc != "")
                  ProductDesc(product: widget.product),
                if ((!widget.isSmall &&
                    widget.product?.type == "Trending Cake"))
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                    child: OutlinedButton(
                      onPressed: () {
                        redirect(widget.product?.hyperlink ?? "");
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 5,
                        ),
                        backgroundColor: themeColor.withOpacity(.05),
                        side: const BorderSide(color: themeColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                      child: Text(
                        'Preview',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.pink[200],
                          letterSpacing: 1,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                // Text(widget.product?.tags.toString() ?? ""),
                dataLoading
                    ? Padding(
                        padding: const EdgeInsets.symmetric(vertical: 20),
                        child: SizedBox(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(),
                        ),
                      )
                    : Row(
                        children: [
                          if (inDeal) ...[
                            Text(
                              "Rs.$originalPrice",
                              // ? "Rs.${product?.fixedprice.toString() ?? ""}"
                              // : "Rs.${designCost?.basePrice.toString() ?? ""}",
                              // "Rs.${product?.fixedprice}",
                              // "Rs.${designCost?.basePrice.toString() ?? ""}",
                              style: GoogleFonts.roboto(
                                decoration: TextDecoration.lineThrough,
                                color: const Color.fromARGB(255, 126, 126, 126),
                                fontSize: 32,
                                letterSpacing: 1,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(width: 5),
                          ],
                          Text(
                            "Rs.$price",

                            // "Rs.${designCost?.basePrice.toString() ?? ""}",
                            style: GoogleFonts.roboto(
                              letterSpacing: 1,
                              fontSize: 40,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                Text(
                  "Tax & Shipping will be charged extra",
                  style: GoogleFonts.quicksand(
                    color: Colors.grey[600],
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                if (!typeis && widget.product?.type != "Regular Cake")
                  Text(
                    "Flavour",
                    style: GoogleFonts.quicksand(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                if (!typeis) const SizedBox(height: 10),
                if (!(typeis) && (widget.product?.type != "Regular Cake"))
                  ProductFlavour(
                    product: widget.product,
                    onChanged: (flavourModel) {
                      selectedFlavour = flavourModel;
                      selectedFlavourName = flavourModel.name;
                      flavourPrice();
                      setState(() {});
                    },
                    isSmall: widget.isSmall,
                  ),
                if (!typeis)
                  Text(
                    "WEIGHT",
                    style: GoogleFonts.quicksand(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                if (!typeis)
                  ProductWeight(
                    productlist: productlist,
                    onChanged: (selectedwieghtindex, indexcount) {
                      selectedweight = selectedwieghtindex;
                      indexcountweight = indexcount;
                      setState(() {});
                    },
                  ),
                if (!typeis) const SizedBox(height: 0),
                if (selectedFlavour != null)
                  Container(
                    // width: 400,
                    // height: 600,
                    decoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      borderRadius: BorderRadius.circular(10),
                      color: const Color(0xfffbedbd),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: widget.isSmall ? 30 : 0.0,
                        vertical: 20,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(
                                left: widget.isSmall ? 0 : 20.0,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Select Date and Time",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  const SizedBox(height: 5),
                                  Row(
                                    // mainAxisAlignment:
                                    // MainAxisAlignment.spaceBetween,
                                    // crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      if (time < (type ? 20 : 21))
                                        ElevatedButton(
                                          style: buttonStyle.copyWith(
                                            backgroundColor:
                                                WidgetStatePropertyAll(
                                                  onclicked[0]
                                                      ? const Color(0xff74c066)
                                                      : const Color(0xff7f7f7f),
                                                ),
                                          ),
                                          onPressed: () {
                                            onclicked = [true, false, false];
                                            setState(() {});
                                          },
                                          child: Text(
                                            "Today",
                                            style: TextStyle(
                                              color: onclicked[0]
                                                  ? Colors.white
                                                  : Colors.black,
                                            ),
                                          ),
                                        ),
                                      if (time < (type ? 20 : 21))
                                        const SizedBox(width: 5),
                                      ElevatedButton(
                                        style: buttonStyle.copyWith(
                                          backgroundColor:
                                              WidgetStatePropertyAll(
                                                onclicked[1]
                                                    ? const Color(0xff74c066)
                                                    : const Color(0xff7f7f7f),
                                              ),
                                        ),
                                        onPressed: () {
                                          onclicked = [false, true, false];

                                          setState(() {});
                                        },
                                        child: Text(
                                          "Tomorrow",
                                          style: TextStyle(
                                            color: onclicked[1]
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 5),
                                      ElevatedButton(
                                        style: buttonStyle.copyWith(
                                          backgroundColor:
                                              WidgetStatePropertyAll(
                                                onclicked[2]
                                                    ? const Color(0xff74c066)
                                                    : const Color(0xff7f7f7f),
                                              ),
                                        ),
                                        onPressed: () async {
                                          setState(() {});
                                          onclicked = [false, false, true];

                                          await showDatePicker(
                                            context: context,
                                            helpText: "Select Date",
                                            initialDate: DateTime(
                                              DateTime.now().year,
                                              DateTime.now().month,
                                              DateTime.now().day + 2,
                                            ),
                                            firstDate: DateTime(
                                              DateTime.now().year,
                                              DateTime.now().month,
                                              DateTime.now().day + 2,
                                            ),
                                            lastDate: DateTime.now().add(
                                              const Duration(days: 90),
                                            ),
                                          ).then((value) async {
                                            orderdate = value;
                                          });

                                          setState(() {});
                                        },
                                        child: Text(
                                          "Calender",
                                          style: TextStyle(
                                            color: onclicked[2]
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (orderdate != null && onclicked[2])
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 10.0,
                                      ),
                                      child: Row(
                                        children: [
                                          const Text("Delivery Options for:"),
                                          const SizedBox(width: 5),
                                          Text(
                                            formatDate(
                                              orderdate ?? DateTime.now(),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  const SizedBox(height: 10),
                                  if ((widget.product?.type ==
                                              "Designer Cake" ||
                                          widget.product?.type ==
                                              "Trending Cake")
                                      ? time < 20
                                      : time < 21)
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        RadioMenuButton(
                                          toggleable: true,
                                          style: buttonStyle2,
                                          groupValue: selectedradio,
                                          value: radiolist[0],
                                          onChanged: (newValue) {
                                            selectedradio =
                                                newValue ?? radiolist[0];
                                            ismidnight = false;
                                            setState(() {});
                                          },
                                          child: const Row(
                                            children: [
                                              Text("Standard Delivery"),
                                              // Text(
                                              //     "Rs.${(settingsModel?.fixtimedelivery)! + standardprice}"),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  if (selectedradio == radiolist[0])
                                    if (onclicked[0]
                                        ? (todaySlots == 0 ? false : true)
                                        : true)
                                      Wrap(
                                        children: List.generate(
                                          onclicked[0]
                                              ? todaySlots
                                              : onclicked[1]
                                              ? tommorrowSlots
                                              : seletedDaySlots,
                                          (index) {
                                            String showTime;
                                            List<String> display;
                                            if (onclicked[0]) {
                                              final x = getTimeSlots(
                                                currentTime: DateTime.now(),
                                                isDesigner: type,
                                                kitchenTime: type
                                                    ? designerHours
                                                    : regularHours,
                                              );
                                              showTime =
                                                  x['Today']?[index] ?? "";
                                              display = x['Today'] ?? [];
                                              if (index == 0 &&
                                                  selectedindexfixedtime == 0) {
                                                timings = display[0];
                                              }
                                            } else if (onclicked[1]) {
                                              final x = getTimeSlots(
                                                currentTime: DateTime.now(),
                                                isDesigner: type,
                                                kitchenTime: type ? 2 : 1,
                                              );
                                              showTime =
                                                  x['Tomorrow']?[index] ?? "";
                                              display = x['Tomorrow'] ?? [];
                                              if (index == 0 &&
                                                  selectedindexfixedtime == 0) {
                                                timings = display[0];
                                              }
                                            } else {
                                              final x = getTimeSlots(
                                                currentTime: DateTime.now(),
                                                isDesigner: type,
                                                kitchenTime: type ? 2 : 1,
                                              );
                                              showTime =
                                                  x['Custom']?[index] ?? "";
                                              display = x['Custom'] ?? [];
                                              if (index == 0 &&
                                                  selectedindexfixedtime == 0) {
                                                timings = display[0];
                                              }
                                            }

                                            return Padding(
                                              padding: const EdgeInsets.only(
                                                right: 10,
                                                bottom: 10,
                                                top: 10,
                                              ),
                                              child: OutlinedButton(
                                                onPressed: () {
                                                  setState(() {
                                                    selectedindexfixedtime =
                                                        index;
                                                    issecond = false;
                                                    timings = display[index];
                                                  });
                                                },
                                                style: OutlinedButton.styleFrom(
                                                  padding: const EdgeInsets.all(
                                                    10,
                                                  ),
                                                  backgroundColor:
                                                      index ==
                                                          selectedindexfixedtime
                                                      ? const Color(0xff74c066)
                                                      : issecond && index == 0
                                                      ? const Color(0xff74c066)
                                                      : Colors.transparent,
                                                  // side: BorderSide(
                                                  //     color: index == selectedindex
                                                  //         ? themeColor
                                                  //         : Colors.grey.shade600),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          3,
                                                        ),
                                                  ),
                                                ),
                                                child: Text(
                                                  showTime,
                                                  style: TextStyle(
                                                    color:
                                                        index ==
                                                            selectedindexfixedtime
                                                        ? Colors.white
                                                        : issecond && index == 0
                                                        ? Colors.white
                                                        : Colors.black,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                  if (onclicked[0]
                                      ? (todaySlots == 0 ? true : false)
                                      : false)
                                    Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: Text("No Avialable Slots"),
                                    ),
                                  if (((widget.product?.type ==
                                                  "Designer Cake" ||
                                              widget.product?.type ==
                                                  "Trending Cake")
                                          ? time < 20
                                          : time < 21) ||
                                      !onclicked[0])
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        RadioMenuButton(
                                          toggleable: true,
                                          style: buttonStyle2,
                                          groupValue: selectedradio,
                                          value: radiolist[2],
                                          onChanged: (newValue) {
                                            selectedradio =
                                                newValue ?? radiolist[2];
                                            ismidnight = true;
                                            timings = "11PM-12AM";
                                            // startTime = "11PM";
                                            // endTime = "12AM";
                                            setState(() {});
                                          },
                                          child: Row(
                                            children: [
                                              size.width >= 510
                                                  ? const Text(
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                      ),
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      "Mid Night Delivery - Additional Charge of Rs.",
                                                    )
                                                  : Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Text(
                                                          "Mid Night Delivery - ",
                                                        ),
                                                        Row(
                                                          children: [
                                                            const Text(
                                                              "(Additional Charge of Rs.",
                                                            ),
                                                            Text(
                                                              "${((settingsModel?.midnightdelivery)!).toString()})",
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                              if (size.width >= 510)
                                                Text(
                                                  "${((settingsModel?.midnightdelivery)!).toString()} ",
                                                ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  if (selectedradio == radiolist[2])
                                    OutlinedButton(
                                      onPressed: () {},
                                      style: OutlinedButton.styleFrom(
                                        backgroundColor: const Color(
                                          0xff74c066,
                                        ),
                                        // side: BorderSide(
                                        //     color: index == selectedindex
                                        //         ? themeColor
                                        //         : Colors.grey.shade600),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            3,
                                          ),
                                        ),
                                      ),
                                      child: const Text(
                                        '11PM-12AM',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                          // : const Expanded(child: SizedBox()),
                        ],
                      ),
                    ),
                  ),
                // if (!typeis)
                //   const SizedBox(
                //     height: 10,
                //   ),
                if (!typeis)
                  Row(
                    children: [
                      Expanded(
                        child: TextFieldContainer(ctrlMessage: ctrlMessage),
                      ),
                      // if (!widget.isSmall) const Spacer()
                    ],
                  ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Expanded(
                      child: AnimatedButton(
                        animationDuration: const Duration(milliseconds: 300),
                        text: "ADD TO CART",
                        animatedOn: AnimatedOn.onHover,
                        transitionType: TransitionType.RIGHT_TO_LEFT,
                        backgroundColor: Colors.black,
                        selectedBackgroundColor: Colors.pinkAccent,
                        selectedTextColor: Colors.black,
                        onPress: () async {
                          try {
                            if ((timings == null)) {
                              return showAppSnackBar("Select Time Slot!");
                            }

                            if (typeis || selectedFlavour != null) {
                              // print(selectedFlavour);

                              if (checkCart(
                                (widget.product?.type == "Designer Cake") ||
                                    (widget.product?.type == "Trending Cake"),
                              )) {
                                final cartdata = <String, dynamic>{
                                  getRandomId(6): {
                                    "pId": widget.product?.docId,
                                    "name": widget.product?.name,
                                    "delivery": radiolist[0] == selectedradio
                                        ? "Standard Delivery"
                                        : "Midnight Delivery",
                                    "selectedflavour":
                                        widget.product?.type == "Regular Cake"
                                        ? ""
                                        : selectedFlavourName,
                                    "weightSelected": selectedweight.toString(),
                                    "deliveryTime": timings,
                                    // "deliveryTime": "$startTime-$endTime",
                                    "price":
                                        //] price,
                                        typeis
                                        ? widget.product?.fixedprice
                                        : price,

                                    "messageOnCake": ctrlMessage.text,
                                    // "area": selectedArea,
                                    "delDate": onclicked[0]
                                        ? DateTime.now()
                                              .toString()
                                              .split(" ")
                                              .first
                                        : onclicked[1]
                                        ? DateTime.now()
                                              .add(const Duration(days: 1))
                                              .toString()
                                              .split(" ")
                                              .first
                                        : orderdate.toString().split(" ").first,
                                    "isLateNight": ismidnight,
                                    'qty': 1,
                                  },
                                };

                                if (!isLoggedIn()) {
                                  pctrl.orderProduct.addAll(
                                    Map.castFrom(cartdata).entries
                                        .map(
                                          (e) => CartItemsModel.fromJson(
                                            e.key,
                                            e.value,
                                          ),
                                        )
                                        .toList(),
                                  );
                                }
                                if (isLoggedIn()) {
                                  Map<String, dynamic> tempMap = {};
                                  // print(cartdata);
                                  DocumentSnapshot documentSnapshot =
                                      await FBFireStore.users
                                          .doc(FBAuth.auth.currentUser?.uid)
                                          .get();
                                  if (documentSnapshot.exists) {
                                    tempMap = documentSnapshot.get('cartitems');

                                    tempMap.addAll(cartdata);
                                  } else {
                                    print('Document does not exist.');
                                  }

                                  //  await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).snapshots().listen((event) {
                                  // CartItemsModel temp = CartItemsModel.fromJson(event.data()!);

                                  // });
                                  await FBFireStore.users
                                      .doc(FBAuth.auth.currentUser?.uid)
                                      .update({"cartitems": tempMap});
                                }
                                if (context.mounted) {
                                  context.go(Routes.cart);
                                }
                              } else {
                                Fluttertoast.showToast(
                                  msg: "Do youu want to add in quanitity",
                                );
                              }
                            } else {
                              if (selectedFlavour == null) {
                                Fluttertoast.showToast(
                                  msg: "Please Select Flavour",
                                );
                                return;
                              }
                              if (selectedArea == null) {
                                Fluttertoast.showToast(
                                  msg: "Please Select area",
                                );
                                return;
                              }
                            }
                          } catch (e) {
                            debugPrint(e.toString());
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  bool checkCart(bool isDesigner) {
    if (isLoggedIn() &&
        (Get.find<ProductCtrl>().userDetails?.cartitems.isNotEmpty ?? false)) {
      return Get.find<ProductCtrl>().userDetails!.cartitems.where((element) {
        // print(element.deliveryTime);
        // print("$startTime-$endTime");
        return (element.pId == widget.product?.docId &&
            (element.delDate.toString().split(" ").first ==
                (onclicked[0]
                    ? DateTime.now().toString().split(" ").first
                    : onclicked[1]
                    ? DateTime.now()
                          .add(const Duration(days: 1))
                          .toString()
                          .split(" ")
                          .first
                    : orderdate.toString().split(" ").first)) &&
            (element.delivery ==
                (radiolist[0] == selectedradio
                    ? "Standard Delivery"
                    : "Midnight Delivery")) &&
            ((!isDesigner)
                ? true
                : (element.selectedflavour == selectedFlavourName)) &&
            element.weightSelected == selectedweight.toString() &&
            element.deliveryTime == timings &&
            element.isLateNight == ismidnight);
      }).isEmpty;
    } else if ((!isLoggedIn()) &&
        Get.find<ProductCtrl>().orderProduct.isNotEmpty) {
      return Get.find<ProductCtrl>().orderProduct.where((element) {
        return (element.pId == widget.product?.docId &&
            (element.delDate.toString().split(" ").first ==
                (onclicked[0]
                    ? DateTime.now().toString().split(" ").first
                    : onclicked[1]
                    ? DateTime.now()
                          .add(const Duration(days: 1))
                          .toString()
                          .split(" ")
                          .first
                    : orderdate.toString().split(" ").first)) &&
            (element.delivery ==
                (radiolist[0] == selectedradio
                    ? "Standard Delivery"
                    : "Midnight Delivery")) &&
            ((!isDesigner)
                ? true
                : (element.selectedflavour == selectedFlavourName)) &&
            element.weightSelected == selectedweight.toString() &&
            element.deliveryTime == timings &&
            element.isLateNight == ismidnight);
      }).isEmpty;
    } else {
      return true;
    }
  }
}
