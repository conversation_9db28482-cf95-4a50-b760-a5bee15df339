import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:readmore/readmore.dart';
import 'package:the_bakers_bar_user/Models/productmodel.dart';

class ProductDesc extends StatelessWidget {
  const ProductDesc({super.key, this.product});
  final ProductModel? product;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0, bottom: 10),
      child: ReadMoreText(
        product?.desc ?? "",
        trimMode: TrimMode.Line,
        trimLines: 2,
        trimCollapsedText: 'Read more',
        moreStyle: const TextStyle(color: Colors.pink),
        trimExpandedText: 'Read less',
        lessStyle: const TextStyle(color: Colors.pink),
        style: GoogleFonts.quicksand(fontSize: 15),
      ),
    );
  }
}
