import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:the_bakers_bar_user/Models/productmodel.dart';
import 'package:the_bakers_bar_user/shared/methods.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';

class ProductImage extends StatelessWidget {
  const ProductImage({super.key, required this.product});
  final ProductModel? product;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    // print(product?.weightRangeDocId);
    return Padding(
      padding: EdgeInsets.only(
        left: size.width <= mobileMinsize
            ? 10
            : size.width <= desktopMinSize
            ? 40
            : 60.0,
        right: size.width <= mobileMinsize
            ? 10
            : size.width <= desktopMinSize
            ? 20
            : 30,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: size.width <= mobileMinsize
              ? 300
              : size.width <= desktopMinSize
              ? 450
              : 700,
        ),
        // height: size.width <= mobileMinsize ? 300 : 800,
        child: AspectRatio(
          aspectRatio: 4 / 5,
          child: InkWell(
            hoverColor: Colors.transparent,
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            onLongPress: () async {
              // final url = '${Uri.base}';
              // print(url);
              // await Share.share('Check out this product $url');
              await Clipboard.setData(
                ClipboardData(text: product?.images.first ?? ""),
              );
              // showAppSnackBar("Url copied");
              // SnackBar(content: Text('Copied to Clipboard!'));
            },
            child: Card(
              clipBehavior: Clip.antiAlias,
              shape: RoundedRectangleBorder(
                side: const BorderSide(color: themeColor, width: 2),
                borderRadius: BorderRadius.circular(
                  size.width <= mobileMinsize ? 15 : 25,
                ),
              ),
              // decoration: BoxDecoration(borderRadius: BorderRadius.circular(20)),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Image.asset(
                    "assets/images/TBB-Fix-BG.png",
                    fit: BoxFit.cover,
                    // width: size.width <= mobileMinsize ? 150 : 250,
                    // height: size.width <= mobileMinsize ? 200 : 334,
                  ),
                  // Image.asset(
                  //   "assets/images/TBB-Fix-BG.png",
                  //   fit: BoxFit.cover,
                  //   // height: size.width <= mobileMinsize ? 300 : 800,
                  //   // width: size.width <= mobileMinsize ? 240 : 640,
                  // ),
                  // Padding(
                  //   padding: const EdgeInsets.all(0.0),
                  //   child: Image(
                  //     // width: 300,
                  //     // height: 400,
                  //     fit: BoxFit.cover,
                  //     image: NetworkImage(product?.images.isEmpty ?? true
                  //         ? ""
                  //         : product?.images.first ?? ""),
                  //   ),
                  // ),
                  Image.network(
                    product?.images.isEmpty ?? true
                        ? ""
                        : product?.images.first ?? "",
                    fit: BoxFit.cover,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    //  Padding(
    //   padding: const EdgeInsets.symmetric(horizontal: 60.0),
    //   child: AspectRatio(
    //     aspectRatio: 4 / 5,
    //     child: SizedBox(
    //       // height: size.width >= 1850 ? 1000 : null,
    //       child: Card(
    //         clipBehavior: Clip.antiAlias,
    //         shape:
    //             RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
    //         // decoration: BoxDecoration(borderRadius: BorderRadius.circular(20)),
    //         child: Image.network(fit: BoxFit.cover, product!.images.first),
    //         //     Image(
    //         //   fit: BoxFit.cover,
    //         // height: 700,
    //         //   image: AssetImage('assets/images/image1.png'),
    //         // ),
    //       ),
    //     ),
    //   ),
    // );
  }
}
