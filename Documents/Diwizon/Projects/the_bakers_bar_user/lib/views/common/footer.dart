import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:the_bakers_bar_user/shared/methods.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:url_launcher/url_launcher.dart';

class Footer extends StatelessWidget {
  const Footer({super.key, required Size size});
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final small = size.width < 850;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: small ? 20 : size.width * .07,
        vertical: 40,
      ),
      color: const Color(0xfffcedec),
      // color: themeColor,
      child: Column(
        children: [
          small
              ? const _VerticalView()
              : const Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: _Social()),
                    SizedBox(width: 8),
                    Expanded(child: _Account()),
                    Expanded(child: _Services()),
                    Expanded(child: _Company()),
                  ],
                ),
          const SizedBox(height: 40),
          const _CopyRight(),
        ],
      ),
    );
  }
}

class _VerticalView extends StatefulWidget {
  const _VerticalView();

  @override
  State<_VerticalView> createState() => _VerticalViewState();
}

class _VerticalViewState extends State<_VerticalView> {
  int selected = -1;

  @override
  Widget build(BuildContext context) {
    return Column(
      key: const ValueKey('col'),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _Social(),
        const SizedBox(height: 20),
        Builder(
          key: Key('builder $selected'),
          builder: (context) {
            return ListView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                ExpansionTile(
                  tilePadding: EdgeInsets.zero,
                  shape: const RoundedRectangleBorder(),
                  collapsedShape: const RoundedRectangleBorder(),
                  title: const _AccountHeader(),
                  collapsedIconColor: const Color(0xff5a4b4b),
                  expandedCrossAxisAlignment: CrossAxisAlignment.start,
                  expandedAlignment: Alignment.centerLeft,
                  childrenPadding: EdgeInsets.zero,
                  key: const ValueKey(0),
                  initiallyExpanded: 0 == selected,
                  onExpansionChanged: (value) {
                    setState(() {
                      value ? selected = 0 : selected = -1;
                    });
                  },
                  children: const [_AccountButtons()],
                ),
                ExpansionTile(
                  tilePadding: EdgeInsets.zero,
                  shape: const RoundedRectangleBorder(),
                  collapsedShape: const RoundedRectangleBorder(),
                  title: const _ServiceHeader(),
                  collapsedIconColor: const Color(0xff5a4b4b),
                  expandedCrossAxisAlignment: CrossAxisAlignment.start,
                  expandedAlignment: Alignment.centerLeft,
                  childrenPadding: EdgeInsets.zero,
                  key: const ValueKey(1),
                  initiallyExpanded: 1 == selected,
                  onExpansionChanged: (value) {
                    setState(() {
                      value ? selected = 1 : selected = -1;
                    });
                  },
                  children: const [_ServiceButtons()],
                ),
                ExpansionTile(
                  tilePadding: EdgeInsets.zero,
                  shape: const RoundedRectangleBorder(),
                  collapsedShape: const RoundedRectangleBorder(),
                  title: const _CompanyHeader(),
                  collapsedIconColor: const Color(0xff5a4b4b),
                  expandedCrossAxisAlignment: CrossAxisAlignment.start,
                  expandedAlignment: Alignment.centerLeft,
                  childrenPadding: EdgeInsets.zero,
                  key: const ValueKey(2),
                  initiallyExpanded: 2 == selected,
                  onExpansionChanged: (value) {
                    setState(() {
                      value ? selected = 2 : selected = -1;
                    });
                  },
                  children: const [_CompanyButtons()],
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}

class _Social extends StatelessWidget {
  const _Social();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'NARAYAN CHOITRAM AHUJA',
          style: TextStyle(
            color: Color(0xff5a4b4b),
            fontWeight: FontWeight.w900,
            fontSize: 16,
          ),
        ),
        SizedBox(height: 30),
        Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            const Text(
              "FOLLOW US ON INSTAGRAM",
              style: TextStyle(
                // letterSpacing: 1.2,
                // fontFamily: fontFamily2,
                color: Color(0xff5a4b4b),
                // color: Colors.white,
                fontWeight: FontWeight.w900,
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              onPressed: () => launchUrl(
                Uri.parse('https://www.facebook.com/thebakersbar09/'),
                mode: LaunchMode.externalApplication,
              ),
              icon: const Icon(Icons.facebook, color: Color(0xff5a4b4b)),
            ),
            const SizedBox(width: 5),
            InkWell(
              mouseCursor: SystemMouseCursors.click,
              onTap: () => launchUrl(
                Uri.parse('https://www.instagram.com/thebakersbar/'),
                mode: LaunchMode.externalApplication,
              ),
              child: const Image(
                color: Color(0xff5a4b4b),
                height: 25,
                width: 25,
                image: AssetImage('assets/images/instagram.png'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        const Text(
          "Catch the latest updates and be a part of our story.",
          style: TextStyle(color: Color(0xff5a4b4b)),
        ),
        /* const SizedBox(height: 20),
        InkWell(
          onTap: () async {
            try {
              launchUrlString(instaUrl, mode: LaunchMode.externalApplication);
            } catch (e) {
              debugPrint(e.toString());
            }
          },
          child: Image.asset(
            'assets/insta.png',
            height: 28,
            width: 28,
          ),
        ) */
      ],
    );
  }
}

class _Account extends StatelessWidget {
  const _Account();

  @override
  Widget build(BuildContext context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_AccountHeader(), SizedBox(height: 28), _AccountButtons()],
    );
  }
}

class _AccountButtons extends StatelessWidget {
  const _AccountButtons();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _FooterButton(
          name: isLoggedIn() ? "MY ACCOUNT" : "SIGN IN / REGISTER",
          callback: () {
            if (isLoggedIn()) {
              context.go(Routes.account);
            } else {
              context.go(Routes.auth);
              // context.go(Routes.auth, extra: Routes.overview);
            }
          },
        ),
        // const SizedBox(height: 12),
        // _FooterButton(name: "TRACK ORDER", callback: () {}),
        const SizedBox(height: 12),
        // _FooterButton(
        //     name: "ADDRESS BOOK",
        //     callback: () {
        //       context.go(isLoggedIn() ? Routes.addressbook : Routes.auth);
        //     }),
        // const SizedBox(height: 12),
        _FooterButton(
          name: "TRACK ORDER",
          callback: () {
            if (isLoggedIn()) {
              context.go(Routes.account);
            } else {
              context.go(Routes.auth);
            }
          },
        ),
        const SizedBox(height: 28),
      ],
    );
  }
}

class _AccountHeader extends StatelessWidget {
  const _AccountHeader();

  @override
  Widget build(BuildContext context) {
    return const Text(
      "ACCOUNTS",
      style: TextStyle(
        color: Color(0xff5a4b4b),
        fontWeight: FontWeight.w900,
        fontSize: 16,
      ),
    );
  }
}

class _Services extends StatelessWidget {
  const _Services();

  @override
  Widget build(BuildContext context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_ServiceHeader(), SizedBox(height: 28), _ServiceButtons()],
    );
  }
}

class _ServiceButtons extends StatelessWidget {
  const _ServiceButtons();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _FooterButton(
          name: "CONTACT US",
          callback: () {
            context.go(Routes.contact);
          },
        ),
        const SizedBox(height: 12),
        _FooterButton(
          name: "SHIPPING / DELIVERY",
          callback: () {
            context.go(Routes.shippingPolicy);
          },
        ),
        const SizedBox(height: 12),
        _FooterButton(
          name: "RETURNS & EXCHANGE",
          callback: () {
            context.go(Routes.returnandrefundPolicy);
          },
        ),
        const SizedBox(height: 12),
        _FooterButton(
          name: "REFUND POLICY",
          callback: () {
            context.go(Routes.refundPolicy);
          },
        ),
        /*  const SizedBox(height: 12),
        _FooterButton(
            name: "FAQS",
            callback: () {
              context.go(Routes.faqs);
            }), */
        const SizedBox(height: 28),
      ],
    );
  }
}

class _ServiceHeader extends StatelessWidget {
  const _ServiceHeader();

  @override
  Widget build(BuildContext context) {
    return const Text(
      "CUSTOMER SERVICE",
      style: TextStyle(
        color: Color(0xff5a4b4b),
        fontWeight: FontWeight.w900,
        fontSize: 16,
      ),
    );
  }
}

class _Company extends StatelessWidget {
  const _Company();

  @override
  Widget build(BuildContext context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_CompanyHeader(), SizedBox(height: 28), _CompanyButtons()],
    );
  }
}

class _CompanyButtons extends StatelessWidget {
  const _CompanyButtons();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // _FooterButton(
        //     name: "ABOUT",
        //     callback: () {
        //       context.go(Routes.about);
        //     }),
        // const SizedBox(height: 12),
        _FooterButton(
          name: "TERMS & CONDITIONS",
          callback: () {
            context.go(Routes.terms);
          },
        ),
        const SizedBox(height: 12),
        _FooterButton(
          name: "PRIVACY POLICY",
          callback: () {
            context.go(Routes.privacy);
          },
        ),
        const SizedBox(height: 28),
      ],
    );
  }
}

class _CompanyHeader extends StatelessWidget {
  const _CompanyHeader();

  @override
  Widget build(BuildContext context) {
    return const Text(
      "POLICIES",
      // "OUR COMPANY",
      style: TextStyle(
        color: Color(0xff5a4b4b),
        fontWeight: FontWeight.w900,
        fontSize: 16,
      ),
    );
  }
}

class _FooterButton extends StatefulWidget {
  const _FooterButton({required this.name, required this.callback});

  final String name;
  final Function callback;

  @override
  State<_FooterButton> createState() => _FooterButtonState();
}

class _FooterButtonState extends State<_FooterButton> {
  bool hovered = false;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => widget.callback(),
      onHover: (value) => setState(() => hovered = value),
      child: Text(
        widget.name,
        style: TextStyle(
          color: hovered ? themeColor : const Color(0xff5a4b4b),
          fontWeight: FontWeight.w900,
          fontSize: 14,
        ),
      ),
    );
  }
}

class _CopyRight extends StatelessWidget {
  const _CopyRight();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Icon(Icons.copyright_rounded, color: Color(0xff5a4b4b), size: 18),
        const SizedBox(width: 4),
        Text.rich(
          TextSpan(
            text: "2024 TBB developed by ",
            style: const TextStyle(
              fontWeight: FontWeight.w900,
              color: Color(0xff5a4b4b),
            ),
            children: [diwizonText()],
          ),
        ),
      ],
    );
  }
}

TextSpan diwizonText() {
  return TextSpan(
    text: "DIWIZON",
    style: const TextStyle(
      fontWeight: FontWeight.bold,
      color: Color(0xff5a4b4b),
    ),
    recognizer: TapGestureRecognizer()..onTap = onDiwizonTap,
  );
}

onDiwizonTap() async {
  try {
    final url = Uri.parse("https://www.diwizon.com");
    if (await canLaunchUrl(url)) {
      launchUrl(url);
    }
  } catch (e) {
    debugPrint(e.toString());
  }
}

//   @override
//   Widget build(BuildContext context) {
//     List<CatSetModel> catset = [];
// //     getCakeData(String  ) async {
// // final temp= await FBFireStore.mainCategory.where('field')

// //     }
//     return ResponsiveWid(
//       mobile: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//             // height: 550,
//             // width: MediaQuery.sizeOf(context).width,
//             // decoration: const BoxDecoration(
//             //   image: DecorationImage(
//             //     fit: BoxFit.cover,
//             //     image: AssetImage('assets/images/banner2.jpeg'),
//             //   ),
//             // ),
//             child: Center(
//               child: Column(
//                 children: [
//                   Padding(
//                     padding: const EdgeInsets.only(),
//                     child: Column(
//                       // crossAxisAlignment: CrossAxisAlignment.center,
//                       children: [
//                         Container(
//                           width: double.maxFinite,
//                           // height: 400,
//                           decoration: const BoxDecoration(
//                               // color: Colors.pink[200],
//                               color: Color.fromARGB(255, 238, 174, 196),
//                               borderRadius: BorderRadius.all(Radius.zero)),
//                           child: Padding(
//                             padding: const EdgeInsets.all(20),
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     Image.asset(
//                                       height: 50,
//                                       width: 100,
//                                       'assets/images/logo2.png',
//                                       fit: BoxFit.contain,
//                                     ),
//                                   ],
//                                 ),
//                                 const SizedBox(
//                                   height: 40,
//                                 ),
//                                 Row(
//                                   children: [
//                                     const Text(
//                                       "FOLLOW US ON",
//                                       style: TextStyle(
//                                         // letterSpacing: 1.2,
//                                         // fontFamily: fontFamily2,
//                                         // color: Colors.white,
//                                         fontWeight: FontWeight.w900,
//                                         fontSize: 16,
//                                       ),
//                                     ),
//                                     //  const SizedBox(
//                                     //     width: 5,
//                                     //   ),
//                                     IconButton(
//                                       highlightColor: Colors.transparent,
//                                       hoverColor: Colors.transparent,
//                                       onPressed: () => launchUrl(
//                                         Uri.parse(
//                                             'https://www.facebook.com/thebakersbar09/'),
//                                         mode: LaunchMode.externalApplication,
//                                       ),
//                                       icon: const Icon(
//                                         Icons.facebook,
//                                         color: Colors.black,
//                                       ),
//                                     ),
//                                     // SizedBox(
//                                     //   width: 5,
//                                     // ),
//                                     InkWell(
//                                       mouseCursor: SystemMouseCursors.click,
//                                       onTap: () => launchUrl(
//                                         Uri.parse(
//                                             'https://www.instagram.com/thebakersbar/'),
//                                         mode: LaunchMode.externalApplication,
//                                       ),
//                                       child: const Image(
//                                           color: Colors.black,
//                                           height: 25,
//                                           width: 25,
//                                           image: AssetImage(
//                                               'assets/images/instagram.png')),
//                                     ),
//                                   ],
//                                 ),
//                                 const SizedBox(
//                                   height: 5,
//                                 ),
//                                 const Text(
//                                   "Catch the latest updates and be a part of our story.",
//                                   style: TextStyle(
//                                     color: Colors.black,
//                                   ),
//                                 ),
//                                 const Column(
//                                   mainAxisAlignment: MainAxisAlignment.start,
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     SizedBox(
//                                       height: 10,
//                                     ),
//                                     ExpansionTile(
//                                       tilePadding: EdgeInsets.all(0),
//                                       backgroundColor: Colors.transparent,
//                                       title: Text(
//                                         'COLLECTIONS',
//                                         style: TextStyle(
//                                             fontWeight: FontWeight.bold,
//                                             fontSize: 16),
//                                       ),
//                                       children: [
//                                         InkWell(
//                                           mouseCursor: SystemMouseCursors.click,
//                                           child: Text(
//                                             "Same Day Cakes",
//                                             style: TextStyle(fontSize: 12),
//                                           ),
//                                         ),
//                                         SizedBox(
//                                           height: 10,
//                                         ),
//                                         InkWell(
//                                           mouseCursor: SystemMouseCursors.click,
//                                           child: Text(
//                                             "Classic Cakes",
//                                             style: TextStyle(fontSize: 12),
//                                           ),
//                                         ),
//                                         SizedBox(
//                                           height: 10,
//                                         ),
//                                         InkWell(
//                                           mouseCursor: SystemMouseCursors.click,
//                                           child: Text(
//                                             "Custom Cakes",
//                                             style: TextStyle(fontSize: 12),
//                                           ),
//                                         ),
//                                         SizedBox(
//                                           height: 10,
//                                         ),
//                                         InkWell(
//                                           mouseCursor: SystemMouseCursors.click,
//                                           child: Text(
//                                             "CupCakes",
//                                             style: TextStyle(fontSize: 12),
//                                           ),
//                                         ),
//                                         SizedBox(
//                                           height: 10,
//                                         ),
//                                         InkWell(
//                                           mouseCursor: SystemMouseCursors.click,
//                                           child: Text(
//                                             "Desserts",
//                                             style: TextStyle(fontSize: 12),
//                                           ),
//                                         ),
//                                         SizedBox(
//                                           height: 10,
//                                         ),
//                                         InkWell(
//                                           mouseCursor: SystemMouseCursors.click,
//                                           child: Text(
//                                             "Flowers",
//                                             style: TextStyle(fontSize: 12),
//                                           ),
//                                         ),
//                                         SizedBox(
//                                           height: 10,
//                                         ),
//                                         InkWell(
//                                           mouseCursor: SystemMouseCursors.click,
//                                           child: Text(
//                                             "Anniversary Cakes",
//                                             style: TextStyle(fontSize: 12),
//                                           ),
//                                         ),
//                                         SizedBox(
//                                           height: 10,
//                                         ),
//                                         InkWell(
//                                           mouseCursor: SystemMouseCursors.click,
//                                           child: Text(
//                                             "Birthday Cakes",
//                                             style: TextStyle(fontSize: 12),
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ],
//                                 ),
//                                 Column(
//                                   children: [
//                                     Padding(
//                                       padding:
//                                           const EdgeInsets.only(bottom: 5.0),
//                                       child: ExpansionTile(
//                                         tilePadding: const EdgeInsets.all(0),
//                                         title: const Text(
//                                           "QUICK LINKS",
//                                           style: TextStyle(
//                                               fontWeight: FontWeight.bold,
//                                               fontSize: 16),
//                                         ),
//                                         children: [
//                                           InkWell(
//                                             onTap: () =>
//                                                 context.go(Routes.contact),
//                                             // onTap: () => Navigator.push(
//                                             //     context,
//                                             //     MaterialPageRoute(
//                                             //       builder: (context) =>
//                                             //           const ContactUs(),
//                                             //     )),
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: const Text(
//                                               "Contact Us",
//                                               style: TextStyle(fontSize: 12),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             height: 10,
//                                           ),
//                                           InkWell(
//                                             onTap: () =>
//                                                 context.go(Routes.about),
//                                             // onTap: () => Navigator.push(
//                                             //     context,
//                                             //     MaterialPageRoute(
//                                             //         builder: (context) =>
//                                             //             const AboutUs())),
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: const Text(
//                                               "About Us",
//                                               style: TextStyle(fontSize: 12),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             height: 10,
//                                           ),
//                                           InkWell(
//                                             onTap: () =>
//                                                 context.go(Routes.privacy),
//                                             // onTap: () => Navigator.push(
//                                             //     context,
//                                             //     MaterialPageRoute(
//                                             //         builder: (context) =>
//                                             //             const PrivacyPolicy())),
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: const Text(
//                                               "Privacy Policy",
//                                               style: TextStyle(fontSize: 12),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             height: 10,
//                                           ),
//                                           InkWell(
//                                             onTap: () =>
//                                                 context.go(Routes.terms),
//                                             // onTap: () => Navigator.push(
//                                             //     context,
//                                             //     MaterialPageRoute(
//                                             //         builder: (context) =>
//                                             //             const TermsAndConditions())),
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: const Text(
//                                               "Terms and Conditions",
//                                               style: TextStyle(fontSize: 12),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             height: 10,
//                                           ),
//                                           InkWell(
//                                             onTap: () => context
//                                                 .go(Routes.shippingPolicy),

//                                             // onTap: () => Navigator.push(
//                                             //     context,
//                                             //     MaterialPageRoute(
//                                             //         builder: (context) =>
//                                             //             const ShippingPolicy())),
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: const Text(
//                                               "Shipping Policy",
//                                               style: TextStyle(fontSize: 12),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             height: 10,
//                                           ),
//                                           InkWell(
//                                             onTap: () => context.go(
//                                                 Routes.returnandrefundPolicy),
//                                             // onTap: () => Navigator.push(
//                                             //     context,
//                                             //     MaterialPageRoute(
//                                             //         builder: (context) =>
//                                             //             const ReturnAndRefundPolicy())),
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: const Text(
//                                               "Return and Refund Policy",
//                                               style: TextStyle(fontSize: 12),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             height: 10,
//                                           ),
//                                           // const InkWell(
//                                           //   mouseCursor:
//                                           //       SystemMouseCursors.click,
//                                           //   child: Text(
//                                           //     "FAQs",
//                                           //     style: TextStyle(fontSize: 15),
//                                           //   ),
//                                           // ),
//                                         ],
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 const SizedBox(
//                                   height: 14,
//                                 ),
//                                 const CopyRightBar(
//                                   size: Size(1, 1),
//                                   fontSize: 15,
//                                 ),
//                                 const SizedBox(
//                                   height: 5,
//                                 ),
//                                 // Column(
//                                 //   // crossAxisAlignment: CrossAxisAlignment.center,
//                                 //   children: [
//                                 //     const Padding(
//                                 //       padding: EdgeInsets.only(bottom: 10.0),
//                                 //       child: Text(
//                                 //         "Social Media",
//                                 //         style: TextStyle(
//                                 //             fontWeight: FontWeight.bold,
//                                 //             fontSize: 15),
//                                 //       ),
//                                 //     ),
//                                 //     Row(
//                                 //       // mainAxisAlignment:
//                                 //       // MainAxisAlignment.spaceBetween,
//                                 //       children: [
//                                 //         // const SizedBox(width: 35),
//                                 //         const Spacer(),

//                                 //         const Spacer(),
//                                 //       ],
//                                 //     ),
//                                 //   ],
//                                 // ),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//       //tablet

//       tablet: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//             child: Center(
//               child: Column(
//                 children: [
//                   Padding(
//                     padding: const EdgeInsets.only(),
//                     child: Column(
//                       children: [
//                         Container(
//                           width: double.maxFinite,
//                           // height: 400,
//                           decoration: const BoxDecoration(
//                               // color: Colors.pink[200],
//                               color: Color.fromARGB(255, 238, 174, 196),
//                               borderRadius: BorderRadius.all(Radius.zero)),
//                           child: Padding(
//                             padding: const EdgeInsets.all(20),
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     Image.asset(
//                                       height: 80,
//                                       width: 120,
//                                       'assets/images/logo2.png',
//                                       fit: BoxFit.contain,
//                                     ),
//                                   ],
//                                 ),
//                                 const SizedBox(
//                                   height: 40,
//                                 ),
//                                 Row(
//                                   children: [
//                                     const Text(
//                                       "FOLLOW US ON",
//                                       style: TextStyle(
//                                         fontWeight: FontWeight.w900,
//                                         fontSize: 20,
//                                       ),
//                                     ),
//                                     IconButton(
//                                       highlightColor: Colors.transparent,
//                                       hoverColor: Colors.transparent,
//                                       onPressed: () => launchUrl(
//                                         Uri.parse(
//                                             'https://www.facebook.com/thebakersbar09/'),
//                                         mode: LaunchMode.externalApplication,
//                                       ),
//                                       icon: const Icon(
//                                         size: 30,
//                                         Icons.facebook,
//                                         color: Colors.black,
//                                       ),
//                                     ),
//                                     InkWell(
//                                       mouseCursor: SystemMouseCursors.click,
//                                       onTap: () => launchUrl(
//                                         Uri.parse(
//                                             'https://www.instagram.com/thebakersbar/'),
//                                         mode: LaunchMode.externalApplication,
//                                       ),
//                                       child: const Image(
//                                           color: Colors.black,
//                                           height: 30,
//                                           width: 30,
//                                           image: AssetImage(
//                                               'assets/images/instagram.png')),
//                                     ),
//                                   ],
//                                 ),
//                                 const SizedBox(
//                                   height: 5,
//                                 ),
//                                 const Text(
//                                   "Catch the latest updates and be a part of our story.",
//                                   style: TextStyle(
//                                       color: Colors.black, fontSize: 18),
//                                 ),
//                                 Column(
//                                   mainAxisAlignment: MainAxisAlignment.start,
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     const SizedBox(
//                                       height: 10,
//                                     ),
//                                     Theme(
//                                       data: ThemeData(
//                                           dividerColor: Colors.transparent),
//                                       child: ExpansionTile(
//                                         expandedAlignment: Alignment(-1, 0),
//                                         expandedCrossAxisAlignment:
//                                             CrossAxisAlignment.start,
//                                         childrenPadding: EdgeInsets.only(
//                                             top: 10, bottom: 20),
//                                         tilePadding: EdgeInsets.all(0),
//                                         backgroundColor: Colors.transparent,
//                                         title: Text(
//                                           'COLLECTIONS',
//                                           style: TextStyle(
//                                               fontWeight: FontWeight.bold,
//                                               fontSize: 20),
//                                         ),
//                                         children: [
//                                           InkWell(
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: Text(
//                                               "Same Day Cakes",
//                                               style: expansionstyle(),
//                                             ),
//                                           ),
//                                           sizeBetweenExpansion(),
//                                           InkWell(
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: Text(
//                                               "Classic Cakes",
//                                               style: expansionstyle(),
//                                             ),
//                                           ),
//                                           sizeBetweenExpansion(),
//                                           InkWell(
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: Text(
//                                               "Custom Cakes",
//                                               style: expansionstyle(),
//                                             ),
//                                           ),
//                                           sizeBetweenExpansion(),
//                                           InkWell(
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: Text(
//                                               "CupCakes",
//                                               style: expansionstyle(),
//                                             ),
//                                           ),
//                                           sizeBetweenExpansion(),
//                                           InkWell(
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: Text(
//                                               "Desserts",
//                                               style: expansionstyle(),
//                                             ),
//                                           ),
//                                           sizeBetweenExpansion(),
//                                           InkWell(
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: Text(
//                                               "Flowers",
//                                               style: expansionstyle(),
//                                             ),
//                                           ),
//                                           sizeBetweenExpansion(),
//                                           InkWell(
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: Text(
//                                               "Anniversary Cakes",
//                                               style: expansionstyle(),
//                                             ),
//                                           ),
//                                           sizeBetweenExpansion(),
//                                           InkWell(
//                                             mouseCursor:
//                                                 SystemMouseCursors.click,
//                                             child: Text(
//                                               "Birthday Cakes",
//                                               style: expansionstyle(),
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 Column(
//                                   children: [
//                                     Padding(
//                                       padding:
//                                           EdgeInsets.only(top: 10, bottom: 5.0),
//                                       child: Theme(
//                                         data: ThemeData(
//                                             dividerColor: Colors.transparent),
//                                         child: ExpansionTile(
//                                           expandedAlignment: Alignment(-1, 0),
//                                           expandedCrossAxisAlignment:
//                                               CrossAxisAlignment.start,
//                                           childrenPadding:
//                                               EdgeInsets.only(bottom: 20),
//                                           tilePadding: EdgeInsets.all(0),
//                                           title: Text(
//                                             "QUICK LINKS",
//                                             style: TextStyle(
//                                                 fontWeight: FontWeight.bold,
//                                                 fontSize: 20),
//                                           ),
//                                           children: [
//                                             InkWell(
//                                               onTap: () =>
//                                                   context.go(Routes.contact),
//                                               mouseCursor:
//                                                   SystemMouseCursors.click,
//                                               child: Text(
//                                                 "Contact Us",
//                                                 style: expansionstyle(),
//                                               ),
//                                             ),
//                                             sizeBetweenExpansion(),
//                                             InkWell(
//                                               onTap: () =>
//                                                   context.go(Routes.about),
//                                               mouseCursor:
//                                                   SystemMouseCursors.click,
//                                               child: Text(
//                                                 "About Us",
//                                                 style: expansionstyle(),
//                                               ),
//                                             ),
//                                             sizeBetweenExpansion(),
//                                             InkWell(
//                                               onTap: () =>
//                                                   context.go(Routes.privacy),
//                                               mouseCursor:
//                                                   SystemMouseCursors.click,
//                                               child: Text(
//                                                 "Privacy Policy",
//                                                 style: expansionstyle(),
//                                               ),
//                                             ),
//                                             sizeBetweenExpansion(),
//                                             InkWell(
//                                               onTap: () =>
//                                                   context.go(Routes.terms),
//                                               mouseCursor:
//                                                   SystemMouseCursors.click,
//                                               child: Text(
//                                                 "Terms and Conditions",
//                                                 style: expansionstyle(),
//                                               ),
//                                             ),
//                                             sizeBetweenExpansion(),
//                                             InkWell(
//                                               onTap: () => context
//                                                   .go(Routes.shippingPolicy),
//                                               mouseCursor:
//                                                   SystemMouseCursors.click,
//                                               child: Text(
//                                                 "Shipping Policy",
//                                                 style: expansionstyle(),
//                                               ),
//                                             ),
//                                             sizeBetweenExpansion(),
//                                             InkWell(
//                                               onTap: () => context.go(
//                                                   Routes.returnandrefundPolicy),
//                                               mouseCursor:
//                                                   SystemMouseCursors.click,
//                                               child: Text(
//                                                 "Return and Refund Policy",
//                                                 style: expansionstyle(),
//                                               ),
//                                             ),
//                                             const SizedBox(
//                                               height: 10,
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 const SizedBox(
//                                   height: 14,
//                                 ),
//                                 const CopyRightBar(
//                                   size: Size(5, 5),
//                                   fontSize: 18,
//                                 ),
//                                 const SizedBox(
//                                   height: 5,
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//       // Column(
//       //   children: [
//       //     Container(
//       //       height: 1000,
//       //       width: MediaQuery.sizeOf(context).width,
//       //       decoration: const BoxDecoration(
//       //         image: DecorationImage(
//       //           fit: BoxFit.cover,
//       //           image: AssetImage('assets/images/banner2.jpeg'),
//       //         ),
//       //       ),
//       //       child: Center(
//       //         child: Column(
//       //           children: [
//       //             Padding(
//       //               padding:
//       //                   const EdgeInsets.only(right: 60, left: 60, top: 60),
//       //               child: Column(
//       //                 // crossAxisAlignment: CrossAxisAlignment.start,
//       //                 children: [
//       //                   Container(
//       //                     width: double.maxFinite,
//       //                     height: 900,
//       //                     decoration: BoxDecoration(
//       //                         color: Colors.pink[200],
//       //                         borderRadius:
//       //                             const BorderRadius.all(Radius.zero)),
//       //                     child: Padding(
//       //                       padding: const EdgeInsets.only(
//       //                           left: 50, right: 50, top: 30, bottom: 30),
//       //                       child: Column(
//       //                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       //                         children: [
//       //                           Image.asset(
//       //                             height: 80,
//       //                             width: 150,
//       //                             'assets/images/logo2.png',
//       //                             fit: BoxFit.contain,
//       //                           ),
//       //                           const Column(
//       //                             children: [
//       //                               Padding(
//       //                                 padding: EdgeInsets.only(bottom: 10.0),
//       //                                 child: Text(
//       //                                   'COLLECTIONS',
//       //                                   style: TextStyle(
//       //                                       fontWeight: FontWeight.bold,
//       //                                       fontSize: 20),
//       //                                 ),
//       //                               ),
//       //                               InkWell(
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: Text(
//       //                                   "Same Day Cakes",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: Text(
//       //                                   "Classic Cakes",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: Text(
//       //                                   "Custom Cakes",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: Text(
//       //                                   "CupCakes",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: Text(
//       //                                   "Desserts",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: Text(
//       //                                   "Flowers",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: Text(
//       //                                   "Anniversary Cakes",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: Text(
//       //                                   "Birthday Cakes",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                             ],
//       //                           ),
//       //                           Column(
//       //                             children: [
//       //                               const Padding(
//       //                                 padding: EdgeInsets.only(bottom: 10.0),
//       //                                 child: Text(
//       //                                   "Quick Links",
//       //                                   style: TextStyle(
//       //                                       fontWeight: FontWeight.bold,
//       //                                       fontSize: 20),
//       //                                 ),
//       //                               ),
//       //                               InkWell(
//       //                                 onTap: () => context.go(Routes.contact),
//       //                                 // onTap: () => Navigator.push(
//       //                                 //     context,
//       //                                 //     MaterialPageRoute(
//       //                                 //       builder: (context) =>
//       //                                 //           const ContactUs(),
//       //                                 //     )),
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: const Text(
//       //                                   "Contact Us",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               const SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 onTap: () => context.go(Routes.about),
//       //                                 // onTap: () => Navigator.push(
//       //                                 //     context,
//       //                                 //     MaterialPageRoute(
//       //                                 //         builder: (context) =>
//       //                                 //             const AboutUs())),
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: const Text(
//       //                                   "About Us",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               const SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 onTap: () => context.go(Routes.privacy),
//       //                                 // onTap: () => Navigator.push(
//       //                                 //     context,
//       //                                 //     MaterialPageRoute(
//       //                                 //         builder: (context) =>
//       //                                 //             const PrivacyPolicy())),
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: const Text(
//       //                                   "Privacy Policy",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               const SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 onTap: () => context.go(Routes.terms),
//       //                                 // onTap: () => Navigator.push(
//       //                                 //     context,
//       //                                 //     MaterialPageRoute(
//       //                                 //         builder: (context) =>
//       //                                 //             const TermsAndConditions())),
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: const Text(
//       //                                   "Terms and Conditions",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               const SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 onTap: () =>
//       //                                     context.go(Routes.shippingPolicy),
//       //                                 // onTap: () => Navigator.push(
//       //                                 //     context,
//       //                                 //     MaterialPageRoute(
//       //                                 //         builder: (context) =>
//       //                                 //             const ShippingPolicy())),
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: const Text(
//       //                                   "Shipping Policy",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               const SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               InkWell(
//       //                                 onTap: () => context
//       //                                     .go(Routes.returnandrefundPolicy),
//       //                                 // onTap: () => Navigator.push(
//       //                                 //     context,
//       //                                 //     MaterialPageRoute(
//       //                                 //         builder: (context) =>
//       //                                 //             const ReturnAndRefundPolicy())),
//       //                                 mouseCursor: SystemMouseCursors.click,
//       //                                 child: const Text(
//       //                                   "Return and Refund Policy",
//       //                                   style: TextStyle(fontSize: 15),
//       //                                 ),
//       //                               ),
//       //                               const SizedBox(
//       //                                 height: 10,
//       //                               ),
//       //                               // const InkWell(
//       //                               //   mouseCursor: SystemMouseCursors.click,
//       //                               //   child: Text(
//       //                               //     "FAQs",
//       //                               //     style: TextStyle(fontSize: 15),
//       //                               //   ),
//       //                               // ),
//       //                             ],
//       //                           ),
//       //                           Column(
//       //                             crossAxisAlignment: CrossAxisAlignment.center,
//       //                             children: [
//       //                               const Padding(
//       //                                 padding: EdgeInsets.only(bottom: 10.0),
//       //                                 child: Text(
//       //                                   "Social Media",
//       //                                   style: TextStyle(
//       //                                       fontWeight: FontWeight.bold,
//       //                                       fontSize: 20),
//       //                                 ),
//       //                               ),
//       //                               Row(
//       //                                 // mainAxisAlignment:
//       //                                 // MainAxisAlignment.spaceBetween,
//       //                                 children: [
//       //                                   // const SizedBox(width: 35),
//       //                                   const Spacer(),
//       //                                   IconButton(
//       //                                     highlightColor: Colors.transparent,
//       //                                     hoverColor: Colors.transparent,
//       //                                     onPressed: () => launchUrl(
//       //                                       Uri.parse(
//       //                                           'https://www.facebook.com/thebakersbar09/'),
//       //                                       mode:
//       //                                           LaunchMode.externalApplication,
//       //                                     ),
//       //                                     icon: const Icon(
//       //                                       Icons.facebook,
//       //                                       color: Colors.black,
//       //                                     ),
//       //                                   ),
//       //                                   const SizedBox(
//       //                                     width: 20,
//       //                                   ),
//       //                                   InkWell(
//       //                                     mouseCursor: SystemMouseCursors.click,
//       //                                     onTap: () => launchUrl(
//       //                                       Uri.parse(
//       //                                           'https://www.instagram.com/thebakersbar/'),
//       //                                       mode:
//       //                                           LaunchMode.externalApplication,
//       //                                     ),
//       //                                     child: const Image(
//       //                                         color: Colors.black,
//       //                                         height: 25,
//       //                                         width: 25,
//       //                                         image: AssetImage(
//       //                                             'assets/images/instagram.png')),
//       //                                   ),
//       //                                   const Spacer(),
//       //                                 ],
//       //                               ),
//       //                             ],
//       //                           ),
//       //                         ],
//       //                       ),
//       //                     ),
//       //                   ),
//       //                   const SizedBox(
//       //                     height: 10,
//       //                   ),
//       //                   const SizedBox(
//       //                     width: 350,
//       //                     child: CopyRightBar(
//       //                       size: Size(5, 5),
//       //                     ),
//       //                   ),
//       //                   // const SizedBox(
//       //                   //   height: 10,
//       //                   // ),
//       //                 ],
//       //               ),
//       //             ),
//       //           ],
//       //         ),
//       //       ),
//       //     ),
//       //   ],
//       // ),
//       //desktop

//       desktop: Column(
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Container(
//             width: double.maxFinite,
//             decoration: const BoxDecoration(
//                 // color: Color(0xffecc9c7),
//                 color: Color.fromARGB(255, 238, 174, 196),
//                 // color: themeColorLite,
//                 borderRadius: BorderRadius.all(Radius.zero)),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Row(
//                   children: [
//                     Expanded(
//                       child: Padding(
//                         padding: const EdgeInsets.only(
//                             left: 100, right: 100, top: 50, bottom: 50),
//                         child: Row(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Image.asset(
//                               // height: 100,
//                               // width: 200,
//                               'assets/images/logo2.png',
//                               fit: BoxFit.contain,
//                             ),
//                             Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               mainAxisAlignment: MainAxisAlignment.spaceAround,
//                               children: [
//                                 const Padding(
//                                   padding: EdgeInsets.only(bottom: 20.0),
//                                   child: Text(
//                                     'COLLECTIONS',
//                                     style: TextStyle(
//                                         fontWeight: FontWeight.bold,
//                                         fontSize: 20),
//                                   ),
//                                 ),
//                                 // InkWell(
//                                 //   mouseCursor: SystemMouseCursors.click,
//                                 //   child: Text(
//                                 //     "Same Day Cakes",
//                                 //     style: TextStyle(fontSize: 17),
//                                 //   ),
//                                 // ),
//                                 // SizedBox(
//                                 //   height: 10,
//                                 // ),
//                                 InkWell(
//                                   onTap: () {
//                                     Navigator.push(
//                                         context,
//                                         MaterialPageRoute(
//                                             builder: (context) => const Wrapper(
//                                                   body: SubcatPage(
//                                                       name: "Classic Cakes"),
//                                                 )));
//                                   },
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: const Text(
//                                     "Classic Cakes",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 const InkWell(
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: Text(
//                                     "Custom Cakes",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 const InkWell(
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: Text(
//                                     "CupCakes",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 const InkWell(
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: Text(
//                                     "Desserts",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 const InkWell(
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: Text(
//                                     "Flowers",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 const InkWell(
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: Text(
//                                     "Anniversary Cakes",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 const InkWell(
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: Text(
//                                     "Birthday Cakes",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                             Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               mainAxisAlignment: MainAxisAlignment.spaceAround,
//                               children: [
//                                 const Padding(
//                                   padding: EdgeInsets.only(bottom: 20.0),
//                                   child: Text(
//                                     "Quick Links",
//                                     style: TextStyle(
//                                         fontWeight: FontWeight.bold,
//                                         fontSize: 20),
//                                   ),
//                                 ),
//                                 InkWell(
//                                   onTap: () => context.go(Routes.contact),
//                                   // Navigator.push(
//                                   //     context,
//                                   //     MaterialPageRoute(
//                                   //         builder: (context) =>
//                                   //             const ContactUs())),
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: const Text(
//                                     "Contact Us",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 InkWell(
//                                   onTap: () => context.go(Routes.about),
//                                   // Navigator.push(
//                                   //     context,
//                                   //     MaterialPageRoute(
//                                   //         builder: (context) =>
//                                   //             const AboutUs())),
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: const Text(
//                                     "About Us",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 InkWell(
//                                   onTap: () => context.go(Routes.privacy),
//                                   // Navigator.push(
//                                   //     context,
//                                   //     MaterialPageRoute(
//                                   //         builder: (context) =>
//                                   //             const PrivacyPolicy())),
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: const Text(
//                                     "Privacy Policy",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 InkWell(
//                                   onTap: () => context.go(Routes.terms),
//                                   // Navigator.push(
//                                   //     context,
//                                   //     MaterialPageRoute(
//                                   //         builder: (context) =>
//                                   //             const TermsAndConditions())),
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: const Text(
//                                     "Terms and Conditions",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 InkWell(
//                                   onTap: () =>
//                                       context.go(Routes.shippingPolicy),
//                                   // Navigator.push(
//                                   //     context,
//                                   //     MaterialPageRoute(
//                                   //         builder: (context) =>
//                                   //             const ShippingPolicy())),
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: const Text(
//                                     "Shipping Policy",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 InkWell(
//                                   onTap: () =>
//                                       context.go(Routes.returnandrefundPolicy),
//                                   // Navigator.push(
//                                   //     context,
//                                   //     MaterialPageRoute(
//                                   //         builder: (context) =>
//                                   //             const ReturnAndRefundPolicy())),
//                                   mouseCursor: SystemMouseCursors.click,
//                                   child: const Text(
//                                     "Return and Refund Policy",
//                                     style: TextStyle(fontSize: 17),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   height: 10,
//                                 ),
//                                 // const InkWell(
//                                 //   mouseCursor: SystemMouseCursors.click,
//                                 //   child: Text(
//                                 //     "FAQs",
//                                 //     style: TextStyle(fontSize: 17),
//                                 //   ),
//                                 // ),
//                               ],
//                             ),
//                             Column(
//                               crossAxisAlignment: CrossAxisAlignment.center,
//                               children: [
//                                 const Padding(
//                                   padding: EdgeInsets.only(bottom: 20.0),
//                                   child: Text(
//                                     "Social Media",
//                                     style: TextStyle(
//                                         fontWeight: FontWeight.bold,
//                                         fontSize: 20),
//                                   ),
//                                 ),
//                                 Row(
//                                   mainAxisAlignment:
//                                       MainAxisAlignment.spaceBetween,
//                                   children: [
//                                     IconButton(
//                                       highlightColor: Colors.transparent,
//                                       hoverColor: Colors.transparent,
//                                       onPressed: () => launchUrl(
//                                         Uri.parse(
//                                             'https://www.facebook.com/thebakersbar09/'),
//                                         mode: LaunchMode.externalApplication,
//                                       ),
//                                       icon: const Icon(
//                                         Icons.facebook,
//                                         color: Colors.white,
//                                       ),
//                                     ),
//                                     const SizedBox(
//                                       width: 20,
//                                     ),
//                                     InkWell(
//                                       mouseCursor: SystemMouseCursors.click,
//                                       onTap: () => launchUrl(
//                                         Uri.parse(
//                                             'https://www.instagram.com/thebakersbar/'),
//                                         mode: LaunchMode.externalApplication,
//                                       ),
//                                       child: const Image(
//                                           height: 25,
//                                           width: 25,
//                                           image: AssetImage(
//                                               'assets/images/instagram.png')),
//                                     ),
//                                   ],
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//                 const SizedBox(
//                   height: 10,
//                 ),
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 100.0),
//                   child: const SizedBox(
//                     width: 350,
//                     child: CopyRightBar(
//                       size: Size(10, 10),
//                     ),
//                   ),
//                 ),
//                 const SizedBox(
//                   height: 10,
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   SizedBox sizeBetweenExpansion() {
//     return SizedBox(
//       height: 15,
//     );
//   }

//   TextStyle expansionstyle() =>
//       TextStyle(fontSize: 18, fontWeight: FontWeight.w600);
// }
