import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CartBanner extends StatelessWidget {
  const CartBanner({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return SizedBox(
      height: size.width <= 510 ? 100 : 200,
      child: Stack(
        children: [
          Image.asset(
            "assets/images/common-banner 3.png",
            // "assets/images/banner2.jpeg",
            fit: BoxFit.cover,
            width: double.maxFinite,
            height: 400,
          ),
          Align(
            child: Text(
              "YOUR CART",
              style: GoogleFonts.crimsonPro(
                  fontSize: 25, fontWeight: FontWeight.bold),
            ),
          )
        ],
      ),
    );
  }
}
