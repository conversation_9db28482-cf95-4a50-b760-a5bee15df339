import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/methods.dart';
import 'package:the_bakers_bar_user/shared/router.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/views/common/responsive.dart';

class CartStickybar extends StatefulWidget {
  const CartStickybar({super.key});

  @override
  State<CartStickybar> createState() => _CartStickybarState();
}

class _CartStickybarState extends State<CartStickybar> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  bool loader = false;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<ProductCtrl>(
      builder: (pctrl) {
        if (isLoggedIn())
          loader = Get.find<ProductCtrl>().userDetails?.cartitems != null
              ? true
              : false;
        if (!isLoggedIn()) {
          loader = true;
        }
        getProductPrice();
        return ResponsiveWid(
          mobile: Material(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: const BoxDecoration(
                color: themeColorLite,
                boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6)],
              ),
              // padding: const EdgeInsets.symmetric(vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    children: [
                      Row(
                        children: [
                          const Spacer(),
                          Text(
                            "Subtotal",
                            style: GoogleFonts.b612Mono(fontSize: 22),
                          ),
                          SizedBox(width: size.width < 640 ? 60 : 80),
                          !loader
                              ? const CircularProgressIndicator(
                                  color: Colors.white,
                                )
                              : Text(
                                  "Rs.${Get.find<ProductCtrl>().totalPrice}",
                                  style: GoogleFonts.b612Mono(fontSize: 20),
                                ),
                          const Spacer(),
                        ],
                      ),
                      const SizedBox(height: 6),
                      const Row(
                        children: [
                          Spacer(),
                          Text("Shipping & tax calculation at next step"),
                          Spacer(),
                        ],
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      const SizedBox(height: 10),
                      InkWell(
                        focusColor: Colors.amber,
                        child: ElevatedButton(
                          style: const ButtonStyle(
                            foregroundColor: WidgetStatePropertyAll(
                              Colors.white,
                            ),
                            backgroundColor: WidgetStatePropertyAll(
                              Color.fromARGB(255, 41, 40, 40),
                            ),
                            shadowColor: WidgetStatePropertyAll(
                              Colors.transparent,
                            ),
                            fixedSize:
                                // WidgetStatePropertyAll(Size.fromWidth(320)
                                WidgetStatePropertyAll(Size(200, 35)),
                            shape: WidgetStatePropertyAll(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                              ),
                            ),
                            elevation: WidgetStatePropertyAll(10),
                          ),
                          onPressed: () async {
                            if (isLoggedIn()) {
                              pctrl.totalPrice = 0;
                              pctrl.finalPrice = 0;
                              pctrl.coupon = null;
                              pctrl.couponvalue = 0;
                              context.go(Routes.checkout);
                            } else {
                              context.go(Routes.auth);
                            }
                          },
                          child: const Text(
                            "CHECK OUT",
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          tablet: Material(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 25),
              decoration: const BoxDecoration(
                color: themeColorLite,
                boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6)],
              ),
              // padding: const EdgeInsets.symmetric(vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      Row(
                        children: [
                          Text(
                            "Subtotal",
                            style: GoogleFonts.b612Mono(
                              fontSize: size.width < 640 ? 18 : 24,
                            ),
                          ),
                          const SizedBox(width: 80),
                          !loader
                              ? const CircularProgressIndicator(
                                  color: Colors.white,
                                )
                              : Text(
                                  "Rs.${Get.find<ProductCtrl>().totalPrice}",
                                  style: GoogleFonts.b612Mono(
                                    fontSize: size.width < 640 ? 16 : 20,
                                  ),
                                ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          Text(
                            "Shipping & tax calculation at next step",
                            style: TextStyle(
                              fontSize: size.width < 640 ? 10 : 14,
                            ),
                          ),
                          const SizedBox(height: 10),
                        ],
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        focusColor: Colors.amber,
                        child: ElevatedButton(
                          style: ButtonStyle(
                            foregroundColor: const WidgetStatePropertyAll(
                              Colors.white,
                            ),
                            backgroundColor: const WidgetStatePropertyAll(
                              Color.fromARGB(255, 41, 40, 40),
                            ),
                            shadowColor: const WidgetStatePropertyAll(
                              Colors.transparent,
                            ),
                            fixedSize:
                                // WidgetStatePropertyAll(Size.fromWidth(320)
                                WidgetStatePropertyAll(
                                  Size(
                                    size.width < 640 ? 200 : 250,
                                    size.width < 640 ? 30 : 40,
                                  ),
                                ),
                            shape: const WidgetStatePropertyAll(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                              ),
                            ),
                            elevation: const WidgetStatePropertyAll(10),
                          ),
                          onPressed: () async {
                            if (isLoggedIn()) {
                              pctrl.totalPrice = 0;
                              pctrl.finalPrice = 0;
                              pctrl.coupon = null;
                              ;
                              pctrl.couponvalue = 0;
                              context.go(Routes.checkout);
                            } else {
                              context.go(Routes.auth);
                            }
                          },
                          child: Text(
                            "CHECK OUT",
                            style: GoogleFonts.crimsonPro(
                              fontWeight: FontWeight.w400,
                              fontSize: size.width < 640 ? 16 : 22,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          desktop: Material(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 80, vertical: 25),
              decoration: const BoxDecoration(
                color: themeColorLite,
                boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6)],
              ),
              // padding: const EdgeInsets.symmetric(vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      Row(
                        children: [
                          InkWell(
                            onTap: () {
                              setState(() {});
                            },
                            child: Text(
                              "Subtotal",
                              style: GoogleFonts.b612Mono(fontSize: 28),
                            ),
                          ),
                          const SizedBox(width: 80),
                          !loader
                              ? const CircularProgressIndicator(
                                  color: Colors.white,
                                )
                              : Text(
                                  "Rs.${Get.find<ProductCtrl>().totalPrice}",
                                  style: GoogleFonts.b612Mono(fontSize: 20),
                                ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      const Row(
                        children: [
                          Text("Shipping & tax calculation at next step"),
                          SizedBox(height: 10),
                        ],
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      InkWell(
                        focusColor: Colors.amber,
                        child: ElevatedButton(
                          style: const ButtonStyle(
                            foregroundColor: WidgetStatePropertyAll(
                              Colors.white,
                            ),
                            backgroundColor: WidgetStatePropertyAll(
                              Color.fromARGB(255, 41, 40, 40),
                            ),
                            shadowColor: WidgetStatePropertyAll(
                              Colors.transparent,
                            ),
                            fixedSize:
                                // WidgetStatePropertyAll(Size.fromWidth(320)
                                WidgetStatePropertyAll(Size(300, 50)),
                            shape: WidgetStatePropertyAll(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                              ),
                            ),
                            elevation: WidgetStatePropertyAll(10),
                          ),
                          onPressed: () async {
                            if (isLoggedIn()) {
                              pctrl.totalPrice = 0;
                              pctrl.finalPrice = 0;
                              pctrl.coupon = null;
                              pctrl.couponvalue = 0;
                              context.go(Routes.checkout);
                            } else {
                              context.go(Routes.auth);
                            }
                          },
                          child: Text(
                            "CHECK OUT",
                            style: GoogleFonts.crimsonPro(
                              fontWeight: FontWeight.w400,
                              fontSize: 24,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
