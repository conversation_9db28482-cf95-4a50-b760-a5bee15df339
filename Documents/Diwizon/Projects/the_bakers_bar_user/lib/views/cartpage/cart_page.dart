import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/Models/cart_items.dart';
import 'package:the_bakers_bar_user/controllers/product_ctrl.dart';
import 'package:the_bakers_bar_user/shared/methods.dart';
import 'package:the_bakers_bar_user/views/cartpage/cart_banner.dart';
import 'package:the_bakers_bar_user/views/cartpage/cart_details.dart';
import 'package:the_bakers_bar_user/views/cartpage/cart_stickybar.dart';
import 'package:the_bakers_bar_user/views/common/underline_bar.dart';
import 'package:the_bakers_bar_user/views/homepage/add_on.dart';
import 'package:the_bakers_bar_user/wrapper.dart';

class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

// final scrollcTRL = ScrollController();

class _CartPageState extends State<CartPage> {
  CartItemsModel? cartproduct;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    try {
      trackFacebookEvent('AddToCart', {
        'content_id': cartproduct?.id, // Product ID
        'content_type': 'cart',
      });
    } catch (e) {
      // TODO
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<ProductCtrl>(
      builder: (pctrl) {
        getProductPrice();
        // print(pctrl.userDetails?.cartitems);
        final listdetails =
            (isLoggedIn()
                ? pctrl.userDetails?.cartitems
                : pctrl.orderProduct) ??
            [];
        listdetails.sort((a, b) => a.id.compareTo(b.id));
        return Column(
          children: [
            Expanded(
              child: Wrapper(
                body: Column(
                  children: [
                    const CartBanner(),
                    SizedBox(height: size.width <= 510 ? 15 : 25),
                    const SizedBox(height: 10),
                    // if (listdetails.isNotEmpty)
                    // const Padding(
                    //   padding: EdgeInsets.only(left: 40.0, right: 100),
                    //   child: Divider(
                    //     color: Color.fromARGB(255, 202, 199, 199),
                    //     indent: 20,
                    //     endIndent: 20,
                    //   ),
                    // ),
                    ...List.generate(
                      // pctrl.orderProduct.length,
                      listdetails.length,
                      (index) {
                        cartproduct = listdetails[index];
                        // if (isLoggedIn()) {
                        //   cartproduct = Get.find<ProductCtrl>()
                        //       .userDetails
                        //       ?.cartitems[index];
                        // }
                        // if (!isLoggedIn()) {
                        //   cartproduct =
                        //       Get.find<ProductCtrl>().orderProduct[index];
                        // }

                        return Cartdetails(cartproduct: cartproduct!);
                      },
                    ),
                    // const Carttotal(),
                    if (listdetails.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 50.0, bottom: 10),
                        child: Text(
                          "ADD ON's",
                          style: GoogleFonts.crimsonPro(
                            fontSize: 30,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),

                    if (listdetails.isNotEmpty) const UnderlineBar(),
                    if (listdetails.isEmpty)
                      Padding(
                        padding: EdgeInsets.only(
                          top: size.width <= 510 ? 145 : 170,
                          bottom: size.width <= 510 ? 130 : 140,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Your Cart is Empty!",
                              style: TextStyle(
                                fontSize: size.width <= 510 ? 20 : 28,
                              ),
                            ),
                            SizedBox(width: 10),
                            Image.asset(
                              height: 40,
                              'assets/images/shopping-cart.png',
                            ),
                          ],
                        ),
                      ),

                    if (listdetails.isNotEmpty) const SizedBox(height: 50),
                    if (listdetails.isNotEmpty) const AddOn(),
                    SizedBox(height: size.width <= 510 ? 65 : 155),
                  ],
                ),
              ),
            ),
            if (listdetails.isNotEmpty) const CartStickybar(),
          ],
        );
      },
    );
  }
}
