import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/views/common/underline_bar.dart';
import 'package:the_bakers_bar_user/views/contact_us_page/contact_us_page.dart';
import 'package:the_bakers_bar_user/wrapper.dart';

class AboutUs extends StatelessWidget {
  const AboutUs({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      body: Column(
        children: [
          SizedBox(
            height: 300,
            child: Stack(
              children: [
                Image.asset(
                  "assets/images/banner2.jpeg",
                  fit: BoxFit.cover,
                  width: double.maxFinite,
                  height: 400,
                ),
                const Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: EdgeInsets.only(right: 60.0),
                    child: Image(
                      fit: BoxFit.cover,
                      height: 220,
                      image: AssetImage("assets/images/tbblogo.jpeg"),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(40.0),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 30.0, bottom: 30),
                  child: Center(
                    child: Text(
                      'WE ARE LOCATED HERE',
                      style: GoogleFonts.crimsonPro(
                        fontSize: 38,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const UnderlineBar(),
                Padding(
                  padding: const EdgeInsets.only(top: 20, bottom: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Wrap(
                          alignment: WrapAlignment.center,
                          runAlignment: WrapAlignment.start,
                          crossAxisAlignment: WrapCrossAlignment.center,
                          children: [
                            ...List.generate(3, (index) {
                              return const LocationCard1();
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  "IT'S NOT A CAKE.",
                  style: GoogleFonts.aBeeZee(
                    fontWeight: FontWeight.bold,
                    fontSize: 40,
                    color: themeColor,
                  ),
                ),
                Text(
                  "IT'S AN",
                  style: GoogleFonts.aBeeZee(
                    fontWeight: FontWeight.bold,
                    fontSize: 40,
                    color: themeColor,
                  ),
                ),
                Text(
                  "EXPRESSION",
                  style: GoogleFonts.aBeeZee(
                    fontWeight: FontWeight.bold,
                    fontSize: 40,
                    color: themeColor,
                  ),
                ),
                const SizedBox(height: 20),
                CarouselSlider(
                  items: [
                    Image.asset(
                      'assets/images/TBB-Banner-1-new.png',
                      fit: BoxFit.fitWidth,
                      width: double.maxFinite,
                    ),
                    Image.asset(
                      'assets/images/TBB-Banner-1-new.png',
                      fit: BoxFit.fitWidth,
                      width: double.maxFinite,
                    ),
                    Image.asset(
                      'assets/images/TBB-Banner-1-new.png',
                      fit: BoxFit.fitWidth,
                      width: double.maxFinite,
                    ),
                  ],
                  options: CarouselOptions(
                    viewportFraction: 1.0,
                    aspectRatio: 3,
                    scrollPhysics: const NeverScrollableScrollPhysics(),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  "Creme Castle is a bakery that specialises in Customized bakery products. It started as a café and bakery situated in Greater Noida with now presence in 3 cities: Gurugram, Noida and Greater Noida. Established in 2013 by a home baker and her son who is a graduate from IIT Delhi and FMS Delhi. Creme Castle is now Delhi NCR's #1 Designer cake bakery.",
                ),
                const SizedBox(height: 20),
                const Text(
                  "The team here constantly thrives to provide you unique and most personalized delicacies. We believe that we are not selling just cakes. Rather a cake is an expression by someone for someone to express their love and our clients entrust us with that responsibility to make that expression as beautiful as possible. We acknowledge our role very passionately.",
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
