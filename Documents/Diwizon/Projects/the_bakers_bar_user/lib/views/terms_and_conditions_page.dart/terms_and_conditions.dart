import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:the_bakers_bar_user/shared/theme.dart';
import 'package:the_bakers_bar_user/wrapper.dart';

class TermsAndConditions extends StatelessWidget {
  const TermsAndConditions({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Wrapper(
      body: Padding(
        padding: const EdgeInsets.all(40.0),
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "TERMS AND CONDITIONS",
              style: GoogleFonts.crimsonPro(
                fontSize: 42,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 40),
            Text(
              "Welcome to The Bakers Bar!",
              style: GoogleFonts.roboto(
                fontWeight: FontWeight.bold,
                fontSize: size.width <= 510 ? 16 : 20,
              ),
            ),
            const Text(
              "By using our website and placing an order, you agree to the following terms and conditions.",
            ),
            const SizedBox(height: 20),
            <PERSON>umn(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  textAlign: TextAlign.start,
                  "1. Ordering",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.bold,
                    fontSize: size.width <= 510 ? 16 : 20,
                    color: themeColor,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  "You can place orders through our website or by contacting us.\nAll orders depend on availability. We may cancel an order if necessary, but we’ll always inform you if that happens.\nPayments are required when you place your order.",
                ),
                const SizedBox(height: 20),
                Text(
                  "2. Pricing",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.bold,
                    fontSize: size.width <= 510 ? 16 : 20,
                    color: themeColor,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  "All prices are shown in INR and exclude taxes.\nPrices can change, but you’ll pay the price listed when you place your order.",
                ),
                const SizedBox(height: 20),
                Text(
                  "3. Delivery & Pick-up",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.bold,
                    fontSize: size.width <= 510 ? 16 : 20,
                    color: themeColor,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  "We deliver to certain areas, and delivery fees may apply.\nWe’ll try our best to deliver on time, but delays can happen. Please make sure your delivery details are correct.",
                ),
                const SizedBox(height: 20),
                // const Text(
                //     "Crème Castle does not accept or consider unsolicited ideas, including ideas for new advertising campaigns, new promotions, new or improved products or menu items, product enhancements, processes, recipes, materials, marketing plans or new product names. Please do not send any original creative artwork, suggestions or other works. The sole purpose of this policy is to avoid potential misunderstandings or disputes when Crème Castle’s products or marketing strategies might seem similar to ideas submitted to Crème Castle. So, please do not send your unsolicited ideas to Crème Castle or anyone at Crème Castle."),
                // const SizedBox(
                //   height: 20,
                // ),
                // const Text(
                //     "If, despite our request that you not send us your ideas, you still submit them, then regardless of what your letter says, the following terms shall apply to your submission. You agree and warrant that: (1) the submission will immediately become the sole and exclusive property of Crème Castle without compensation to you or any other person or party; (2) Crème Castle will consider the submission to be non-confidential and non-proprietary; (3) Crème Castle shall have no obligations concerning the submission, including but not limited to, no obligation to return any materials or acknowledge receipt of any submission; (4) Crème Castle may use or redistribute the submission or its content for any purpose and in any way it chooses; and (5) the submission does not contain trade secrets or proprietary information owned by another."),
                // const SizedBox(
                //   height: 20,
                // ),
                Text(
                  "4. Allergies",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.bold,
                    fontSize: size.width <= 510 ? 16 : 20,
                    color: themeColor,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  "Please tell us about any allergies or dietary needs when ordering.\nWhile we try to avoid cross-contamination, we can’t guarantee our products are allergen-free.",
                ),
                const SizedBox(height: 20),
                Text(
                  "5. Our Content",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.bold,
                    fontSize: size.width <= 510 ? 16 : 20,
                    color: themeColor,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  "All content on this site (text, images, etc.) belongs to The Bakers Bar.\nDon’t copy or use our content without our permission.",
                ),
                const SizedBox(height: 20),
                Text(
                  "6. Liability",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.bold,
                    fontSize: size.width <= 510 ? 16 : 20,
                    color: themeColor,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  "We’re not responsible for any issues that may arise during delivery or with the use of our products.",
                ),
                const SizedBox(height: 20),
                Text(
                  "7. Laws",
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.bold,
                    fontSize: size.width <= 510 ? 16 : 20,
                    color: themeColor,
                  ),
                ),
                const SizedBox(height: 20),
                const Text("These terms follow the laws of Vadodara."),
                const SizedBox(height: 30),
                const Text(
                  "If you have any questions, please contact us at +91 9265537579.",
                ),
                const SizedBox(height: 20),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
