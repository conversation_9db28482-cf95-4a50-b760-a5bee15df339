import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:razorpay_web/razorpay_web.dart';
import 'package:the_bakers_bar_user/shared/firebase.dart';
import 'package:the_bakers_bar_user/shared/methods.dart';

class RazorpayPG {
  // static const testMode = false;
  static const testMode = false;
  final keyId = testMode
      ? 'rzp_test_Ud2NZBYpeF83j4'
      : '***********************';
  // testMode ? 'rzp_test_UaHSIIL4XCcRR4' : '***********************';
  final keySecret = testMode
      ? '5ttTGTvEUyMwSgTgj8kWB849'
      : 'Vz0d97eFTBgvTKStQNyxFENG';
  // testMode ? 'XbsCKlmZnLX7YFTFtEZ2uWAH' : 'QmsLQEel8A2CUt0rrWHIdq9P';
  //  const url = 'https://api.razorpay.com/v1/orders';
  // final url = 'http://127.0.0.1:5001/chandresh-nathani/us-central1/createOrder';

  final razorpay = Razorpay();

  Future<void> createRazorpayOrder({
    required String orderDocId,
    required String receiptNo,
    required double amount,
  }) async {
    try {
      final result = await FBFunctions.ff.httpsCallable('createOrder').call({
        'amount': amount * 100,
        'receiptNo': receiptNo,
      });
      final response = result.data;
      debugPrint(response.toString());
      debugPrint(response['id'].toString());
      // return;
      // Init Payment //
      if (response['id'] != null) {
        // final responseData = json.decode(response.body);
        _initPayment(orderDocId, response['id'], response['amount']);
      } else {
        debugPrint('Error creating order');
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  _initPayment(String orderDocId, String orderId, double amount) async {
    try {
      var options = {
        'key': keyId,
        'amount': amount,
        'order_id': orderId,
        'name': "THE BAKERS BAR",
        // 'description': 'Fine T-Shirt',
        'prefill': {
          'contact': FBAuth.auth.currentUser?.phoneNumber,
          'email': FBAuth.auth.currentUser?.email,
        },
      };
      // _paymentListener(orderDocId);
      razorpay.open(options);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  _paymentListener(String orderDocId) async {
    razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, (response) {
      _handlePaymentSuccess(response, orderDocId);
    });
    razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, (response) {
      _handlePaymentError(response, orderDocId);
    });
    razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, (response) {
      _handleExternalWallet(response, orderDocId);
    });
  }

  void _handlePaymentSuccess(
    PaymentSuccessResponse response,
    String orderDocId,
  ) {
    log('Success Response: $response');
    showAppSnack("PAYMENT SUCCESSFUL");
  }

  void _handlePaymentError(PaymentFailureResponse response, String orderDocId) {
    log('Error Response: $response');
    showAppSnack("PAYMENT FAILED");
  }

  void _handleExternalWallet(
    ExternalWalletResponse response,
    String orderDocId,
  ) {
    log('External SDK Response: $response');
    showAppSnack("EXTERNAL SDK");
  }

  // Inactive/ Not used
  //  openPaymentPage(String orderId) async {
  //   try {
  //     final paymentUrl =
  //         'https://api.razorpay.com/v1/checkout/embedded/$orderId';
  //     html.window.open(paymentUrl, '_blank');
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }
}
