import 'package:the_bakers_bar_user/Models/designcostmodel.dart';
import 'package:the_bakers_bar_user/Models/our_collection.dart';

const double mobileMinSize = 768;
const double desktopMinSize = 1024;
const mobileMinSize2 = mobileMinSize - 200;
const mobileMinSize3 = mobileMinSize + 200;
const desktopMinSize2 = desktopMinSize - 200;

num priceCalc(
  num? flavourprice,
  num? indexcount,
  DesignCostModel? designcost,
  num? selectedweight,
  num fixedPrice,
  num intialweight,
) {
  if (fixedPrice == 0) {
    num incr = (designcost?.increment ?? 0) * (indexcount ?? 0);

    num price =
        ((flavourprice ?? 0) * 2) * (selectedweight ?? 0) +
        incr +
        (designcost?.basePrice ?? 0);
    return price;
  } else {
    num price =
        (fixedPrice * (intialweight.toString() == "0.5" ? 2 : 1)) *
        (selectedweight ?? 0);
    return price;
  }
}

List<OurCollectionModel> homeCatList = [
  OurCollectionModel(
    id: 'Daughter',
    name: 'Cakes For Daughter',
    image: 'assets/assets/images/cakefordaughter-min.png',
  ),
  OurCollectionModel(
    id: 'Son',
    name: 'Cakes For Son',
    image: 'assets/assets/images/cakeforson-min.png',
  ),
  OurCollectionModel(
    id: 'Her',
    name: 'Cakes For Her',
    image: 'assets/assets/images/cakeforher-min.png',
  ),
  OurCollectionModel(
    id: 'cakes for him',
    name: 'Cakes For Him',
    image: 'assets/assets/images/cakeforhim-min.png',
  ),
  OurCollectionModel(
    id: 'Wedding Cakes',
    name: 'Cakes For Wedding',
    image: 'assets/assets/images/weddingcake-min.png',
  ),
  OurCollectionModel(
    id: 'Baby Shower Cakes',
    name: 'Baby Shower',
    image: 'assets/assets/images/babyshower-min.png',
  ),
  // OurCollectionModel(
  // name: 'Anniversary Cakes',
  // image: 'assets/images/4 Customised Cupcakes.png'),
];

List<String> categories = [
  'Discount',
  "Doctor",
  "Traveller",
  "Him",
  "Engineer",
  "Soldier",
  "Cabin Crew",
  "Advocate",
  "Musician",
  "Biker",
  "Car lover",
  "Gamer",
  "Workaholic",
  "Lazy Dude",
  "TV Lovers",
  "Foodie",
  "Architect",
  "Corporate Cakes",
  "Social Media Cakes",
  "All Anniversary Cakes",
  "1st Anniversary",
  "25th Anniversary Cakes",
  "50th Anniversary Cakes",
  "Her",
  "Sister",
  "Mother",
  "Daughter",
  "Kids",
  "Boys",
  "Girls",
  "New Born",
  "Wedding Cakes",
  "Engagement Cakes",
  "Retirement Cakes",
  "Farewell Cakes",
  "Bride To Be Cakes",
  "Baby Shower Cakes",
  "Congratulations Cakes",
  "Photo cakes",
  "Number Cakes",
  "Dry Cakes",
  "Floral Cakes",
  "Cakes For Boys",
  "New Born Cakes",
  "Peppa Pig Cakes",
  "Frozen Cakes",
  "Avenger Cakes",
  "Masha & Bear Cakes",
  "Cocomelon Cakes",
  "Minion Cakes",
  "Jungle Cakes",
  "Jurassic Park Cakes",
  "Disney Princess Cakes",
  "Among Us Cakes",
  "All Cartoon Cakes",
  "Baby Shark Cakes",
  "Paw Patrol Cakes",
  "Lego Cakes",
  "Mickey Mouse Cakes",
  "Spiderman Cakes",
  "Barbie Cakes",
  "Mermaid Cakes",
  "Rainbow Cakes",
  "Solar System Cakes",
  "Minecraft Cakes",
  "Naruto Cakes",
  "Construction Theme Cakes",
  "Pinata Cakes",
  "Pull Up Cakes",
  "Bento Cakes",
  "Womens Day",
  "Mothers Day",
  "Fathers Day",
  "Friendship Day",
  "Rakhi",
  "Janmashtmi",
  "Teachers Day",
  "Grand Parents Day",
  "Boss's Day",
  "Halloween Cakes",
  "Christmas",
  "New Years",
  "Lohri",
  "Valentines Day",
  "1st Birthday",
  "Half Birthday Cakes",
  "Parent and Kid Cakes",
  "Chocolate Cakes",
  "Pineapple Cakes",
  "Fruit Cakes",
  "Red Velvet Cakes",
  "Cheese Cakes",
  "Butterscotch Cakes",
  "Blueberry Cakes",
  "Father",
  "Brother",
  "Son",
  "BTS Theme Cakes",
  "Cricket Theme Cakes",
  "Sport Theme Cakes",
  "Carnival Theme Cakes",
  "Cakes for Golfers",
  "Cakes for Footballers",
  "Gym Theme Cakes",
  "Makeup Theme Cakes",
  "Unicorn Cakes",
  "Bottle Shape Cakes",
  "Roblox Theme Cakes",
  "Shopping Theme Cakes",
  "Cake for Pubg Lovers",
  "Game of Thrones Cakes",
  "Cartoon Cakes",
];
List<String> byFlavours = [
  "chocolate cakes",
  "pineapple cakes",
  "fruit cakes",
  "red velvet cakes",
  "cheese cakes",
  "butterscotch cakes",
  "blueberry cakes",
];
  // List<String> data = [];
  //   for (CatSetModel element
  //       in Get.find<ProductCtrl>().settings?.catset ?? []) {
  //     for (var e in element.subCats) {
  //       data.add(e);
  //     }
  //   }
  //   for (var element in categories) {
  //     if (data.contains(element)) {
  //     } else {
  //       print(element);
  //     }
  //   }
  //   print(data.length);
  //   print(categories.length);