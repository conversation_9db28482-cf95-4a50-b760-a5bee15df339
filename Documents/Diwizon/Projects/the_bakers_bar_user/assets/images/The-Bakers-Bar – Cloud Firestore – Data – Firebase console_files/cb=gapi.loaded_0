gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([0x200000, ]);
var aa,fa,ha,na,oa,ta,va,xa;aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};fa=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
ha=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.ma=ha(this);na=function(a,b){if(b)a:{var c=_.ma;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&fa(c,a,{configurable:!0,writable:!0,value:b})}};
na("Symbol",function(a){if(a)return a;var b=function(f,h){this.c1=f;fa(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.c1};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
na("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.ma[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&fa(d.prototype,a,{configurable:!0,writable:!0,value:function(){return oa(aa(this))}})}return a});oa=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.sa=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error("b`"+String(a));};ta=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};va=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)ta(d,e)&&(a[e]=d[e])}return a};na("Object.assign",function(a){return a||va});
_.wa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")xa=Object.setPrototypeOf;else{var ya;a:{var Aa={a:!0},Ba={};try{Ba.__proto__=Aa;ya=Ba.a;break a}catch(a){}ya=!1}xa=ya?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.Da=xa;
na("Promise",function(a){function b(){this.Kf=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.YO=function(h){if(this.Kf==null){this.Kf=[];var k=this;this.ZO(function(){k.z7()})}this.Kf.push(h)};var d=_.ma.setTimeout;b.prototype.ZO=function(h){d(h,0)};b.prototype.z7=function(){for(;this.Kf&&this.Kf.length;){var h=this.Kf;this.Kf=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.nq(m)}}}this.Kf=null};b.prototype.nq=function(h){this.ZO(function(){throw h;
})};var e=function(h){this.Ga=0;this.Cf=void 0;this.Qr=[];this.iV=!1;var k=this.fF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.fF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.Rda),reject:h(this.RJ)}};e.prototype.Rda=function(h){if(h===this)this.RJ(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.wfa(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.Qda(h):this.mS(h)}};e.prototype.Qda=function(h){var k=void 0;try{k=h.then}catch(l){this.RJ(l);return}typeof k=="function"?this.xfa(k,h):this.mS(h)};e.prototype.RJ=function(h){this.XZ(2,h)};e.prototype.mS=function(h){this.XZ(1,h)};e.prototype.XZ=function(h,k){if(this.Ga!=0)throw Error("c`"+h+"`"+k+"`"+this.Ga);this.Ga=h;this.Cf=k;this.Ga===2&&this.gea();this.A7()};e.prototype.gea=function(){var h=this;d(function(){if(h.Yba()){var k=_.ma.console;typeof k!=="undefined"&&k.error(h.Cf)}},
1)};e.prototype.Yba=function(){if(this.iV)return!1;var h=_.ma.CustomEvent,k=_.ma.Event,l=_.ma.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.ma.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.Cf;return l(h)};e.prototype.A7=function(){if(this.Qr!=null){for(var h=0;h<this.Qr.length;++h)f.YO(this.Qr[h]);
this.Qr=null}};var f=new b;e.prototype.wfa=function(h){var k=this.fF();h.iy(k.resolve,k.reject)};e.prototype.xfa=function(h,k){var l=this.fF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,t){return typeof q=="function"?function(v){try{m(q(v))}catch(u){n(u)}}:t}var m,n,p=new e(function(q,t){m=q;n=t});this.iy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.iy=function(h,k){function l(){switch(m.Ga){case 1:h(m.Cf);
break;case 2:k(m.Cf);break;default:throw Error("d`"+m.Ga);}}var m=this;this.Qr==null?f.YO(l):this.Qr.push(l);this.iV=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.sa(h),n=m.next();!n.done;n=m.next())c(n.value).iy(k,l)})};e.all=function(h){var k=_.sa(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(v){return function(u){q[v]=u;t--;t==0&&m(q)}}var q=[],t=0;do q.push(void 0),t++,c(l.value).iy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Ea=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
na("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ea(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});
na("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!ta(l,f)){var m=new b;fa(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Ha=(h+=Math.random()+1).toString();if(l){l=_.sa(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!ta(l,f))throw Error("f`"+l);l[f][this.Ha]=m;return this};k.prototype.get=function(l){return c(l)&&ta(l,f)?l[f][this.Ha]:void 0};k.prototype.has=function(l){return c(l)&&ta(l,f)&&ta(l[f],this.Ha)};k.prototype.delete=
function(l){return c(l)&&ta(l,f)&&ta(l[f],this.Ha)?delete l[f][this.Ha]:!1};return k});
na("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.sa([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.sa(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.kf?m.kf.value=l:(m.kf={next:this[1],Yk:this[1].Yk,head:this[1],key:k,value:l},m.list.push(m.kf),this[1].Yk.next=m.kf,this[1].Yk=m.kf,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.kf&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.kf.Yk.next=k.kf.next,k.kf.next.Yk=
k.kf.Yk,k.kf.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Yk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).kf};c.prototype.get=function(k){return(k=d(this,k).kf)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&ta(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,kf:p}}return{id:m,list:n,index:-1,kf:void 0}},e=function(k,l){var m=k[1];return oa(function(){if(m){for(;m.head!=k[1];)m=m.Yk;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Yk=k.next=k.head=k},h=0;return c});na("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});na("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Ea(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});
var Ga=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};na("Array.prototype.keys",function(a){return a?a:function(){return Ga(this,function(b){return b})}});
na("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.sa([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Da=new Map;if(c){c=
_.sa(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Da.size};b.prototype.add=function(c){c=c===0?0:c;this.Da.set(c,c);this.size=this.Da.size;return this};b.prototype.delete=function(c){c=this.Da.delete(c);this.size=this.Da.size;return c};b.prototype.clear=function(){this.Da.clear();this.size=0};b.prototype.has=function(c){return this.Da.has(c)};b.prototype.entries=function(){return this.Da.entries()};b.prototype.values=function(){return this.Da.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Da.forEach(function(f){return c.call(d,f,f,e)})};return b});na("Array.prototype.entries",function(a){return a?a:function(){return Ga(this,function(b,c){return[b,c]})}});var Ja=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{pU:e,AD:f}}return{pU:-1,AD:void 0}};
na("Array.prototype.find",function(a){return a?a:function(b,c){return Ja(this,b,c).AD}});na("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});na("Array.prototype.values",function(a){return a?a:function(){return Ga(this,function(b,c){return c})}});
na("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});na("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)ta(b,d)&&c.push([d,b[d]]);return c}});
na("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)ta(b,d)&&c.push(b[d]);return c}});na("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});na("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
na("String.prototype.includes",function(a){return a?a:function(b,c){return Ea(this,b,"includes").indexOf(b,c||0)!==-1}});na("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});
na("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});na("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});na("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
na("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});na("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});na("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});var La=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
na("Array.prototype.at",function(a){return a?a:La});var Oa=function(a){return a?a:La};na("Int8Array.prototype.at",Oa);na("Uint8Array.prototype.at",Oa);na("Uint8ClampedArray.prototype.at",Oa);na("Int16Array.prototype.at",Oa);na("Uint16Array.prototype.at",Oa);na("Int32Array.prototype.at",Oa);na("Uint32Array.prototype.at",Oa);na("Float32Array.prototype.at",Oa);na("Float64Array.prototype.at",Oa);na("String.prototype.at",function(a){return a?a:La});
na("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});na("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ja(this,b,c).pU}});
na("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});
na("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Ea(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});na("globalThis",function(a){return a||_.ma});na("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});_.Pa={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Ra=_.Ra||{};_.Sa=this||self;_.Wa=_.Sa._F_toggles||[];_.Za="closure_uid_"+(Math.random()*1E9>>>0);_.$a=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.r=function(a,b){a=a.split(".");var c=_.Sa;a[0]in c||typeof c.execScript=="undefined"||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.bb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.tt=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.eb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0}});
var hb,lb;_.gb=function(a){return function(){return _.fb[a].apply(this,arguments)}};_.fb=[];hb=function(a,b,c){return a.call.apply(a.bind,arguments)};lb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};
_.z=function(a,b,c){_.z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?hb:lb;return _.z.apply(null,arguments)};
/*

 SPDX-License-Identifier: Apache-2.0
*/
var Hb,Mb,ac,pc,Bc,Dc,Hc,Kc,Mc,Nc,Rc,Sc,Vc;_.nb=function(a,b){return _.fb[a]=b};_.rb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.rb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.tY=!0};_.tb=function(a,b){return(0,_.sb)(a,b)>=0};_.ub=function(a,b){b=(0,_.sb)(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.vb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};
_.wb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};_.xb=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.yb=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.zb=function(a,b){for(var c in a)if(a[c]==b)return!0;return!1};_.Bb=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Ab.length;f++)c=Ab[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};
_.Gb=function(a){var b=arguments.length;if(b==1&&Array.isArray(arguments[0]))return _.Gb.apply(null,arguments[0]);for(var c={},d=0;d<b;d++)c[arguments[d]]=!0;return c};Hb=function(a){return{valueOf:a}.valueOf()};_.Kb=function(a){if(a instanceof _.Jb)return a.XX;throw Error("m");};Mb=function(a){return new Lb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};
_.Pb=function(a){var b=b===void 0?Nb:b;a:if(b=b===void 0?Nb:b,!(a instanceof _.Jb)){for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Lb&&d.Ej(a)){a=new _.Jb(a);break a}}a=void 0}return a||_.Ob};_.Sb=function(a){if(Qb.test(a))return a};_.Tb=function(a){return a instanceof _.Jb?_.Kb(a):_.Sb(a)};
_.Wb=function(a,b){b=b===void 0?{}:b;if(a instanceof _.Ub)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");b.bra&&(a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;"));b.fda&&(a=a.replace(/(\r\n|\n|\r)/g,"<br>"));b.cra&&(a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>'));return _.Vb(a)};_.Xb=function(){var a=_.Sa.navigator;return a&&(a=a.userAgent)?a:""};
ac=function(a){return _.Yb?_.Zb?_.Zb.brands.some(function(b){return(b=b.brand)&&_.$b(b,a)}):!1:!1};_.bc=function(a){return _.$b(_.Xb(),a)};_.cc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.dc=function(){return _.Yb?!!_.Zb&&_.Zb.brands.length>0:!1};_.ec=function(){return _.dc()?!1:_.bc("Opera")};_.fc=function(){return _.dc()?!1:_.bc("Trident")||_.bc("MSIE")};_.gc=function(){return _.dc()?!1:_.bc("Edge")};
_.hc=function(){return _.dc()?ac("Microsoft Edge"):_.bc("Edg/")};_.ic=function(){return _.dc()?ac("Opera"):_.bc("OPR")};_.jc=function(){return _.bc("Firefox")||_.bc("FxiOS")};_.lc=function(){return _.bc("Safari")&&!(_.kc()||(_.dc()?0:_.bc("Coast"))||_.ec()||_.gc()||_.hc()||_.ic()||_.jc()||_.bc("Silk")||_.bc("Android"))};_.kc=function(){return _.dc()?ac("Chromium"):(_.bc("Chrome")||_.bc("CriOS"))&&!_.gc()||_.bc("Silk")};_.mc=function(){return _.bc("Android")&&!(_.kc()||_.jc()||_.ec()||_.bc("Silk"))};
_.nc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.oc=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
pc=function(){return _.Yb?!!_.Zb&&!!_.Zb.platform:!1};_.qc=function(){return pc()?_.Zb.platform==="Android":_.bc("Android")};_.rc=function(){return _.bc("iPhone")&&!_.bc("iPod")&&!_.bc("iPad")};_.sc=function(){return _.rc()||_.bc("iPad")||_.bc("iPod")};_.tc=function(){return pc()?_.Zb.platform==="macOS":_.bc("Macintosh")};_.uc=function(){return pc()?_.Zb.platform==="Windows":_.bc("Windows")};_.vc=function(){return pc()?_.Zb.platform==="Chrome OS":_.bc("CrOS")};
_.xc=function(a,b){if(a.nodeType===1){var c=a.tagName;if(c==="SCRIPT"||c==="STYLE")throw Error("m");}a.innerHTML=_.wc(b)};_.yc=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};_.zc=function(a){if(!(a instanceof Array)){a=_.sa(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};
_.A=function(a,b){a.prototype=(0,_.wa)(b.prototype);a.prototype.constructor=a;if(_.Da)(0,_.Da)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.Ac=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.Cc=function(a,b){a=a.split(".");b=b||_.Sa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};
Dc=function(a,b){var c=_.Cc("WIZ_global_data.oxN3nb");a=c&&c[a];return a!=null?a:b};_.Fc=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.Gc=function(a){var b=_.Fc(a);return b=="array"||b=="object"&&typeof a.length=="number"};Hc=0;_.Ic=function(a){return Object.prototype.hasOwnProperty.call(a,_.Za)&&a[_.Za]||(a[_.Za]=++Hc)};_.Jc=function(){return Date.now()};Kc=function(a){return a};_.bb(_.rb,Error);_.rb.prototype.name="CustomError";Mc={};Nc={};
_.Oc=function(a,b){this.I_=a===Mc&&b||"";this.Y4=Nc};_.Oc.prototype.toString=function(){return this.I_};_.Pc=function(a){return a instanceof _.Oc&&a.constructor===_.Oc&&a.Y4===Nc?a.I_:"type_error:Const"};_.Qc=function(a){return new _.Oc(Mc,a)};Sc=function(){if(Rc===void 0){var a=null,b=_.Sa.trustedTypes;if(b&&b.createPolicy)try{a=b.createPolicy("gapi#html",{createHTML:Kc,createScript:Kc,createScriptURL:Kc})}catch(c){_.Sa.console&&_.Sa.console.error(c.message)}Rc=a}return Rc};
_.Tc=function(a){this.VX=a};_.Tc.prototype.toString=function(){return this.VX+""};_.Uc=function(a){if(a instanceof _.Tc&&a.constructor===_.Tc)return a.VX;_.Fc(a);return"type_error:TrustedResourceUrl"};Vc={};_.Xc=function(a){var b=Sc();a=b?b.createScriptURL(a):a;return new _.Tc(a,Vc)};_.Yc=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};
_.sb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Zc=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.$c=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.ad=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.bd=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.cd=Array.prototype.reduce?function(a,b,c){return Array.prototype.reduce.call(a,b,c)}:function(a,b,c){var d=c;(0,_.$c)(a,function(e,f){d=b.call(void 0,d,e,f,a)});return d};_.dd=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};
_.ed=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Ab="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");var jd,kd,ld,md,nd,od,id,qd;_.fd=function(a,b){return a.lastIndexOf(b,0)==0};_.gd=function(a){return/^[\s\xa0]*$/.test(a)};_.hd=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};
_.pd=function(a){if(!id.test(a))return a;a.indexOf("&")!=-1&&(a=a.replace(jd,"&amp;"));a.indexOf("<")!=-1&&(a=a.replace(kd,"&lt;"));a.indexOf(">")!=-1&&(a=a.replace(ld,"&gt;"));a.indexOf('"')!=-1&&(a=a.replace(md,"&quot;"));a.indexOf("'")!=-1&&(a=a.replace(nd,"&#39;"));a.indexOf("\x00")!=-1&&(a=a.replace(od,"&#0;"));return a};jd=/&/g;kd=/</g;ld=/>/g;md=/"/g;nd=/'/g;od=/\x00/g;id=/[\x00&<>"']/;_.$b=function(a,b){return a.indexOf(b)!=-1};
_.rd=function(a,b){var c=0;a=(0,_.hd)(String(a)).split(".");b=(0,_.hd)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=qd(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||qd(f[2].length==0,h[2].length==0)||qd(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
qd=function(a,b){return a<b?-1:a>b?1:0};_.Jb=function(a){this.XX=a};_.Jb.prototype.toString=function(){return this.XX};_.Ob=new _.Jb("about:invalid#zClosurez");var Lb,Nb,Qb;Lb=function(a){this.Ej=a};Nb=[Mb("data"),Mb("http"),Mb("https"),Mb("mailto"),Mb("ftp"),new Lb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.sd=Hb(function(){return typeof URL==="function"});Qb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.td={};_.ud=function(a){this.UX=a};_.ud.prototype.toString=function(){return this.UX.toString()};_.vd=new _.ud("",_.td);_.wd=RegExp("^[-+,.\"'%_!#/ a-zA-Z0-9\\[\\]]+$");_.xd=RegExp("\\b(url\\([ \t\n]*)('[ -&(-\\[\\]-~]*'|\"[ !#-\\[\\]-~]*\"|[!#-&*-\\[\\]-~]*)([ \t\n]*\\))","g");_.yd=RegExp("\\b(calc|cubic-bezier|fit-content|hsl|hsla|linear-gradient|matrix|minmax|radial-gradient|repeat|rgb|rgba|(rotate|scale|translate)(X|Y|Z|3d)?|steps|var)\\([-+*/0-9a-zA-Z.%#\\[\\], ]+\\)","g");var Cd;_.Ad={};_.Bd=function(a){this.TX=a};_.Bd.prototype.toString=function(){return this.TX.toString()};_.Dd=function(a){a=_.Pc(a);return a.length===0?Cd:new _.Bd(a,_.Ad)};Cd=new _.Bd("",_.Ad);var Ed;Ed={};_.Ub=function(a){this.SX=a};_.Ub.prototype.toString=function(){return this.SX.toString()};_.wc=function(a){if(a instanceof _.Ub&&a.constructor===_.Ub)return a.SX;_.Fc(a);return"type_error:SafeHtml"};_.Fd=function(a){return a instanceof _.Ub?a:_.Vb(_.pd(String(a)))};_.Vb=function(a){var b=Sc();a=b?b.createHTML(a):a;return new _.Ub(a,Ed)};_.Id=new _.Ub(_.Sa.trustedTypes&&_.Sa.trustedTypes.emptyHTML||"",Ed);var Kd=function(a,b,c,d){var e=new Map(Jd);this.M5=a;this.TQ=e;this.N5=b;this.A9=c;this.pT=d};var Ld="ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" "),
Jd=[["A",new Map([["href",{Bd:2}]])],["AREA",new Map([["href",{Bd:2}]])],["LINK",new Map([["href",{Bd:5,conditions:new Map([["rel",new Set("alternate author bookmark canonical cite help icon license next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" "))]])}]])],["SOURCE",new Map([["src",{Bd:5}],["srcset",{Bd:6}]])],["IMG",new Map([["src",{Bd:5}],["srcset",{Bd:6}]])],["VIDEO",new Map([["src",{Bd:5}]])],["AUDIO",new Map([["src",{Bd:5}]])]],Md="title aria-atomic aria-autocomplete aria-busy aria-checked aria-current aria-disabled aria-dropeffect aria-expanded aria-haspopup aria-hidden aria-invalid aria-label aria-level aria-live aria-multiline aria-multiselectable aria-orientation aria-posinset aria-pressed aria-readonly aria-relevant aria-required aria-selected aria-setsize aria-sort aria-valuemax aria-valuemin aria-valuenow aria-valuetext alt align autocapitalize autocomplete autocorrect autofocus autoplay bgcolor border cellpadding cellspacing checked color cols colspan controls datetime disabled download draggable enctype face formenctype frameborder height hreflang hidden ismap label lang loop max maxlength media minlength min multiple muted nonce open placeholder preload rel required reversed role rows rowspan selected shape size sizes slot span spellcheck start step summary translate type valign value width wrap itemscope itemtype itemid itemprop itemref".split(" "),
Nd=[["dir",{Bd:3,conditions:Hb(function(){return new Map([["dir",new Set(["auto","ltr","rtl"])]])})}],["async",{Bd:3,conditions:Hb(function(){return new Map([["async",new Set(["async"])]])})}],["cite",{Bd:2}],["loading",{Bd:3,conditions:Hb(function(){return new Map([["loading",new Set(["eager","lazy"])]])})}],["poster",{Bd:2}],["target",{Bd:3,conditions:Hb(function(){return new Map([["target",new Set(["_self","_blank"])]])})}]],Pd=new Kd(new Set(Ld),new Set(Md),new Map(Nd)),Qd=new Kd(new Set(Ld.concat(["BUTTON",
"INPUT"])),new Set(Hb(function(){return Md.concat(["class","id","name"])})),new Map(Hb(function(){return Nd.concat([["style",{Bd:1}]])}))),Rd=new Kd(new Set(Hb(function(){return Ld.concat("STYLE TITLE INPUT TEXTAREA BUTTON LABEL".split(" "))})),new Set(Hb(function(){return Md.concat(["class","id","tabindex","contenteditable","name"])})),new Map(Hb(function(){return Nd.concat([["style",{Bd:1}]])})),new Set(["data-","aria-"]));var Sd=function(a){this.MY=a};Sd.prototype.createTextNode=function(a){return document.createTextNode(a)};_.Td=Hb(function(){return new Sd(Pd)});_.Ud=Hb(function(){return new Sd(Qd)});_.Vd=Hb(function(){return new Sd(Rd)});var Wd=!!(_.Wa[0]&1024),Xd=!!(_.Wa[0]&32),Yd=!!(_.Wa[0]&2048),Zd=!!(_.Wa[0]&16),$d=!!(_.Wa[0]&8);var ae;ae=Dc(1,!0);_.Yb=Wd?Yd:Dc(610401301,!1);_.be=Wd?Xd||!Zd:Dc(188588736,ae);_.ce=Wd?Xd||!$d:Dc(645172343,ae);var de;de=_.Sa.navigator;_.Zb=de?de.userAgentData||null:null;var ee=function(a){ee[" "](a);return a};ee[" "]=function(){};_.fe=function(a,b){try{return ee(a[b]),!0}catch(c){}return!1};var ve,we,Be;_.ge=_.ec();_.he=_.fc();_.ie=_.bc("Edge");_.je=_.ie||_.he;_.ke=_.bc("Gecko")&&!(_.$b(_.Xb().toLowerCase(),"webkit")&&!_.bc("Edge"))&&!(_.bc("Trident")||_.bc("MSIE"))&&!_.bc("Edge");_.le=_.$b(_.Xb().toLowerCase(),"webkit")&&!_.bc("Edge");_.me=_.le&&_.bc("Mobile");_.ne=_.tc();_.oe=_.uc();_.pe=(pc()?_.Zb.platform==="Linux":_.bc("Linux"))||_.vc();_.qe=_.qc();_.re=_.rc();_.se=_.bc("iPad");_.te=_.bc("iPod");_.ue=_.sc();ve=function(){var a=_.Sa.document;return a?a.documentMode:void 0};
a:{var xe="",ye=function(){var a=_.Xb();if(_.ke)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.ie)return/Edge\/([\d\.]+)/.exec(a);if(_.he)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.le)return/WebKit\/(\S+)/.exec(a);if(_.ge)return/(?:Version)[ \/]?(\S+)/.exec(a)}();ye&&(xe=ye?ye[1]:"");if(_.he){var ze=ve();if(ze!=null&&ze>parseFloat(xe)){we=String(ze);break a}}we=xe}_.Ae=we;if(_.Sa.document&&_.he){var De=ve();Be=De?De:parseInt(_.Ae,10)||void 0}else Be=void 0;_.Ee=Be;try{(new self.OffscreenCanvas(0,0)).getContext("2d")}catch(a){};var Fe,He;Fe=_.Yc(function(){var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);b=a.firstChild.firstChild;a.innerHTML=_.wc(_.Id);return!b.parentElement});_.Ge=function(a,b){if(Fe())for(;a.lastChild;)a.removeChild(a.lastChild);a.innerHTML=_.wc(b)};He=/^[\w+/_-]+[=]{0,2}$/;_.Ie=function(a,b){b=(b||_.Sa).document;return b.querySelector?(a=b.querySelector(a))&&(a=a.nonce||a.getAttribute("nonce"))&&He.test(a)?a:"":""};_.Je=function(a,b){this.width=a;this.height=b};_.Ke=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.Je.prototype;_.g.clone=function(){return new _.Je(this.width,this.height)};_.g.Nx=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.Nx()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.Le=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.Me=Math.random()*2147483648|0;var Se,$e,Ze;_.Pe=function(a){return a?new _.Ne(_.Oe(a)):Bc||(Bc=new _.Ne)};_.Qe=function(a,b){return typeof b==="string"?a.getElementById(b):b};
_.Re=function(a,b,c,d){a=d||a;b=b&&b!="*"?String(b).toUpperCase():"";if(a.querySelectorAll&&a.querySelector&&(b||c))return a.querySelectorAll(b+(c?"."+c:""));if(c&&a.getElementsByClassName){a=a.getElementsByClassName(c);if(b){d={};for(var e=0,f=0,h;h=a[f];f++)b==h.nodeName&&(d[e++]=h);d.length=e;return d}return a}a=a.getElementsByTagName(b||"*");if(c){d={};for(f=e=0;h=a[f];f++)b=h.className,typeof b.split=="function"&&_.tb(b.split(/\s+/),c)&&(d[e++]=h);d.length=e;return d}return a};
_.Te=function(a,b){_.wb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:Se.hasOwnProperty(d)?a.setAttribute(Se[d],c):_.fd(d,"aria-")||_.fd(d,"data-")?a.setAttribute(d,c):a[d]=c})};Se={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.Ve=function(a){return _.Ue(a||window)};
_.Ue=function(a){a=a.document;a=_.We(a)?a.documentElement:a.body;return new _.Je(a.clientWidth,a.clientHeight)};_.Ye=function(a){return a?a.parentWindow||a.defaultView:window};_.af=function(a,b){var c=b[1],d=Ze(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.Te(d,c));b.length>2&&$e(a,d,b,2);return d};
$e=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.Gc(f)||_.yc(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.yc(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.$c(h?_.vb(f):f,e)}}};_.bf=function(a){return Ze(document,a)};
Ze=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.We=function(a){return a.compatMode=="CSS1Compat"};_.cf=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.df=function(a,b){$e(_.Oe(a),a,arguments,1)};_.ef=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.ff=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.gf=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.hf=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.jf=function(a){return _.yc(a)&&a.nodeType==1};
_.kf=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.Oe=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.lf=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.ef(a),a.appendChild(_.Oe(a).createTextNode(String(b)))};_.Ne=function(a){this.Rb=a||_.Sa.document||document};_.g=_.Ne.prototype;_.g.Ja=_.Pe;_.g.QK=_.gb(0);_.g.Ab=function(){return this.Rb};_.g.O=_.gb(1);_.g.getElementsByTagName=function(a,b){return(b||this.Rb).getElementsByTagName(String(a))};
_.g.aH=_.gb(2);_.g.wa=function(a,b,c){return _.af(this.Rb,arguments)};_.g.createElement=function(a){return Ze(this.Rb,a)};_.g.createTextNode=function(a){return this.Rb.createTextNode(String(a))};_.g.getWindow=function(){var a=this.Rb;return a.parentWindow||a.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.df;_.g.canHaveChildren=_.cf;_.g.Be=_.ef;_.g.LU=_.ff;_.g.removeNode=_.gf;_.g.kG=_.hf;_.g.isElement=_.jf;_.g.contains=_.kf;_.g.CG=_.Oe;_.g.Cj=_.gb(3);
_.mf=function(a){var b=_.Ac.apply(1,arguments);if(b.length===0)return _.Xc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.Xc(c)};_.nf=function(a){var b,c;return(a=(c=(b=a.document).querySelector)==null?void 0:c.call(b,"script[nonce]"))?a.nonce||a.getAttribute("nonce")||"":""};_.of=function(a,b){a.src=_.Uc(b);(b=_.nf(a.ownerDocument&&a.ownerDocument.defaultView||window))&&a.setAttribute("nonce",b)};_.pf=function(a){return a.raw=a};
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.qf=function(a){return a===null?"null":a===void 0?"undefined":a};_.rf=window;_.sf=document;_.tf=_.rf.location;_.uf=/\[native code\]/;_.vf=function(a,b,c){return a[b]=a[b]||c};_.wf=function(){var a;if((a=Object.create)&&_.uf.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.xf=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.Af=function(a,b){a=a||{};for(var c in a)_.xf(a,c)&&(b[c]=a[c])};_.Bf=_.vf(_.rf,"gapi",{});_.Cf=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.Df=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Ef=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Ff=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Hf=function(a,b,c){_.Gf(a,b,c,"add","at")};_.Gf=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.If={};_.If=_.vf(_.rf,"___jsl",_.wf());_.vf(_.If,"I",0);_.vf(_.If,"hel",10);var Jf,Kf,Lf,Mf,Pf,Nf,Of,Qf,Rf;Jf=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Kf=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Lf=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Mf=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Lf(a[d])&&!Lf(b[d])?Mf(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Lf(b[d])?[]:{},Mf(a[d],b[d])):a[d]=b[d])};
Pf=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Jf("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Nf())if(e=Of(c),d.push(25),typeof e===
"object")return e;return{}}};Nf=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Of=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Qf=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Mf(c,b);a.push(c)};
Rf=function(a){Kf(!0);var b=window.___gcfg,c=Jf("cu"),d=window.___gu;b&&b!==d&&(Qf(c,b),window.___gu=b);b=Jf("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Jf("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=Pf(f,h))&&b.push(f));a&&Qf(c,a);d=Jf("cd");a=0;for(b=d.length;a<b;++a)Mf(Kf(),d[a],!0);d=Jf("ci");a=0;for(b=d.length;a<b;++a)Mf(Kf(),d[a],!0);a=0;for(b=c.length;a<b;++a)Mf(Kf(),c[a],!0)};_.Sf=function(a,b){var c=Kf();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.Tf=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;Rf(c)};var Uf=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.vf(_.If,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};Uf&&Uf();Rf();_.r("gapi.config.get",_.Sf);_.r("gapi.config.update",_.Tf);
var Vf,Wf,Xf,Yf,Zf,$f,bg,fg,gg,hg,ig,cg,dg;Vf=function(a,b){var c=b.createRange();c.selectNode(b.body);a=_.Vb(a);return c.createContextualFragment(_.wc(a))};Wf=function(a){a=a.nodeName;return typeof a==="string"?a:"FORM"};Xf=function(a){a=a.nodeType;return a===1||typeof a!=="number"};Yf=function(a,b,c){a.setAttribute(b,c)};Zf=function(a,b){var c=new XMLHttpRequest;c.open("POST",a);c.setRequestHeader("Content-Type","application/json");c.send(b)};
$f=function(a,b){(typeof window!=="undefined"&&window.navigator&&window.navigator.sendBeacon!==void 0?navigator.sendBeacon.bind(navigator):Zf)("https://csp.withgoogle.com/csp/lcreport/"+a.WJ,JSON.stringify({host:window.location.hostname,type:b,additionalData:void 0}))};bg=function(a,b){try{_.ag(_.Vd,a)}catch(c){return $f(b,"H_SLSANITIZE"),!0}try{_.ag(_.Ud,a)}catch(c){return $f(b,"H_RSANITIZE"),!0}try{_.ag(_.Td,a)}catch(c){return $f(b,"H_SANITIZE"),!0}return!1};
_.eg=function(a,b){a=_.qf(a);var c;if(c=b){var d,e;c=Math.random()<((e=(d=b.wra)!=null?d:cg[b.WJ[0]])!=null?e:0)}if(c&&window.SAFEVALUES_REPORTING!==!1&&"DocumentFragment"in window){var f,h;Math.random()<((h=(f=b.oqa)!=null?f:dg[b.WJ[0]])!=null?h:0)&&$f(b,"HEARTBEAT");bg(a,b)||_.Wb(a).toString()!==a&&$f(b,"H_ESCAPE")}return _.Vb(a)};fg=["data:","http:","https:","mailto:","ftp:"];
gg=function(a,b,c){c=a.TQ.get(c);return(c==null?0:c.has(b))?c.get(b):a.N5.has(b)?{Bd:1}:(c=a.A9.get(b))?c:a.pT&&[].concat(_.zc(a.pT)).some(function(d){return b.indexOf(d)===0})?{Bd:1}:{Bd:0}};
hg=function(a,b,c){var d=Wf(b);c=c.createElement(d);b=b.attributes;for(var e=_.sa(b),f=e.next();!f.done;f=e.next()){var h=f.value;f=h.name;h=h.value;var k=gg(a.MY,f,d),l;a:{if(l=k.conditions){l=_.sa(l);for(var m=l.next();!m.done;m=l.next()){var n=_.sa(m.value);m=n.next().value;n=n.next().value;var p=void 0;if((m=(p=b.getNamedItem(m))==null?void 0:p.value)&&!n.has(m)){l=!1;break a}}}l=!0}if(l)switch(k.Bd){case 1:Yf(c,f,h);break;case 2:a:if(k=void 0,_.sd){try{k=new URL(h)}catch(q){k="https:";break a}k=
k.protocol}else b:{k=document.createElement("a");try{k.href=h}catch(q){k=void 0;break b}k=k.protocol;k=k===":"||k===""?"https:":k}Yf(c,f,k!==void 0&&fg.indexOf(k.toLowerCase())!==-1?h:"about:invalid#zClosurez");break;case 3:Yf(c,f,h.toLowerCase());break;case 4:Yf(c,f,h);break;case 5:Yf(c,f,h);break;case 6:Yf(c,f,h)}}return c};
ig=function(a,b,c){b=Vf(b,c);b=document.createTreeWalker(b,5,function(k){if(k.nodeType===3)k=1;else if(Xf(k))if(k=Wf(k),k===null)k=2;else{var l=a.MY;k=k!=="FORM"&&(l.M5.has(k)||l.TQ.has(k))?1:2}else k=2;return k});for(var d=b.nextNode(),e=c.createDocumentFragment(),f=e;d!==null;){var h=void 0;if(d.nodeType===3)h=a.createTextNode(d.data);else if(Xf(d))h=hg(a,d,c);else throw Error("m");f.appendChild(h);if(d=b.firstChild())f=h;else for(;!(d=b.nextSibling())&&(d=b.parentNode());)f=f.parentNode}return e};
_.ag=function(a,b){var c=document.implementation.createHTMLDocument("");a=ig(a,b,c);c=c.body;c.appendChild(a);c=(new XMLSerializer).serializeToString(c);c=c.slice(c.indexOf(">")+1,c.lastIndexOf("</"));return _.Vb(c)};cg={0:1,1:1};dg={0:.1,1:.1};
var og,pg,qg,rg,sg,tg,ug,vg,wg,xg,yg,zg,Bg,Cg,Dg,Eg,Fg,Gg,Hg,Ig,Jg,Kg,Lg,Mg,Ng,Og,Pg,Qg,Rg,Sg,Tg,Wg,Xg;qg=void 0;rg=function(a){try{return _.Sa.JSON.parse.call(_.Sa.JSON,a)}catch(b){return!1}};sg=function(a){return Object.prototype.toString.call(a)};tg=sg(0);ug=sg(new Date(0));vg=sg(!0);wg=sg("");xg=sg({});yg=sg([]);
zg=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=sg(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==yg||a.constructor!==Array&&a.constructor!==Object)&&(e!==xg||a.constructor!==Array&&a.constructor!==Object)&&e!==wg&&e!==tg&&e!==vg&&e!==ug))return zg(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===tg)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===vg)b[b.length]=String(!!Number(a));else{if(e===ug)return zg(a.toISOString.call(a),c);if(e===yg&&sg(a.length)===tg){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=zg(a[f],c)||"null";b[b.length]="]"}else if(e==wg&&sg(a.length)===tg){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=zg(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=zg(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};Bg=/[\0-\x07\x0b\x0e-\x1f]/;
Cg=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;Dg=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;Eg=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;Fg=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;Gg=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Hg=/[ \t\n\r]+/g;Ig=/[^"]:/;Jg=/""/g;Kg=/true|false|null/g;Lg=/00/;Mg=/[\{]([^0\}]|0[^:])/;Ng=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Og=/[^\[,:][\[\{]/;Pg=/^(\{|\}|\[|\]|,|:|0)+/;Qg=/\u2028/g;
Rg=/\u2029/g;
Sg=function(a){a=String(a);if(Bg.test(a)||Cg.test(a)||Dg.test(a)||Eg.test(a))return!1;var b=a.replace(Fg,'""');b=b.replace(Gg,"0");b=b.replace(Hg,"");if(Ig.test(b))return!1;b=b.replace(Jg,"0");b=b.replace(Kg,"0");if(Lg.test(b)||Mg.test(b)||Ng.test(b)||Og.test(b)||!b||(b=b.replace(Pg,"")))return!1;a=a.replace(Qg,"\\u2028").replace(Rg,"\\u2029");b=void 0;try{b=qg?[rg(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Tg=function(){var a=((_.Sa.document||{}).scripts||[]).length;if((og===void 0||qg===void 0||pg!==a)&&pg!==-1){og=qg=!1;pg=-1;try{try{qg=!!_.Sa.JSON&&_.Sa.JSON.stringify.call(_.Sa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&rg("true")===!0&&rg('[{"a":3}]')[0].a===3}catch(b){}og=qg&&!rg("[00]")&&!rg('"\u0007"')&&!rg('"\\0"')&&!rg('"\\v"')}finally{pg=a}}};_.Ug=function(a){if(pg===-1)return!1;Tg();return(og?rg:Sg)(a)};
_.Vg=function(a){if(pg!==-1)return Tg(),qg?_.Sa.JSON.stringify.call(_.Sa.JSON,a):zg(a)};Wg=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Xg=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Wg?Xg:Date.prototype.toISOString;
var Ph=function(){this.blockSize=-1};var Qh=function(){this.blockSize=-1;this.blockSize=64;this.Vc=[];this.FE=[];this.o5=[];this.AB=[];this.AB[0]=128;for(var a=1;a<this.blockSize;++a)this.AB[a]=0;this.oD=this.nr=0;this.reset()};_.bb(Qh,Ph);Qh.prototype.reset=function(){this.Vc[0]=1732584193;this.Vc[1]=4023233417;this.Vc[2]=2562383102;this.Vc[3]=271733878;this.Vc[4]=3285377520;this.oD=this.nr=0};
var Rh=function(a,b,c){c||(c=0);var d=a.o5;if(typeof b==="string")for(var e=0;e<16;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;e<16;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(e=16;e<80;e++){var f=d[e-3]^d[e-8]^d[e-14]^d[e-16];d[e]=(f<<1|f>>>31)&4294967295}b=a.Vc[0];c=a.Vc[1];var h=a.Vc[2],k=a.Vc[3],l=a.Vc[4];for(e=0;e<80;e++){if(e<40)if(e<20){f=k^c&(h^k);var m=1518500249}else f=c^h^k,m=1859775393;else e<60?(f=c&h|k&(c|h),
m=2400959708):(f=c^h^k,m=3395469782);f=(b<<5|b>>>27)+f+l+m+d[e]&4294967295;l=k;k=h;h=(c<<30|c>>>2)&4294967295;c=b;b=f}a.Vc[0]=a.Vc[0]+b&4294967295;a.Vc[1]=a.Vc[1]+c&4294967295;a.Vc[2]=a.Vc[2]+h&4294967295;a.Vc[3]=a.Vc[3]+k&4294967295;a.Vc[4]=a.Vc[4]+l&4294967295};
Qh.prototype.update=function(a,b){if(a!=null){b===void 0&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.FE,f=this.nr;d<b;){if(f==0)for(;d<=c;)Rh(this,a,d),d+=this.blockSize;if(typeof a==="string")for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){Rh(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){Rh(this,e);f=0;break}}this.nr=f;this.oD+=b}};
Qh.prototype.digest=function(){var a=[],b=this.oD*8;this.nr<56?this.update(this.AB,56-this.nr):this.update(this.AB,this.blockSize-(this.nr-56));for(var c=this.blockSize-1;c>=56;c--)this.FE[c]=b&255,b/=256;Rh(this,this.FE);for(c=b=0;c<5;c++)for(var d=24;d>=0;d-=8)a[b]=this.Vc[c]>>d&255,++b;return a};_.Sh=function(){this.yM=new Qh};_.g=_.Sh.prototype;_.g.reset=function(){this.yM.reset()};_.g.x0=function(a){this.yM.update(a)};_.g.xQ=function(){return this.yM.digest()};_.g.jx=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=0,d=a.length;c<d;++c)b.push(a.charCodeAt(c));this.x0(b)};_.g.Zi=function(){for(var a=this.xQ(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};
_.Yi=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};_.Zi=function(a){var b=_.Yi();if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&typeof b==="object"&&c<d;++c)b=b[a[c]];return c===a.length&&b!==void 0?b:void 0};
_.$i=function(a,b){b=_.Tb(b);b!==void 0&&(a.href=b)};var aj;aj=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//;
_.bj=function(a){var b=_.Zi("googleapis.config/sessionIndex");"string"===typeof b&&b.length>254&&(b=null);b==null&&(b=window.__X_GOOG_AUTHUSER);"string"===typeof b&&b.length>254&&(b=null);if(b==null){var c=window.google;c&&(b=c.authuser)}"string"===typeof b&&b.length>254&&(b=null);b==null&&(a=a||window.location.href,b=_.Cf(a,"authuser")||null,b==null&&(b=(b=a.match(aj))?b[1]:null));if(b==null)return null;b=String(b);b.length>254&&(b=null);return b};
var vj,uj,Bj,Cj,wj,zj,xj,Dj,yj;_.Aj=function(){if(uj){var a=new _.rf.Uint32Array(1);vj.getRandomValues(a);a=Number("0."+a[0])}else a=wj,a+=parseInt(xj.substr(0,20),16),xj=yj(xj),a/=zj+Math.pow(16,20);return a};vj=_.rf.crypto;uj=!1;Bj=0;Cj=0;wj=1;zj=0;xj="";Dj=function(a){a=a||_.rf.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;wj=wj*b%zj;Bj>0&&++Cj==Bj&&_.Gf(_.rf,"mousemove",Dj,"remove","de")};yj=function(a){var b=new _.Sh;b.jx(a);return b.Zi()};
uj=!!vj&&typeof vj.getRandomValues=="function";uj||(zj=(screen.width*screen.width+screen.height)*1E6,xj=yj(_.sf.cookie+"|"+_.sf.location+"|"+(new Date).getTime()+"|"+Math.random()),Bj=_.Zi("random/maxObserveMousemove")||0,Bj!=0&&_.Hf(_.rf,"mousemove",Dj));
var tm,um,vm,wm,xm,ym,zm,Am,Bm,Cm,Dm,Em,Im,Jm,Km,Lm,Mm,Nm,Om,Pm;_.sm=function(a,b){if(!a)throw Error(b||"");};tm=/&/g;um=/</g;vm=/>/g;wm=/"/g;xm=/'/g;ym=function(a){return String(a).replace(tm,"&amp;").replace(um,"&lt;").replace(vm,"&gt;").replace(wm,"&quot;").replace(xm,"&#39;")};zm=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g;Am=/%([a-f]|[0-9a-fA-F][a-f])/g;Bm=/^(https?|ftp|file|chrome-extension):$/i;
Cm=function(a){a=String(a);a=a.replace(zm,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(_.Ef,function(e){return e.replace(/%/g,"%25")}).replace(Am,function(e){return e.toUpperCase()});a=a.match(_.Df)||[];var b=_.wf(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,"%7D")},d=!!(a[1]||"").match(Bm);b.tt=c((a[1]||"")+(a[2]||"")+(a[3]||
(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.hj=a[7]?[d(a[7])]:[];return b};Dm=function(a){return a.tt+(a.query.length>0?"?"+a.query.join("&"):"")+(a.hj.length>0?"#"+a.hj.join("&"):"")};Em=function(a,b){var c=[];if(a)for(var d in a)if(_.xf(a,d)&&a[d]!=null){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c};
_.Fm=function(a,b,c,d){a=Cm(a);a.query.push.apply(a.query,Em(b,d));a.hj.push.apply(a.hj,Em(c,d));return Dm(a)};
_.Gm=function(a,b){var c=Cm(b);b=c.tt;c.query.length&&(b+="?"+c.query.join(""));c.hj.length&&(b+="#"+c.hj.join(""));var d="";b.length>2E3&&(c=b,b=b.substr(0,2E3),b=b.replace(_.Ff,""),d=c.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=Cm(b);b=c.tt;c.query.length&&(b+="?"+c.query.join(""));c.hj.length&&(b+="#"+c.hj.join(""));_.$i(a,new _.Jb(_.qf(b)));e.appendChild(a);_.xc(e,_.Vb(e.innerHTML));b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);c=Cm(b+d);b=
c.tt;c.query.length&&(b+="?"+c.query.join(""));c.hj.length&&(b+="#"+c.hj.join(""));return b};_.Hm=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;Jm=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};Km=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//;
Lm=function(){var a=_.Zi("googleapis.config/sessionDelegate");"string"===typeof a&&a.length>21&&(a=null);a==null&&(a=(a=window.location.href.match(Km))?a[1]:null);if(a==null)return null;a=String(a);a.length>21&&(a=null);return a};Mm=function(){var a=_.If.onl;if(!a){a=_.wf();_.If.onl=a;var b=_.wf();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a};Nm=function(a,b){b=b.onload;return typeof b==="function"?(Mm().a(a,b),b):null};
Om=function(a){_.sm(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'};Pm=function(a){Mm().r(a)};var Rm,Sm,Wm;_.Qm={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"};Rm={allowtransparency:!0,onload:!0};Sm=0;_.Tm=function(a,b){var c=0;do var d=b.id||["I",Sm++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&++c<5);_.sm(c<5,"Error creating iframe id");return d};_.Um=function(a,b){return a?b+"/"+a:""};
_.Vm=function(a,b,c,d){var e={},f={};a.documentMode&&a.documentMode<9&&(e.hostiemode=a.documentMode);_.Af(d.queryParams||{},e);_.Af(d.fragmentParams||{},f);var h=d.pfname;var k=_.wf();_.Zi("iframes/dropLegacyIdParam")||(k.id=c);k._gfid=c;k.parent=a.location.protocol+"//"+a.location.host;c=_.Cf(a.location.href,"parent");h=h||"";!h&&c&&(h=_.Cf(a.location.href,"_gfid","")||_.Cf(a.location.href,"id",""),h=_.Um(h,_.Cf(a.location.href,"pfname","")));h||(c=_.Ug(_.Cf(a.location.href,"jcp","")))&&typeof c==
"object"&&(h=_.Um(c.id,c.pfname));k.pfname=h;d.connectWithJsonParam&&(h={},h.jcp=_.Vg(k),k=h);h=_.Cf(b,"rpctoken")||e.rpctoken||f.rpctoken;h||(h=d.rpctoken||String(Math.round(_.Aj()*1E8)),k.rpctoken=h);d.rpctoken=h;_.Af(k,d.connectWithQueryParams?e:f);k=a.location.href;a=_.wf();(h=_.Cf(k,"_bsh",_.If.bsh))&&(a._bsh=h);(k=_.If.dpo?_.If.h:_.Cf(k,"jsh",_.If.h))&&(a.jsh=k);d.hintInFragment?_.Af(a,f):_.Af(a,e);return _.Fm(b,e,f,d.paramsSerializer)};
Wm=function(a){_.sm(!a||_.Hm.test(a),"Illegal url for new iframe - "+a)};
_.Xm=function(a,b,c,d,e){Wm(c.src);var f,h=Nm(d,c),k=h?Om(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+ym(String(c.frameborder))+'" scrolling="'+ym(String(c.scrolling))+'" '+k+' name="'+ym(String(c.name))+'"/>'))}catch(m){}finally{f||(f=_.Pe(a).wa("IFRAME"),h&&(f.onload=function(){f.onload=null;h.call(this)},Pm(d)))}f.setAttribute("ng-non-bindable","");for(var l in c)a=c[l],l==="style"&&typeof a==="object"?_.Af(a,f.style):Rm[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||
null)||e&&e.dontclear||Jm(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};var Ym,an;Ym=/^:[\w]+$/;_.Zm=/:([a-zA-Z_]+):/g;_.$m=function(){var a=_.bj()||"0",b=Lm();var c=_.bj()||a;var d=Lm(),e="";c&&(e+="u/"+encodeURIComponent(String(c))+"/");d&&(e+="b/"+encodeURIComponent(String(d))+"/");c=e||null;(e=(d=_.Zi("isLoggedIn")===!1)?"_/im/":"")&&(c="");var f=_.Zi("iframes/:socialhost:"),h=_.Zi("iframes/:im_socialhost:");return Im={socialhost:f,ctx_socialhost:d?h:f,session_index:a,session_delegate:b,session_prefix:c,im_prefix:e}};an=function(a,b){return _.$m()[b]||""};
_.bn=function(a){return _.Gm(_.sf,a.replace(_.Zm,an))};_.cn=function(a){var b=a;Ym.test(a)&&(b="iframes/"+b.substring(1)+"/url",b=_.Zi(b),_.sm(!!b,"Unknown iframe url config for - "+a));return _.bn(b)};
_.dn=function(a,b,c){c=c||{};var d=c.attributes||{};_.sm(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=_.cn(a);d=b.ownerDocument||_.sf;var e=_.Tm(d,c);a=_.Vm(d,a,e,c);var f=c,h=_.wf();_.Af(_.Qm,h);_.Af(f.attributes,h);h.name=h.id=e;h.src=a;c.eurl=a;c=(f=c)||{};var k=!!c.allowPost;if(c.forcePost||k&&a.length>2E3){c=Cm(a);h.src="";f.dropDataPostorigin||(h["data-postorigin"]=a);a=_.Xm(d,b,h,e);if(navigator.userAgent.indexOf("WebKit")!=-1){var l=
a.contentWindow.document;l.open();h=l.createElement("div");k={};var m=e+"_inner";k.name=m;k.src="";k.style="display:none";_.Xm(d,h,k,m,f)}h=(f=c.query[0])?f.split("&"):[];f=[];for(k=0;k<h.length;k++)m=h[k].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];h=Dm(c);_.sm(_.Hm.test(h),"Invalid URL: "+h);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=_.Tb(h);e!==void 0&&(c.action=e);for(e=0;e<f.length;e++)h=d.createElement("input"),h.type=
"hidden",h.name=f[e][0],h.value=f[e][1],c.appendChild(h);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=_.Xm(d,b,h,e,f);return b};
var Yg=function(){this.Tg=window.console};Yg.prototype.log=function(a){this.Tg&&this.Tg.log&&this.Tg.log(a)};Yg.prototype.error=function(a){this.Tg&&(this.Tg.error?this.Tg.error(a):this.Tg.log&&this.Tg.log(a))};Yg.prototype.warn=function(a){this.Tg&&(this.Tg.warn?this.Tg.warn(a):this.Tg.log&&this.Tg.log(a))};Yg.prototype.debug=function(){};_.Zg=new Yg;
_.Nh=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("A`"+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&c!=="moz-extension"&&
c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("B`"+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};
_.vi=function(a){_.Sa.setTimeout(function(){throw a;},0)};
_.wi=_.jc();_.xi=_.rc()||_.bc("iPod");_.zi=_.bc("iPad");_.Ai=_.mc();_.Bi=_.kc();_.Ci=_.lc()&&!_.sc();
_.Rj=[];_.Sj=[];_.Tj=!1;_.Uj=function(a){_.Rj[_.Rj.length]=a;if(_.Tj)for(var b=0;b<_.Sj.length;b++)a((0,_.z)(_.Sj[b].wrap,_.Sj[b]))};
_.Ek=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.Gc(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Fk=function(a,b,c,d){for(var e=0,f=a.length,h;e<f;){var k=e+(f-e>>>1);var l=c?b.call(void 0,a[k],k,a):b(d,a[k]);l>0?e=k+1:(f=k,h=!l)}return h?e:-e-1};_.Gk=function(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c};_.Hk=function(a,b,c,d){b=_.Tb(b);return b!==void 0?a.open(b,c,d):null};
var Ik=function(a){this.T=a};_.g=Ik.prototype;_.g.value=function(){return this.T};_.g.We=function(a){this.T.width=a;return this};_.g.Xb=function(){return this.T.width};_.g.ee=function(a){this.T.height=a;return this};_.g.Qc=function(){return this.T.height};_.g.Ah=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.Jk=function(a){this.T=a||{}};_.g=_.Jk.prototype;_.g.value=function(){return this.T};_.g.setUrl=function(a){this.T.url=a;return this};_.g.getUrl=function(){return this.T.url};_.g.Ah=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.g.Ve=function(a){this.T.id=a;return this};_.g.getId=function(){return this.T.id};_.g.wn=function(a){this.T.rpctoken=a;return this};_.Kk=function(a,b){a.T.messageHandlers=b;return a};_.Lk=function(a,b){a.T.messageHandlersFilter=b;return a};
_.g=_.Jk.prototype;_.g.ss=_.gb(4);_.g.getContext=function(){return this.T.context};_.g.xd=function(){return this.T.openerIframe};_.g.wo=function(){this.T.attributes=this.T.attributes||{};return new Ik(this.T.attributes)};_.g.mj=function(){return this.T.controller};
var Sk;_.Mk=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.yc(f)?"o"+_.Ic(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Nk=function(a){var b={},c;for(c in a)b[c]=a[c];return b};_.Ok=function(){};_.Pk=function(a){return a};_.Qk=function(a){a.prototype.$goog_Thenable=!0};_.Rk=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};Sk=function(a,b){this.V6=a;this.Oda=b;this.mB=0;this.jA=null};
Sk.prototype.get=function(){if(this.mB>0){this.mB--;var a=this.jA;this.jA=a.next;a.next=null}else a=this.V6();return a};Sk.prototype.put=function(a){this.Oda(a);this.mB<100&&(this.mB++,a.next=this.jA,this.jA=a)};var Uk,Vk,Tk;_.Wk=function(a){a=Tk(a);typeof _.Sa.setImmediate!=="function"||_.Sa.Window&&_.Sa.Window.prototype&&_.Sa.Window.prototype.setImmediate==_.Sa.setImmediate?(Uk||(Uk=Vk()),Uk(a)):_.Sa.setImmediate(a)};
Vk=function(){var a=_.Sa.MessageChannel;typeof a==="undefined"&&typeof window!=="undefined"&&window.postMessage&&window.addEventListener&&!_.bc("Presto")&&(a=function(){var e=_.bf("IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var h="callImmediate"+Math.random(),k=f.location.protocol=="file:"?"*":f.location.protocol+"//"+f.location.host;e=(0,_.z)(function(l){if((k=="*"||l.origin==k)&&l.data==h)this.port1.onmessage()},
this);f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(h,k)}}});if(typeof a!=="undefined"){var b=new a,c={},d=c;b.port1.onmessage=function(){if(c.next!==void 0){c=c.next;var e=c.cb;c.cb=null;e()}};return function(e){d.next={cb:e};d=d.next;b.port2.postMessage(0)}}return function(e){_.Sa.setTimeout(e,0)}};Tk=_.Pk;_.Uj(function(a){Tk=a});var Xk=function(){this.ED=this.Us=null};Xk.prototype.add=function(a,b){var c=Yk.get();c.set(a,b);this.ED?this.ED.next=c:this.Us=c;this.ED=c};Xk.prototype.remove=function(){var a=null;this.Us&&(a=this.Us,this.Us=this.Us.next,this.Us||(this.ED=null),a.next=null);return a};var Yk=new Sk(function(){return new Zk},function(a){return a.reset()}),Zk=function(){this.next=this.scope=this.Zh=null};Zk.prototype.set=function(a,b){this.Zh=a;this.scope=b;this.next=null};
Zk.prototype.reset=function(){this.next=this.scope=this.Zh=null};var $k,al,bl,cl,el;al=!1;bl=new Xk;_.dl=function(a,b){$k||cl();al||($k(),al=!0);bl.add(a,b)};cl=function(){if(_.Sa.Promise&&_.Sa.Promise.resolve){var a=_.Sa.Promise.resolve(void 0);$k=function(){a.then(el)}}else $k=function(){_.Wk(el)}};el=function(){for(var a;a=bl.remove();){try{a.Zh.call(a.scope)}catch(b){_.vi(b)}Yk.put(a)}al=!1};var hl,il,jl,xl,Bl,zl,Cl;_.gl=function(a,b){this.Ga=0;this.Cf=void 0;this.sq=this.Ql=this.wb=null;this.Zz=this.JF=!1;if(a!=_.Ok)try{var c=this;a.call(b,function(d){fl(c,2,d)},function(d){fl(c,3,d)})}catch(d){fl(this,3,d)}};hl=function(){this.next=this.context=this.Pr=this.Ev=this.Nn=null;this.nt=!1};hl.prototype.reset=function(){this.context=this.Pr=this.Ev=this.Nn=null;this.nt=!1};il=new Sk(function(){return new hl},function(a){a.reset()});
jl=function(a,b,c){var d=il.get();d.Ev=a;d.Pr=b;d.context=c;return d};_.kl=function(a){if(a instanceof _.gl)return a;var b=new _.gl(_.Ok);fl(b,2,a);return b};_.ll=function(a){return new _.gl(function(b,c){c(a)})};_.nl=function(a,b,c){ml(a,b,c,null)||_.dl(_.$a(b,a))};_.ol=function(a){return new _.gl(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},h=function(m){c(m)},k=0,l;k<a.length;k++)l=a[k],_.nl(l,_.$a(f,k),h);else b(e)})};
_.ql=function(){var a,b,c=new _.gl(function(d,e){a=d;b=e});return new pl(c,a,b)};_.gl.prototype.then=function(a,b,c){return rl(this,typeof a==="function"?a:null,typeof b==="function"?b:null,c)};_.Qk(_.gl);_.tl=function(a,b){b=jl(b,b);b.nt=!0;sl(a,b);return a};_.gl.prototype.Ow=function(a,b){return rl(this,null,a,b)};_.gl.prototype.catch=_.gl.prototype.Ow;_.gl.prototype.cancel=function(a){if(this.Ga==0){var b=new _.ul(a);_.dl(function(){vl(this,b)},this)}};
var vl=function(a,b){if(a.Ga==0)if(a.wb){var c=a.wb;if(c.Ql){for(var d=0,e=null,f=null,h=c.Ql;h&&(h.nt||(d++,h.Nn==a&&(e=h),!(e&&d>1)));h=h.next)e||(f=h);e&&(c.Ga==0&&d==1?vl(c,b):(f?(d=f,d.next==c.sq&&(c.sq=d),d.next=d.next.next):wl(c),xl(c,e,3,b)))}a.wb=null}else fl(a,3,b)},sl=function(a,b){a.Ql||a.Ga!=2&&a.Ga!=3||yl(a);a.sq?a.sq.next=b:a.Ql=b;a.sq=b},rl=function(a,b,c,d){var e=jl(null,null,null);e.Nn=new _.gl(function(f,h){e.Ev=b?function(k){try{var l=b.call(d,k);f(l)}catch(m){h(m)}}:f;e.Pr=c?
function(k){try{var l=c.call(d,k);l===void 0&&k instanceof _.ul?h(k):f(l)}catch(m){h(m)}}:h});e.Nn.wb=a;sl(a,e);return e.Nn};_.gl.prototype.oga=function(a){this.Ga=0;fl(this,2,a)};_.gl.prototype.pga=function(a){this.Ga=0;fl(this,3,a)};
var fl=function(a,b,c){a.Ga==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Ga=1,ml(c,a.oga,a.pga,a)||(a.Cf=c,a.Ga=b,a.wb=null,yl(a),b!=3||c instanceof _.ul||zl(a,c)))},ml=function(a,b,c,d){if(a instanceof _.gl)return sl(a,jl(b||_.Ok,c||null,d)),!0;if(_.Rk(a))return a.then(b,c,d),!0;if(_.yc(a))try{var e=a.then;if(typeof e==="function")return Al(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Al=function(a,b,c,d,e){var f=!1,h=function(l){f||(f=!0,c.call(e,l))},k=function(l){f||
(f=!0,d.call(e,l))};try{b.call(a,h,k)}catch(l){k(l)}},yl=function(a){a.JF||(a.JF=!0,_.dl(a.Yy,a))},wl=function(a){var b=null;a.Ql&&(b=a.Ql,a.Ql=b.next,b.next=null);a.Ql||(a.sq=null);return b};_.gl.prototype.Yy=function(){for(var a;a=wl(this);)xl(this,a,this.Ga,this.Cf);this.JF=!1};xl=function(a,b,c,d){if(c==3&&b.Pr&&!b.nt)for(;a&&a.Zz;a=a.wb)a.Zz=!1;if(b.Nn)b.Nn.wb=null,Bl(b,c,d);else try{b.nt?b.Ev.call(b.context):Bl(b,c,d)}catch(e){Cl.call(null,e)}il.put(b)};
Bl=function(a,b,c){b==2?a.Ev.call(a.context,c):a.Pr&&a.Pr.call(a.context,c)};zl=function(a,b){a.Zz=!0;_.dl(function(){a.Zz&&Cl.call(null,b)})};Cl=_.vi;_.ul=function(a){_.rb.call(this,a);this.tY=!1};_.bb(_.ul,_.rb);_.ul.prototype.name="cancel";var pl=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};
_.Dl=function(a){return new _.gl(a)};
var Pl=function(){this.Zw={pY:Il?"../"+Il:null,Ly:Jl,oT:Kl,Nqa:Ll,Fo:Ml,Gra:Nl};this.jg=_.rf;this.IX=this.a7;this.R7=/MSIE\s*[0-8](\D|$)/.test(window.navigator.userAgent);if(this.Zw.pY){this.jg=this.Zw.oT(this.jg,this.Zw.pY);var a=this.jg.document,b=a.createElement("script");b.setAttribute("type","text/javascript");b.text="window.doPostMsg=function(w,s,o) {window.setTimeout(function(){w.postMessage(s,o);},0);};";a.body.appendChild(b);this.IX=this.jg.doPostMsg}this.zM={};this.dN={};a=(0,_.z)(this.iH,
this);_.Hf(this.jg,"message",a);_.vf(_.If,"RPMQ",[]).push(a);this.jg!=this.jg.parent&&Ol(this,this.jg.parent,this.xI(this.jg.name),"*")};Pl.prototype.xI=function(a){return'{"h":"'+escape(a)+'"}'};var Ql=function(a){var b=null;a.indexOf('{"h":"')===0&&a.indexOf('"}')===a.length-2&&(b=unescape(a.substring(6,a.length-2)));return b},Rl=function(a){if(!/^\s*{/.test(a))return!1;a=_.Ug(a);return a!==null&&typeof a==="object"&&!!a.g};
Pl.prototype.iH=function(a){var b=String(a.data);_.Zg.debug("gapix.rpc.receive("+Ll+"): "+(!b||b.length<=512?b:b.substr(0,512)+"... ("+b.length+" bytes)"));var c=b.indexOf("!_")!==0;c||(b=b.substring(2));var d=Rl(b);if(!c&&!d){if(!d&&(c=Ql(b))){if(this.zM[c])this.zM[c]();else this.dN[c]=1;return}var e=a.origin,f=this.Zw.Ly;this.R7?_.rf.setTimeout(function(){f(b,e)},0):f(b,e)}};Pl.prototype.Nb=function(a,b){a===".."||this.dN[a]?(b(),delete this.dN[a]):this.zM[a]=b};
var Ol=function(a,b,c,d){var e=Rl(c)?"":"!_";_.Zg.debug("gapix.rpc.send("+Ll+"): "+(!c||c.length<=512?c:c.substr(0,512)+"... ("+c.length+" bytes)"));a.IX(b,e+c,d)};Pl.prototype.a7=function(a,b,c){a.postMessage(b,c)};Pl.prototype.send=function(a,b,c){(a=this.Zw.oT(this.jg,a))&&!a.closed&&Ol(this,a,b,c)};var Sl,Tl,Ul,Vl,Wl,Xl,Yl,Il,Ll,Zl,$l,am,Kl,Ml,cm,dm,im,jm,lm,Nl,nm,mm,em,fm,om,Jl,pm,qm;Sl=0;Tl=[];Ul={};Vl={};Wl=_.rf.location.href;Xl=_.Cf(Wl,"rpctoken");Yl=_.Cf(Wl,"parent")||_.sf.referrer;Il=_.Cf(Wl,"rly");Ll=Il||(_.rf!==_.rf.top||_.rf.opener)&&_.rf.name||"..";Zl=null;$l={};am=function(){};_.bm={send:am,Nb:am,xI:am};
Kl=function(a,b){var c=a;b.charAt(0)=="/"&&(b=b.substring(1),c=_.rf.top);if(b.length===0)return c;for(b=b.split("/");b.length;){a=b.shift();a.charAt(0)=="{"&&a.charAt(a.length-1)=="}"&&(a=a.substring(1,a.length-1));var d=a;if(d==="..")c=c==c.parent?c.opener:c.parent;else if(d!==".."&&c.frames[d]){var e=c;a=d;c=c.frames[d];if(!("postMessage"in c))if(c instanceof HTMLIFrameElement&&"contentWindow"in c)c=c.contentWindow!=null&&"postMessage"in c.contentWindow?c.contentWindow:null;else{d=null;e=_.sa(e.document.getElementsByTagName("iframe"));
for(var f=e.next();!f.done;f=e.next())if(f=f.value,f.getAttribute("id")==a||f.getAttribute("name")==a)d=f;if(d&&"contentWindow"in d)c=d.contentWindow!=null?d.contentWindow:null;else throw Error("N`"+c+"`"+a);}}else return null}return c};Ml=function(a){return(a=Ul[a])&&a.token};cm=function(a){if(a.f in{})return!1;var b=a.t,c=Ul[a.r];a=a.origin;return c&&(c.token===b||!c.token&&!b)&&(a===c.origin||c.origin==="*")};
dm=function(a){var b=a.id.split("/"),c=b[b.length-1],d=a.origin;return function(e){var f=e.origin;return e.f==c&&(d==f||d=="*")}};_.gm=function(a,b,c){a=em(a);Vl[a.name]={Zh:b,uv:a.uv,Ss:c||cm};fm()};_.hm=function(a){a=em(a);delete Vl[a.name]};im={};jm=function(a,b){(a=im["_"+a])&&a[1](this)&&a[0].call(this,b)};lm=function(a){var b=a.c;if(!b)return am;var c=a.r,d=a.g?"legacy__":"";return function(){var e=[].slice.call(arguments,0);e.unshift(c,d+"__cb",null,b);_.km.apply(null,e)}};
Nl=function(a){Zl=a};nm=function(a){$l[a]||($l[a]=_.rf.setTimeout(function(){$l[a]=!1;mm(a)},0))};mm=function(a){var b=Ul[a];if(b&&b.ready){var c=b.NJ;for(b.NJ=[];c.length;)_.bm.send(a,_.Vg(c.shift()),b.origin)}};em=function(a){return a.indexOf("legacy__")===0?{name:a.substring(8),uv:!0}:{name:a,uv:!1}};
fm=function(){for(var a=_.Zi("rpc/residenceSec")||60,b=(new Date).getTime()/1E3,c,d=0;c=Tl[d];++d){var e=c.rpc;if(!e||a>0&&b-c.timestamp>a)Tl.splice(d,1),--d;else{var f=e.s,h=Vl[f]||Vl["*"];if(h)if(Tl.splice(d,1),--d,e.origin=c.origin,c=lm(e),e.callback=c,h.Ss(e)){if(f!=="__cb"&&!!h.uv!=!!e.g)break;e=h.Zh.apply(e,e.a);e!==void 0&&c(e)}else _.Zg.debug("gapix.rpc.rejected("+Ll+"): "+f)}}};om=function(a,b,c){Tl.push({rpc:a,origin:b,timestamp:(new Date).getTime()/1E3});c||fm()};
Jl=function(a,b){a=_.Ug(a);om(a,b,!1)};pm=function(a){for(;a.length;)om(a.shift(),this.origin,!0);fm()};qm=function(a){var b=!1;a=a.split("|");var c=a[0];c.indexOf("/")>=0&&(b=!0);return{id:c,origin:a[1]||"*",VH:b}};
_.rm=function(a,b,c,d){var e=qm(a);d&&(_.rf.frames[e.id]=_.rf.frames[e.id]||d);a=e.id;if(!Ul.hasOwnProperty(a)){c=c||null;d=e.origin;if(a==="..")d=_.Nh(Yl),c=c||Xl;else if(!e.VH){var f=_.sf.getElementById(a);f&&(f=f.src,d=_.Nh(f),c=c||_.Cf(f,"rpctoken"))}e.origin==="*"&&d||(d=e.origin);Ul[a]={token:c,NJ:[],origin:d,aea:b,iY:function(){var h=a;Ul[h].ready=1;mm(h)}};_.bm.Nb(a,Ul[a].iY)}return Ul[a].iY};
_.km=function(a,b,c,d){a=a||"..";_.rm(a);a=a.split("|",1)[0];var e=b,f=a,h=[].slice.call(arguments,3),k=c,l=Ll,m=Xl,n=Ul[f],p=l,q=qm(f);if(n&&f!==".."){if(q.VH){if(!(m=Ul[f].aea)){m=Zl?Zl.substring(1).split("/"):[Ll];p=m.length-1;for(f=_.rf.parent;f!==_.rf.top;){var t=f.parent;if(!p--){for(var v=null,u=t.frames.length,w=0;w<u;++w)t.frames[w]==f&&(v=w);m.unshift("{"+v+"}")}f=t}m="/"+m.join("/")}p=m}else p=l="..";m=n.token}k&&q?(n=cm,q.VH&&(n=dm(q)),im["_"+ ++Sl]=[k,n],k=Sl):k=null;h={s:e,f:l,r:p,t:m,
c:k,a:h};e=em(e);h.s=e.name;h.g=e.uv;Ul[a].NJ.push(h);nm(a)};if(typeof _.rf.postMessage==="function"||typeof _.rf.postMessage==="object")_.bm=new Pl,_.gm("__cb",jm,function(){return!0}),_.gm("_processBatch",pm,function(){return!0}),_.rm("..");
var en;
en=function(){function a(k,l){k=window.getComputedStyle(k,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(k[0],10)}for(var b=0,c=[document.body];c.length>0;){var d=c.shift(),e=d.childNodes;if(typeof d.style!=="undefined"){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if(f!="visible"&&f!="inherit"&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),f.length>0&&f!="auto"))continue}for(d=0;d<e.length;d++){f=e[d];
if(typeof f.offsetTop!=="undefined"&&typeof f.offsetHeight!=="undefined"){var h=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,h)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")};
_.fn=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;if(document.compatMode==="CSS1Compat"&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(navigator.userAgent.indexOf("AppleWebKit")>=0)return en();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,
e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};
var gn=function(a,b){return _.Fk(a,b,!0)},hn=function(a){var b=function(c){return new (a().Context)(c)};b.prototype.addOnConnectHandler=function(c,d,e,f){return a().Context.prototype.addOnConnectHandler.apply(this,[c,d,e,f])};b.prototype.addOnOpenerHandler=function(c,d,e){return a().Context.prototype.addOnOpenerHandler.apply(this,[c,d,e])};b.prototype.closeSelf=function(c,d,e){return a().Context.prototype.closeSelf.apply(this,[c,d,e])};b.prototype.connectIframes=function(c,d){a().Context.prototype.connectIframes.apply(this,
[c,d])};b.prototype.getFrameName=function(){return a().Context.prototype.getFrameName.apply(this)};b.prototype.getGlobalParam=function(c){a().Context.prototype.getGlobalParam.apply(this,[c])};b.prototype.getParentIframe=function(){return a().Context.prototype.getParentIframe.apply(this)};b.prototype.getWindow=function(){return a().Context.prototype.getWindow.apply(this)};b.prototype.isDisposed=function(){return a().Context.prototype.isDisposed.apply(this)};b.prototype.open=function(c,d){return a().Context.prototype.open.apply(this,
[c,d])};b.prototype.openChild=function(c){return a().Context.prototype.openChild.apply(this,[c])};b.prototype.ready=function(c,d,e,f){a().Context.prototype.ready.apply(this,[c,d,e,f])};b.prototype.removeOnConnectHandler=function(c){a().Context.prototype.removeOnConnectHandler.apply(this,[c])};b.prototype.restyleSelf=function(c,d,e){return a().Context.prototype.restyleSelf.apply(this,[c,d,e])};b.prototype.setCloseSelfFilter=function(c){a().Context.prototype.setCloseSelfFilter.apply(this,[c])};b.prototype.setGlobalParam=
function(c,d){a().Context.prototype.setGlobalParam.apply(this,[c,d])};b.prototype.setRestyleSelfFilter=function(c){a().Context.prototype.setRestyleSelfFilter.apply(this,[c])};return b},jn=function(a){var b=function(c,d,e,f){return new (a().Iframe)(c,d,e,f)};b.prototype.applyIframesApi=function(c){a().Iframe.prototype.applyIframesApi(c)};b.prototype.close=function(c,d){return a().Iframe.prototype.close.apply(this,[c,d])};b.prototype.getContext=function(){return a().Iframe.prototype.getContext.apply(this,
[])};b.prototype.getFrameName=function(){return a().Iframe.prototype.getFrameName.apply(this,[])};b.prototype.getId=function(){return a().Iframe.prototype.getId.apply(this,[])};b.prototype.getIframeEl=function(){return a().Iframe.prototype.getIframeEl.apply(this,[])};b.prototype.getOrigin=function(){return a().Iframe.prototype.getOrigin.apply(this,[])};b.prototype.getParam=function(c){a().Iframe.prototype.getParam.apply(this,[c])};b.prototype.getSiteEl=function(){return a().Iframe.prototype.getSiteEl.apply(this,
[])};b.prototype.getWindow=function(){return a().Iframe.prototype.getWindow.apply(this,[])};b.prototype.isDisposed=function(){return a().Iframe.prototype.isDisposed.apply(this,[])};b.prototype.ping=function(c,d){return a().Iframe.prototype.ping.apply(this,[c,d])};b.prototype.register=function(c,d,e){a().Iframe.prototype.register.apply(this,[c,d,e])};b.prototype.registerWasClosed=function(c,d){a().Iframe.prototype.registerWasClosed.apply(this,[c,d])};b.prototype.registerWasRestyled=function(c,d){a().Iframe.prototype.registerWasRestyled.apply(this,
[c,d])};b.prototype.restyle=function(c,d){return a().Iframe.prototype.restyle.apply(this,[c,d])};b.prototype.send=function(c,d,e,f){return a().Iframe.prototype.send.apply(this,[c,d,e,f])};b.prototype.setParam=function(c,d){a().Iframe.prototype.setParam.apply(this,[c,d])};b.prototype.setSiteEl=function(c){a().Iframe.prototype.setSiteEl.apply(this,[c])};b.prototype.unregister=function(c,d){a().Iframe.prototype.unregister.apply(this,[c,d])};return b},kn,ln,pn,rn,wn,Fn,Gn,In,Mn,Nn,Qn,Sn,Tn,Vn,Un,Wn;
_.Jk.prototype.ss=_.nb(4,function(a){this.T.apis=a;return this});kn=function(a,b){a.T.onload=b};ln=function(a){return a.T.rpctoken};_.mn=function(a,b){a.T.queryParams=b;return a};_.nn=function(a,b){a.T.relayOpen=b;return a};_.on=function(a,b){a.T.onClose=b;return a};pn=function(a,b){a.T.controllerData=b};_.qn=function(a){a.T.waitForOnload=!0;return a};rn=function(a){return(a=a.T.timeout)?a:null};_.sn=function(a){return!!a&&typeof a==="object"&&_.uf.test(a.push)};
_.tn=function(a){for(var b=0;b<this.length;b++)if(this[b]===a)return b;return-1};_.un=function(a,b,c){if(a){_.sm(_.sn(a),"arrayForEach was called with a non array value");for(var d=0;d<a.length;d++)b.call(c,a[d],d)}};_.vn=function(a,b,c){if(a)if(_.sn(a))_.un(a,b,c);else{_.sm(typeof a==="object","objectForEach was called with a non object value");c=c||a;for(var d in a)_.xf(a,d)&&a[d]!==void 0&&b.call(c,a[d],d)}};wn=function(a){this.T=a||{}};wn.prototype.value=function(){return this.T};
wn.prototype.getIframe=function(){return this.T.iframe};var xn=function(a,b){a.T.role=b;return a},yn=function(a,b){a.T.data=b;return a};wn.prototype.rl=function(a){this.T.setRpcReady=a;return this};var zn=function(a){return a.T.setRpcReady};wn.prototype.wn=function(a){this.T.rpctoken=a;return this};var An=function(a){a.T.selfConnect=!0;return a},Bn=function(a){this.T=a||{}};Bn.prototype.value=function(){return this.T};var Dn=function(a){var b=new Cn;b.T.role=a;return b};Bn.prototype.cT=function(){return this.T.role};
Bn.prototype.Lc=function(a){this.T.handler=a;return this};Bn.prototype.Bb=function(){return this.T.handler};var En=function(a,b){a.T.filter=b;return a};Bn.prototype.ss=function(a){this.T.apis=a;return this};In=/^[\w\.\-]*$/;_.Jn=function(a){return a.getOrigin()===a.getContext().getOrigin()};_.Kn=function(){return!0};_.Ln=function(a){for(var b=_.wf(),c=0;c<a.length;c++)b[a[c]]=!0;return function(d){return!!b[d.Jd]}};
Mn=function(a,b,c){a=Fn[a];if(!a)return[];for(var d=[],e=0;e<a.length;e++)d.push(_.kl(a[e].call(c,b,c)));return d};Nn=function(a,b,c){return function(d){if(!b.isDisposed()){var e=this.origin,f=b.getOrigin();_.sm(e===f,"Wrong origin "+e+" != "+f);e=this.callback;d=Mn(a,d,b);!c&&d.length>0&&_.ol(d).then(e)}}};_.On=function(a,b,c){_.sm(a!="_default","Cannot update default api");Gn[a]={map:b,filter:c}};
_.Pn=function(a,b,c){_.sm(a!="_default","Cannot update default api");_.vf(Gn,a,{map:{},filter:_.Jn}).map[b]=c};Qn=function(a,b){_.vf(Gn,"_default",{map:{},filter:_.Kn}).map[a]=b;_.vn(_.Hn.vg,function(c){c.register(a,b,_.Kn)})};_.Rn=function(){return _.Hn};Sn=/^https?:\/\/[^\/%\\?#\s]+$/i;Tn={longdesc:!0,name:!0,src:!0,frameborder:!0,marginwidth:!0,marginheight:!0,scrolling:!0,align:!0,height:!0,width:!0,id:!0,"class":!0,title:!0,tabindex:!0,hspace:!0,vspace:!0,allowtransparency:!0};
Vn=function(a){this.resolve=this.reject=null;this.promise=_.Dl((0,_.z)(function(b,c){this.resolve=b;this.reject=c},this));a&&(this.promise=Un(this.promise,a))};Un=function(a,b){return a.then(function(c){try{b(c)}catch(d){}return c})};Wn=function(a){this.yg=a;this.Context=hn(a);this.Iframe=jn(a)};_.g=Wn.prototype;_.g.CROSS_ORIGIN_IFRAMES_FILTER=function(a){return this.yg().CROSS_ORIGIN_IFRAMES_FILTER(a)};_.g.SAME_ORIGIN_IFRAMES_FILTER=function(a){return this.yg().SAME_ORIGIN_IFRAMES_FILTER(a)};
_.g.create=function(a,b,c){return this.yg().create(a,b,c)};_.g.getBeforeOpenStyle=function(a){return this.yg().getBeforeOpenStyle(a)};_.g.getContext=function(){return this.yg().getContext()};_.g.getStyle=function(a){return this.yg().getStyle(a)};_.g.makeWhiteListIframesFilter=function(a){return this.yg().makeWhiteListIframesFilter(a)};_.g.registerBeforeOpenStyle=function(a,b){return this.yg().registerBeforeOpenStyle(a,b)};
_.g.registerIframesApi=function(a,b,c){return this.yg().registerIframesApi(a,b,c)};_.g.registerIframesApiHandler=function(a,b,c){return this.yg().registerIframesApiHandler(a,b,c)};_.g.registerStyle=function(a,b){return this.yg().registerStyle(a,b)};var Xn=function(){this.Gi=[]};Xn.prototype.yg=function(a){return this.Gi.length?Yn(this.Gi[0],a):void 0};var Yn=function(a,b){b=b===void 0?function(c){return new c}:b;return a.Ke?b(a.Ke):a.instance},Zn=function(){Xn.apply(this,arguments)};_.A(Zn,Xn);var ao=function(a){var b=$n.rQ,c=a.priority,d=~gn(b.Gi,function(e){return e.priority<c?-1:1});b.Gi.splice(d,0,a)};var $n=new function(){var a=this;this.rQ=new Zn;this.instance=new Wn(function(){return a.rQ.yg()()})};ao({instance:function(){return window.gapi.iframes},priority:1});_.bo=$n.instance;var co,eo;co={height:!0,width:!0};eo=/^(?!-*(?:expression|(?:moz-)?binding))(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|-?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:[a-z]{1,2}|%)?|!important|)$/i;_.fo=function(a){typeof a==="number"&&(a=String(a)+"px");return a};var go=function(){wn.apply(this,arguments)};_.A(go,wn);var Cn=function(){Bn.apply(this,arguments)};_.A(Cn,Bn);var ho=function(){_.Jk.apply(this,arguments)};_.A(ho,_.Jk);var io=function(a){ho.call(this,a)};_.A(io,ho);var jo=function(a,b){a.T.frameName=b;return a};io.prototype.getFrameName=function(){return this.T.frameName};var ko=function(a,b){a.T.rpcAddr=b;return a};io.prototype.Fg=function(){return this.T.rpcAddr};var lo=function(a,b){a.T.retAddr=b;return a};_.g=io.prototype;_.g.li=function(){return this.T.retAddr};_.g.Wj=function(a){this.T.origin=a;return this};_.g.getOrigin=function(){return this.T.origin};_.g.rl=function(a){this.T.setRpcReady=a;return this};
_.g.Hp=function(a){this.T.context=a};var mo=function(a,b){a.T._rpcReadyFn=b};io.prototype.getIframeEl=function(){return this.T.iframeEl};var no=function(a,b,c){var d=a.Fg(),e=b.li();lo(ko(c,a.li()+"/"+b.Fg()),e+"/"+d);jo(c,b.getFrameName()).Wj(b.getOrigin())};var po=function(a,b,c){a.setTimeout(function(){b.closed||c==5?oo(b):(b.close(),c++,po(a,b,c))},1E3)},oo=function(a){a.closed||a.document&&a.document.body&&_.lf(a.document.body,"Please close this window.")};_.qo=function(a,b,c,d){this.eh=!1;this.tb=a;this.dK=b;this.Fq=c;this.Na=d;this.FY=this.Na.li();this.Jd=this.Na.getOrigin();this.naa=this.Na.getIframeEl();this.u_=this.Na.T.where;this.Gi=[];this.applyIframesApi("_default");a=this.Na.T.apis||[];for(b=0;b<a.length;b++)this.applyIframesApi(a[b]);this.tb.vg[c]=this};_.g=_.qo.prototype;_.g.isDisposed=function(){return this.eh};
_.g.dispose=function(){if(!this.isDisposed()){for(var a=0;a<this.Gi.length;a++)this.unregister(this.Gi[a]);delete _.Hn.vg[this.getFrameName()];this.eh=!0}};_.g.getContext=function(){return this.tb};_.g.getOptions=function(){return this.Na};_.g.Fg=function(){return this.dK};_.g.li=function(){return this.FY};_.g.getFrameName=function(){return this.Fq};_.g.getIframeEl=function(){return this.naa};_.g.getSiteEl=function(){return this.u_};_.g.setSiteEl=function(a){this.u_=a};_.g.rl=function(){(0,this.Na.T._rpcReadyFn)()};
_.g.setParam=function(a,b){this.Na.value()[a]=b};_.g.getParam=function(a){return this.Na.value()[a]};_.g.Ac=function(){return this.Na.value()};_.g.getId=function(){return this.Na.getId()};_.g.getOrigin=function(){return this.Jd};var ro=function(a,b){var c=a.Fq;a=a.tb.getFrameName();return c+":"+a+":"+b};_.g=_.qo.prototype;
_.g.register=function(a,b,c){_.sm(!this.isDisposed(),"Cannot register handler on disposed iframe "+a);_.sm((c||_.Jn)(this),"Rejecting untrusted message "+a);c=ro(this,a);_.vf(Fn,c,[]).push(b)==1&&(this.Gi.push(a),_.gm(c,Nn(c,this,a==="_g_wasClosed")))};_.g.unregister=function(a,b){var c=ro(this,a),d=Fn[c];d&&(b?(b=_.tn.call(d,b),b>=0&&d.splice(b,1)):d.splice(0,d.length),d.length==0&&(b=_.tn.call(this.Gi,a),b>=0&&this.Gi.splice(b,1),_.hm(c)))};_.g.k9=function(){return this.Gi};
_.g.applyIframesApi=function(a){this.pE=this.pE||[];if(!(_.tn.call(this.pE,a)>=0)){this.pE.push(a);a=Gn[a]||{map:{}};for(var b in a.map)_.xf(a.map,b)&&this.register(b,a.map[b],a.filter)}};_.g.getWindow=function(){if(!_.Jn(this))return null;var a=this.Na.T._popupWindow;if(a)return a;var b=this.dK.split("/");a=this.getContext().getWindow();for(var c=0;c<b.length&&a;c++){var d=b[c];a=".."===d?a==a.parent?a.opener:a.parent:a.frames[d]}return a};
var so=function(a){var b={};if(a)for(var c in a)_.xf(a,c)&&_.xf(co,c)&&eo.test(a[c])&&(b[c]=a[c]);return b};_.g=_.qo.prototype;_.g.close=function(a,b){return to(this,"_g_close",a,b)};_.g.restyle=function(a,b){return to(this,"_g_restyle",a,b)};_.g.cs=function(a,b){return to(this,"_g_restyleDone",a,b)};_.g.J6=function(a){return this.getContext().closeSelf(a,void 0,this)};_.g.Wda=function(a){if(a&&typeof a==="object")return this.getContext().restyleSelf(a,void 0,this)};
_.g.Xda=function(a){var b=this.Na.T.onRestyle;b&&b.call(this,a,this);a=a&&typeof a==="object"?so(a):{};(b=this.getIframeEl())&&a&&typeof a==="object"&&(_.xf(a,"height")&&(a.height=_.fo(a.height)),_.xf(a,"width")&&(a.width=_.fo(a.width)),_.Af(a,b.style))};
_.g.K6=function(a){var b=this.Na.T.onClose;b&&b.call(this,a,this);if(b=this.getOptions().T._popupWindow){var c=this.getContext().getWindow().document.getElementById(this.getId());c&&c.parentNode&&c.parentNode.removeChild(c);c=this.getContext().getWindow();_.me&&_.Ci&&c?(c.focus(),po(c,b,0)):(b.close(),oo(b))}b||(b=this.getIframeEl())&&b.parentNode&&b.parentNode.removeChild(b);if(b=this.Na.mj())c={},c.frameName=this.getFrameName(),to(b,"_g_disposeControl",c);b=ro(this,"_g_wasClosed");Mn(b,a,this)};
_.g.registerWasRestyled=function(a,b){this.register("_g_wasRestyled",a,b)};_.g.registerWasClosed=function(a,b){this.register("_g_wasClosed",a,b)};_.g.Iga=function(){delete this.getContext().vg[this.getFrameName()];this.getContext().getWindow().setTimeout((0,_.z)(function(){this.dispose()},this),0)};
_.g.send=function(a,b,c,d){_.sm(!this.isDisposed(),"Cannot send message to disposed iframe - "+a);_.sm((d||_.Jn)(this),"Wrong target for message "+a);c=new Vn(c);a=this.tb.getFrameName()+":"+this.Fq+":"+a;_.km(this.dK,a,c.resolve,b);return c.promise};var to=function(a,b,c,d){return a.send(b,c,d,_.Kn)};_.g=_.qo.prototype;_.g.Wca=function(a){return a};_.g.ping=function(a,b){return to(this,"_g_ping",b,a)};
_.g.Q6=function(a){a=a&&typeof a==="object"?a:{};for(var b=a.rpcAddr,c=(this.Fg()+"/"+b).split("/"),d=this.getContext().getWindow(),e;(e=c.shift())&&d;)d=e==".."?d.parent:d.frames[e];_.sm(!!d,"Bad rpc address "+b);a._window=d;a._parentRpcAddr=this.Fg();a._parentRetAddr=this.li();this.getContext();b=new _.uo(a);this.fca&&this.fca(b,a.controllerData);this.ZE=this.ZE||[];this.ZE.push(b,a.controllerData)};
_.g.f7=function(a){a=(a||{}).frameName;for(var b=this.ZE||[],c=0;c<b.length;c++)if(b[c].getFrameName()===a){a=b.splice(c,1)[0];a.dispose();this.jca&&this.jca(a);return}_.sm(!1,"Unknown contolled iframe to dispose - "+a)};
_.g.N6=function(a){var b=new io(a);a=new go(b.value());if(a.T.selfConnect)var c=this;else(_.sm(Sn.test(b.getOrigin()),"Illegal origin for connected iframe - "+b.getOrigin()),c=this.getContext().vg[b.getFrameName()],c)?zn(b)&&(c.rl(),to(c,"_g_rpcReady")):(b=jo(lo(ko(new io,b.Fg()),b.li()).Wj(b.getOrigin()),b.getFrameName()).rl(zn(b)).wn(ln(b)),c=this.getContext().attach(b.value()));b=this.getContext();var d=a.T.role;a=a.T.data;vo(b);d=d||"";_.vf(b.XE,d,[]).push({Tf:c,data:a});zo(c,a,b.ZI[d])};
_.g.EL=function(a,b){(new io(b)).T._relayedDepth||(b={},An(xn(new go(b),"_opener")),to(a,"_g_connect",b))};
_.g.cX=function(a){var b=this,c=a.T.messageHandlers,d=a.T.messageHandlersFilter,e=a.T.onClose;_.on(_.Lk(_.Kk(a,null),null),null);return to(this,"_g_open",a.value()).then(function(f){var h=new io(f[0]),k=h.getFrameName();f=new io;var l=b.li(),m=h.li();lo(ko(f,b.Fg()+"/"+h.Fg()),m+"/"+l);jo(f,k);f.Wj(h.getOrigin());f.ss(h.T.apis);f.wn(ln(a));_.Kk(f,c);_.Lk(f,d);_.on(f,e);(h=b.getContext().vg[k])||(h=b.getContext().attach(f.value()));return h})};
_.g.gK=function(a){var b=a.getUrl();_.sm(!b||_.Hm.test(b),"Illegal url for new iframe - "+b);var c=a.wo().value();b={};for(var d in c)_.xf(c,d)&&_.xf(Tn,d)&&(b[d]=c[d]);_.xf(c,"style")&&(d=c.style,typeof d==="object"&&(b.style=so(d)));a.value().attributes=b};
_.g.Fca=function(a){a=new io(a);this.gK(a);var b=a.T._relayedDepth||0;a.T._relayedDepth=b+1;a.T.openerIframe=this;var c=ln(a);a.wn(null);var d=this;return this.getContext().open(a.value()).then(function(e){var f=(new io(e.Ac())).T.apis,h=new io;no(e,d,h);b==0&&xn(new go(h.value()),"_opener");h.rl(!0);h.wn(c);to(e,"_g_connect",h.value());h=new io;jo(lo(ko(h,e.Fg()),e.FY),e.getFrameName()).Wj(e.getOrigin()).ss(f);return h.value()})};
_.g.Vda=function(a){this.getContext().addOnOpenerHandler(function(b){b.send("_g_wasRestyled",a,void 0,_.Kn)},null,_.Kn)};var Eo;_.Ao=_.wf();_.Bo=_.wf();_.Co=function(a,b){_.Ao[a]=b};_.Do=function(a){return _.Ao[a]};Eo=function(a,b){_.Bf.load("gapi.iframes.style."+a,b)};_.Fo=function(a,b){_.Bo[a]=b};_.Go=function(a){return _.Bo[a]};_.uo=function(a){a=a||{};this.eh=!1;this.Kj=_.wf();this.vg=_.wf();this.jg=a._window||_.rf;this.Qd=this.jg.location.href;this.tX=(this.uJ=Ho(this.Qd,"parent"))?Ho(this.Qd,"pfname"):"";this.Ha=this.uJ?Ho(this.Qd,"_gfid")||Ho(this.Qd,"id"):"";this.Fq=_.Um(this.Ha,this.tX);this.Jd=_.Nh(this.Qd);if(this.Ha){var b=new io;ko(b,a._parentRpcAddr||"..");lo(b,a._parentRetAddr||this.Ha);b.Wj(_.Nh(this.uJ||this.Qd));jo(b,this.tX);this.wb=this.attach(b.value())}else this.wb=null};_.g=_.uo.prototype;
_.g.isDisposed=function(){return this.eh};_.g.dispose=function(){if(!this.isDisposed()){for(var a=_.sa(Object.values(this.vg)),b=a.next();!b.done;b=a.next())b.value.dispose();this.eh=!0}};_.g.getFrameName=function(){return this.Fq};_.g.getOrigin=function(){return this.Jd};_.g.getWindow=function(){return this.jg};_.g.Ab=function(){return this.jg.document};_.g.setGlobalParam=function(a,b){this.Kj[a]=b};_.g.getGlobalParam=function(a){return this.Kj[a]};
_.g.attach=function(a){_.sm(!this.isDisposed(),"Cannot attach iframe in disposed context");a=new io(a);a.Fg()||ko(a,a.getId());a.li()||lo(a,"..");a.getOrigin()||a.Wj(_.Nh(a.getUrl()));a.getFrameName()||jo(a,_.Um(a.getId(),this.Fq));var b=a.getFrameName();if(this.vg[b])return this.vg[b];var c=a.Fg(),d=c;a.getOrigin()&&(d=c+"|"+a.getOrigin());var e=a.li(),f=ln(a);f||(f=(f=a.getIframeEl())&&(f.getAttribute("data-postorigin")||f.src)||a.getUrl(),f=_.Cf(f,"rpctoken"));mo(a,_.rm(d,e,f,a.T._popupWindow));
d=((window.gadgets||{}).rpc||{}).setAuthToken;f&&d&&d(c,f);var h=new _.qo(this,c,b,a),k=a.T.messageHandlersFilter;_.vn(a.T.messageHandlers,function(l,m){h.register(m,l,k)});zn(a)&&h.rl();to(h,"_g_rpcReady");return h};_.g.gK=function(a){jo(a,null);var b=a.getId();!b||In.test(b)&&!this.getWindow().document.getElementById(b)||(_.Zg.log("Ignoring requested iframe ID - "+b),a.Ve(null))};var Ho=function(a,b){var c=_.Cf(a,b);c||(c=_.Ug(_.Cf(a,"jcp",""))[b]);return c||""};
_.uo.prototype.openChild=function(a){_.sm(!this.isDisposed(),"Cannot open iframe in disposed context");var b=new io(a);Io(this,b);var c=b.getFrameName();if(c&&this.vg[c])return this.vg[c];this.gK(b);c=b.getUrl();_.sm(c,"No url for new iframe");var d=b.T.queryParams||{};d.usegapi="1";_.mn(b,d);d=this.ST&&this.ST(c,b);d||(d=b.T.where,_.sm(!!d,"No location for new iframe"),c=_.dn(c,d,a),b.T.iframeEl=c,d=c.getAttribute("id"));ko(b,d).Ve(d);b.Wj(_.Nh(b.T.eurl||""));this.ZV&&this.ZV(b,b.getIframeEl());
c=this.attach(a);c.EL&&c.EL(c,a);(a=b.T.onCreate)&&a(c);b.T.disableRelayOpen||c.applyIframesApi("_open");return c};
var Jo=function(a,b,c){var d=b.T.canvasUrl;if(!d)return c;_.sm(!b.T.allowPost&&!b.T.forcePost,"Post is not supported when using canvas url");var e=b.getUrl();_.sm(e&&_.Nh(e)===a.Jd&&_.Nh(d)===a.Jd,"Wrong origin for canvas or hidden url "+d);b.setUrl(d);_.qn(b);b.T.canvasUrl=null;return function(f){var h=f.getWindow(),k=h.location.hash;k=_.cn(e)+(/#/.test(e)?k.replace(/^#/,"&"):k);h.location.replace(k);c&&c(f)}},Ko=function(a,b,c){var d=b.T.relayOpen;if(d){var e=a.getParentIframe();d instanceof _.qo?
(e=d,_.nn(b,0)):Number(d)>0&&_.nn(b,Number(d)-1);if(e){_.sm(!!e.cX,"Relaying iframe open is disabled");if(d=b.getStyle())if(d=_.Bo[d])b.Hp(a),d(b.value()),b.Hp(null);b.T.openerIframe=null;c.resolve(e.cX(b));return!0}}return!1},Lo=function(a,b,c){var d=b.getStyle();if(d)if(_.sm(!!_.Do,"Defer style is disabled, when requesting style "+d),_.Ao[d])Io(a,b);else return Eo(d,function(){_.sm(!!_.Ao[d],"Fail to load style - "+d);c.resolve(a.open(b.value()))}),!0;return!1};
_.uo.prototype.open=function(a,b){_.sm(!this.isDisposed(),"Cannot open iframe in disposed context");var c=new io(a);b=Jo(this,c,b);var d=new Vn(b);(b=c.getUrl())&&c.setUrl(_.cn(b));if(Ko(this,c,d)||Lo(this,c,d)||Ko(this,c,d))return d.promise;if(rn(c)!=null){var e=setTimeout(function(){h.getIframeEl().src="about:blank";d.reject({timeout:"Exceeded time limit of :"+rn(c)+"milliseconds"})},rn(c)),f=d.resolve;d.resolve=function(k){clearTimeout(e);f(k)}}c.T.waitForOnload&&kn(c.wo(),function(){d.resolve(h)});
var h=this.openChild(a);c.T.waitForOnload||d.resolve(h);return d.promise};_.uo.prototype.getParentIframe=function(){return this.wb};var Mo=function(a,b){var c=a.getParentIframe(),d=!0;b.filter&&(d=b.filter.call(b.Tf,b.params));return _.kl(d).then(function(e){return e&&c?(b.rX&&b.rX.call(a,b.params),e=b.sender?b.sender(b.params):to(c,b.message,b.params),b.Gga?e.then(function(){return!0}):!0):!1})};_.g=_.uo.prototype;
_.g.closeSelf=function(a,b,c){a=Mo(this,{sender:function(d){var e=_.Hn.getParentIframe();_.vn(_.Hn.vg,function(f){f!==e&&to(f,"_g_wasClosed",d)});return to(e,"_g_closeMe",d)},message:"_g_closeMe",params:a,Tf:c,filter:this.getGlobalParam("onCloseSelfFilter")});b=new Vn(b);b.resolve(a);return b.promise};_.g.restyleSelf=function(a,b,c){a=a||{};b=new Vn(b);b.resolve(Mo(this,{message:"_g_restyleMe",params:a,Tf:c,filter:this.getGlobalParam("onRestyleSelfFilter"),Gga:!0,rX:this.w0}));return b.promise};
_.g.w0=function(a){a.height==="auto"&&(a.height=_.fn())};_.g.setCloseSelfFilter=function(a){this.setGlobalParam("onCloseSelfFilter",a)};_.g.setRestyleSelfFilter=function(a){this.setGlobalParam("onRestyleSelfFilter",a)};var Io=function(a,b){var c=b.getStyle();if(c){b.Ah(null);var d=_.Ao[c];_.sm(d,"No such style: "+c);b.Hp(a);d(b.value());b.Hp(null)}};
_.uo.prototype.ready=function(a,b,c,d){var e=b||{},f=this.getParentIframe();this.addOnOpenerHandler(function(k){_.vn(e,function(l,m){k.register(m,l,d)},this);k!==f&&k.send("_ready",h,void 0,d)},void 0,d);var h=a||{};h.height=h.height||"auto";this.w0(h);f&&f.send("_ready",h,c,_.Kn)};
_.uo.prototype.connectIframes=function(a,b){a=new go(a);var c=new go(b),d=zn(a);b=a.getIframe();var e=c.getIframe();if(e){var f=ln(a),h=new io;no(b,e,h);yn(xn((new go(h.value())).wn(f),a.T.role),a.T.data).rl(d);var k=new io;no(e,b,k);yn(xn((new go(k.value())).wn(f),c.T.role),c.T.data).rl(!0);to(b,"_g_connect",h.value(),function(){d||to(e,"_g_connect",k.value())});d&&to(e,"_g_connect",k.value())}else c={},yn(xn(An(new go(c)),a.T.role),a.T.data),to(b,"_g_connect",c)};
var vo=function(a){a.XE||(a.XE=_.wf(),a.ZI=_.wf())};_.uo.prototype.addOnConnectHandler=function(a,b,c,d){vo(this);typeof a==="object"?(b=new Cn(a),c=b.cT()||""):(b=En(Dn(a).Lc(b).ss(c),d),c=a);d=this.XE[c]||[];a=!1;for(var e=0;e<d.length&&!a;e++)zo(this.vg[d[e].Tf.getFrameName()],d[e].data,[b]),a=b.T.runOnce;c=_.vf(this.ZI,c,[]);a||b.T.dontWait||c.push(b)};
_.uo.prototype.removeOnConnectHandler=function(a,b){a=_.vf(this.ZI,a,[]);if(b)for(var c=!1,d=0;!c&&d<a.length;d++)a[d].Bb()===b&&(c=!0,a.splice(d,1));else a.splice(0,a.length)};var zo=function(a,b,c){c=c||[];for(var d=0;d<c.length;d++){var e=c[d];if(e&&a){var f=e.T.filter||_.Jn;if(a&&f(a)){f=e.T.apis||[];for(var h=0;h<f.length;h++)a.applyIframesApi(f[h]);e.Bb()&&e.Bb()(a,b);e.T.runOnce&&(c.splice(d,1),--d)}}}};
_.uo.prototype.addOnOpenerHandler=function(a,b,c){var d=this.addOnConnectHandler;a=En(Dn("_opener").Lc(a).ss(b),c);a.T.runOnce=!0;d.call(this,a.value())};_.uo.prototype.ZV=function(a,b){var c=a.mj();if(c){_.sm(c.Jd===a.getOrigin(),"Wrong controller origin "+this.Jd+" !== "+a.getOrigin());var d=a.Fg();ko(a,c.Fg());lo(a,c.li());var e=new io;pn(ko(e,d),a.T.controllerData);_.Hf(b,"load",function(){c.send("_g_control",e.value())})}};
var No=function(a,b,c){a=a.getWindow();var d=a.document,e=c.T.reuseWindow;if(e){var f=c.getId();if(!f)throw Error("O");}else f=_.Tm(d,c);var h=f,k=c.T.rpcRelayUrl;if(k){k=_.bn(k);h=c.T.fragmentParams||{};h.rly=f;c.T.fragmentParams=h;h=c.T.where||d.body;_.sm(!!h,"Cannot open window in a page with no body");var l={};l.src=k;l.style="display:none;";l.id=f;l.name=f;_.Xm(d,h,l,f);h=f+"_relay"}b=_.cn(b);var m=_.Vm(d,b,f,c.value());c.T.eurl=m;b=c.T.openAsWindow;typeof b!=="string"&&(b=void 0);c=window.navigator.userAgent||
"";/Trident|MSIE/i.test(c)&&/#/.test(c)&&(m="javascript:window.location.replace("+_.rf.JSON.stringify(m).replace(/#/g,"\\x23")+")");if(e){var n=e;setTimeout(function(){n.location.replace(m)})}else n=_.Hk(a,m,h,b);return{id:f,U0:n}};_.uo.prototype.ST=function(a,b){if(b.T.openAsWindow){a=No(this,a,b);var c=a.id;_.sm(!!a.U0,"Open popup window failed");b.T._popupWindow=a.U0}return c};Fn=_.wf();Gn=_.wf();_.Hn=new _.uo;Qn("_g_rpcReady",_.qo.prototype.rl);Qn("_g_discover",_.qo.prototype.k9);Qn("_g_ping",_.qo.prototype.Wca);Qn("_g_close",_.qo.prototype.J6);Qn("_g_closeMe",_.qo.prototype.K6);Qn("_g_restyle",_.qo.prototype.Wda);Qn("_g_restyleMe",_.qo.prototype.Xda);Qn("_g_wasClosed",_.qo.prototype.Iga);_.Pn("control","_g_control",_.qo.prototype.Q6);_.Pn("control","_g_disposeControl",_.qo.prototype.f7);var Oo=_.Hn.getParentIframe();
Oo&&Oo.register("_g_restyleDone",_.qo.prototype.Vda,_.Kn);Qn("_g_connect",_.qo.prototype.N6);var Po={};Po._g_open=_.qo.prototype.Fca;_.On("_open",Po,_.Kn);var Qo={Context:_.uo,Iframe:_.qo,SAME_ORIGIN_IFRAMES_FILTER:_.Jn,CROSS_ORIGIN_IFRAMES_FILTER:_.Kn,makeWhiteListIframesFilter:_.Ln,getContext:_.Rn,registerIframesApi:_.On,registerIframesApiHandler:_.Pn,registerStyle:_.Co,registerBeforeOpenStyle:_.Fo,getStyle:_.Do,getBeforeOpenStyle:_.Go,create:_.dn};ao({instance:function(){return Qo},priority:2});_.Pn("gapi.load","_g_gapi.load",function(a){return new _.gl(function(b){_.Bf.load(a&&typeof a==="object"&&a.features||"",b)})});
_.Ro=function(a){this.T=a};_.g=_.Ro.prototype;_.g.uK=function(a){this.T.anchor=a;return this};_.g.jj=function(){return this.T.anchor};_.g.vK=function(a){this.T.anchorPosition=a};_.g.ee=function(a){this.T.height=a;return this};_.g.Qc=function(){return this.T.height};_.g.We=function(a){this.T.width=a;return this};_.g.Xb=function(){return this.T.width};_.g.setZIndex=function(a){this.T.zIndex=a;return this};_.g.getZIndex=function(){return this.T.zIndex};
_.So=function(a){a.T.connectWithQueryParams=!0;return a};
_.r("gapi.iframes.create",_.dn);
_.r("gapi.iframes.registerStyle",_.Co);_.r("gapi.iframes.registerBeforeOpenStyle",_.Fo);_.r("gapi.iframes.getStyle",_.Do);_.r("gapi.iframes.getBeforeOpenStyle",_.Go);_.r("gapi.iframes.registerIframesApi",_.On);_.r("gapi.iframes.registerIframesApiHandler",_.Pn);_.r("gapi.iframes.getContext",_.Rn);_.r("gapi.iframes.SAME_ORIGIN_IFRAMES_FILTER",_.Jn);_.r("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Kn);_.r("gapi.iframes.makeWhiteListIframesFilter",_.Ln);_.r("gapi.iframes.Context",_.uo);
_.r("gapi.iframes.Context.prototype.isDisposed",_.uo.prototype.isDisposed);_.r("gapi.iframes.Context.prototype.getWindow",_.uo.prototype.getWindow);_.r("gapi.iframes.Context.prototype.getFrameName",_.uo.prototype.getFrameName);_.r("gapi.iframes.Context.prototype.getGlobalParam",_.uo.prototype.getGlobalParam);_.r("gapi.iframes.Context.prototype.setGlobalParam",_.uo.prototype.setGlobalParam);_.r("gapi.iframes.Context.prototype.open",_.uo.prototype.open);
_.r("gapi.iframes.Context.prototype.openChild",_.uo.prototype.openChild);_.r("gapi.iframes.Context.prototype.getParentIframe",_.uo.prototype.getParentIframe);_.r("gapi.iframes.Context.prototype.closeSelf",_.uo.prototype.closeSelf);_.r("gapi.iframes.Context.prototype.restyleSelf",_.uo.prototype.restyleSelf);_.r("gapi.iframes.Context.prototype.setCloseSelfFilter",_.uo.prototype.setCloseSelfFilter);_.r("gapi.iframes.Context.prototype.setRestyleSelfFilter",_.uo.prototype.setRestyleSelfFilter);
_.r("gapi.iframes.Context.prototype.addOnConnectHandler",_.uo.prototype.addOnConnectHandler);_.r("gapi.iframes.Context.prototype.removeOnConnectHandler",_.uo.prototype.removeOnConnectHandler);_.r("gapi.iframes.Context.prototype.addOnOpenerHandler",_.uo.prototype.addOnOpenerHandler);_.r("gapi.iframes.Context.prototype.connectIframes",_.uo.prototype.connectIframes);_.r("gapi.iframes.Iframe",_.qo);_.r("gapi.iframes.Iframe.prototype.isDisposed",_.qo.prototype.isDisposed);
_.r("gapi.iframes.Iframe.prototype.getContext",_.qo.prototype.getContext);_.r("gapi.iframes.Iframe.prototype.getFrameName",_.qo.prototype.getFrameName);_.r("gapi.iframes.Iframe.prototype.getId",_.qo.prototype.getId);_.r("gapi.iframes.Iframe.prototype.register",_.qo.prototype.register);_.r("gapi.iframes.Iframe.prototype.unregister",_.qo.prototype.unregister);_.r("gapi.iframes.Iframe.prototype.send",_.qo.prototype.send);_.r("gapi.iframes.Iframe.prototype.applyIframesApi",_.qo.prototype.applyIframesApi);
_.r("gapi.iframes.Iframe.prototype.getIframeEl",_.qo.prototype.getIframeEl);_.r("gapi.iframes.Iframe.prototype.getSiteEl",_.qo.prototype.getSiteEl);_.r("gapi.iframes.Iframe.prototype.setSiteEl",_.qo.prototype.setSiteEl);_.r("gapi.iframes.Iframe.prototype.getWindow",_.qo.prototype.getWindow);_.r("gapi.iframes.Iframe.prototype.getOrigin",_.qo.prototype.getOrigin);_.r("gapi.iframes.Iframe.prototype.close",_.qo.prototype.close);_.r("gapi.iframes.Iframe.prototype.restyle",_.qo.prototype.restyle);
_.r("gapi.iframes.Iframe.prototype.restyleDone",_.qo.prototype.cs);_.r("gapi.iframes.Iframe.prototype.registerWasRestyled",_.qo.prototype.registerWasRestyled);_.r("gapi.iframes.Iframe.prototype.registerWasClosed",_.qo.prototype.registerWasClosed);_.r("gapi.iframes.Iframe.prototype.getParam",_.qo.prototype.getParam);_.r("gapi.iframes.Iframe.prototype.setParam",_.qo.prototype.setParam);_.r("gapi.iframes.Iframe.prototype.ping",_.qo.prototype.ping);_.r("gapi.iframes.Iframe.prototype.getOpenParams",_.qo.prototype.Ac);
_.jg=_.jg||{};
_.jg=_.jg||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.jg.kh=a;a()})();_.r("gadgets.util.getUrlParameters",_.jg.kh);
_.Oh=window.googleapis&&window.googleapis.server||{};
_.ng=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Tf(a());return{register:function(b,c,d){d&&d(_.Sf())},get:function(b){return _.Sf(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Tf(b)},zd:function(){}}}();_.r("gadgets.config.register",_.ng.register);_.r("gadgets.config.get",_.ng.get);_.r("gadgets.config.init",_.ng.zd);_.r("gadgets.config.update",_.ng.update);
_.r("gadgets.json.stringify",_.Vg);_.r("gadgets.json.parse",_.Ug);
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.kg=function(e){a(2,e)};_.lg=function(e){a(3,e)};_.mg=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.jg=_.jg||{};(function(){var a=[];_.jg.hra=function(b){a.push(b)};_.jg.ura=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
_.$g=function(){var a=_.sf.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.ah=function(a){if(_.$g())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.rf.addEventListener?(_.rf.addEventListener("load",c,!1),_.rf.addEventListener("DOMContentLoaded",c,!1)):_.rf.attachEvent&&(_.rf.attachEvent("onreadystatechange",function(){_.$g()&&c.apply(this,arguments)}),_.rf.attachEvent("onload",c))}};
_.bh=function(a,b){var c=_.vf(_.If,"watt",_.wf());_.vf(c,a,b)};_.Cf(_.rf.location.href,"rpctoken")&&_.Hf(_.sf,"unload",function(){});var ch=ch||{};ch.IY=null;ch.yW=null;ch.nA=null;ch.frameElement=null;ch=ch||{};
ch.tN||(ch.tN=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Ug(f.data);if(h&&h.f){_.mg();var k=_.dh.Do(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.lg("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{AS:function(){return"wpm"},Zaa:function(){return!0},zd:function(f,h){_.ng.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Nb:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.dh.Do(f),m=_.dh.mO(f);l?window.setTimeout(function(){var n=_.Vg(k);_.mg();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.lg("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.dh!="undefined"&&_.dh||(_.dh=window.gadgets.rpc,_.dh.config=_.dh.config,_.dh.register=_.dh.register,_.dh.unregister=_.dh.unregister,_.dh.nY=_.dh.registerDefault,_.dh.v0=_.dh.unregisterDefault,_.dh.hS=_.dh.forceParentVerifiable,_.dh.call=_.dh.call,_.dh.Hu=_.dh.getRelayUrl,_.dh.Yj=_.dh.setRelayUrl,_.dh.zC=_.dh.setAuthToken,_.dh.xw=_.dh.setupReceiver,_.dh.ko=_.dh.getAuthToken,_.dh.VJ=_.dh.removeReceiver,_.dh.ZS=_.dh.getRelayChannel,_.dh.jY=_.dh.receive,
_.dh.kY=_.dh.receiveSameDomain,_.dh.getOrigin=_.dh.getOrigin,_.dh.Do=_.dh.getTargetOrigin,_.dh.mO=_.dh._getTargetWin,_.dh.w5=_.dh._parseSiblingId);else{_.dh=function(){function a(B,ea){if(!W[B]){var T=cb;ea||(T=Ya);W[B]=T;ea=J[B]||[];for(var ua=0;ua<ea.length;++ua){var za=ea[ua];za.t=C[B];T.call(B,za.f,za)}J[B]=[]}}function b(){function B(){Eb=!0}Fb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",B,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
B),Fb=!0)}function c(B,ea,T,ua,za){C[ea]&&C[ea]===T||(_.lg("Invalid gadgets.rpc token. "+C[ea]+" vs "+T),ob(ea,2));za.onunload=function(){P[ea]&&!Eb&&(ob(ea,1),_.dh.VJ(ea))};b();ua=_.Ug(decodeURIComponent(ua))}function d(B,ea){if(B&&typeof B.s==="string"&&typeof B.f==="string"&&B.a instanceof Array)if(C[B.f]&&C[B.f]!==B.t&&(_.lg("Invalid gadgets.rpc token. "+C[B.f]+" vs "+B.t),ob(B.f,2)),B.s==="__ack")window.setTimeout(function(){a(B.f,!0)},0);else{B.c&&(B.callback=function(Y){_.dh.call(B.f,(B.g?
"legacy__":"")+"__cb",null,B.c,Y)});if(ea){var T=e(ea);B.origin=ea;var ua=B.r;try{var za=e(ua)}catch(Y){}ua&&za==T||(ua=ea);B.referer=ua}ea=(w[B.s]||w[""]).apply(B,B.a);B.c&&typeof ea!=="undefined"&&_.dh.call(B.f,"__cb",null,B.c,ea)}}function e(B){if(!B)return"";B=B.split("#")[0].split("?")[0];B=B.toLowerCase();B.indexOf("//")==0&&(B=window.location.protocol+B);B.indexOf("://")==-1&&(B=window.location.protocol+"//"+B);var ea=B.substring(B.indexOf("://")+3),T=ea.indexOf("/");T!=-1&&(ea=ea.substring(0,
T));B=B.substring(0,B.indexOf("://"));if(B!=="http"&&B!=="https"&&B!=="chrome-extension"&&B!=="file"&&B!=="android-app"&&B!=="chrome-search"&&B!=="chrome-untrusted"&&B!=="chrome"&&B!=="devtools")throw Error("t");T="";var ua=ea.indexOf(":");if(ua!=-1){var za=ea.substring(ua+1);ea=ea.substring(0,ua);if(B==="http"&&za!=="80"||B==="https"&&za!=="443")T=":"+za}return B+"://"+ea+T}function f(B){if(B.charAt(0)=="/"){var ea=B.indexOf("|"),T=ea>0?B.substring(1,ea):B.substring(1);B=ea>0?B.substring(ea+1):null;
return{id:T,origin:B}}return null}function h(B){if(typeof B==="undefined"||B==="..")return window.parent;var ea=f(B);if(ea)return k(window.top.frames[ea.id]);B=String(B);return(ea=window.frames[B])?k(ea):(ea=document.getElementById(B))&&ea.contentWindow?ea.contentWindow:null}function k(B){return B?"postMessage"in B?B:B instanceof HTMLIFrameElement&&"contentWindow"in B&&B.contentWindow!=null&&"postMessage"in B.contentWindow?B.contentWindow:null:null}function l(B,ea){if(P[B]!==!0){typeof P[B]==="undefined"&&
(P[B]=0);var T=h(B);B!==".."&&T==null||cb.Nb(B,ea)!==!0?P[B]!==!0&&P[B]++<10?window.setTimeout(function(){l(B,ea)},500):(W[B]=Ya,P[B]=!0):P[B]=!0}}function m(B){(B=y[B])&&B.substring(0,1)==="/"&&(B=B.substring(1,2)==="/"?document.location.protocol+B:document.location.protocol+"//"+document.location.host+B);return B}function n(B,ea,T){ea&&!/http(s)?:\/\/.+/.test(ea)&&(ea.indexOf("//")==0?ea=window.location.protocol+ea:ea.charAt(0)=="/"?ea=window.location.protocol+"//"+window.location.host+ea:ea.indexOf("://")==
-1&&(ea=window.location.protocol+"//"+ea));y[B]=ea;typeof T!=="undefined"&&(D[B]=!!T)}function p(B,ea){ea=ea||"";C[B]=String(ea);l(B,ea)}function q(B){B=(B.passReferrer||"").split(":",2);O=B[0]||"none";ja=B[1]||"origin"}function t(B){String(B.useLegacyProtocol)==="true"&&(cb=ch.nA||Ya,cb.zd(d,a))}function v(B,ea){function T(ua){ua=ua&&ua.rpc||{};q(ua);var za=ua.parentRelayUrl||"";za=e(la.parent||ea)+za;n("..",za,String(ua.useLegacyProtocol)==="true");t(ua);p("..",B)}!la.parent&&ea?T({}):_.ng.register("rpc",
null,T)}function u(B,ea,T){if(B==="..")v(T||la.rpctoken||la.ifpctok||"",ea);else a:{var ua=null;if(B.charAt(0)!="/"){if(!_.jg)break a;ua=document.getElementById(B);if(!ua)throw Error("u`"+B);}ua=ua&&ua.src;ea=ea||e(ua);n(B,ea);ea=_.jg.kh(ua);p(B,T||ea.rpctoken)}}var w={},y={},D={},C={},I=0,L={},P={},la={},W={},J={},O=null,ja=null,pa=window.top!==window.self,Ha=window.name,ob=function(){},ab=window.console,pb=ab&&ab.log&&function(B){ab.log(B)}||function(){},Ya=function(){function B(ea){return function(){pb(ea+
": call ignored")}}return{AS:function(){return"noop"},Zaa:function(){return!0},zd:B("init"),Nb:B("setup"),call:B("call")}}();_.jg&&(la=_.jg.kh());var Eb=!1,Fb=!1,cb=function(){if(la.rpctx=="rmr")return ch.IY;var B=typeof window.postMessage==="function"?ch.tN:typeof window.postMessage==="object"?ch.tN:window.ActiveXObject?ch.yW?ch.yW:ch.nA:navigator.userAgent.indexOf("WebKit")>0?ch.IY:navigator.product==="Gecko"?ch.frameElement:ch.nA;B||(B=Ya);return B}();w[""]=function(){pb("Unknown RPC service: "+
this.s)};w.__cb=function(B,ea){var T=L[B];T&&(delete L[B],T.call(this,ea))};return{config:function(B){typeof B.WY==="function"&&(ob=B.WY)},register:function(B,ea){if(B==="__cb"||B==="__ack")throw Error("v");if(B==="")throw Error("w");w[B]=ea},unregister:function(B){if(B==="__cb"||B==="__ack")throw Error("x");if(B==="")throw Error("y");delete w[B]},nY:function(B){w[""]=B},v0:function(){delete w[""]},hS:function(){},call:function(B,ea,T,ua){B=B||"..";var za="..";B===".."?za=Ha:B.charAt(0)=="/"&&(za=
e(window.location.href),za="/"+Ha+(za?"|"+za:""));++I;T&&(L[I]=T);var Y={s:ea,f:za,c:T?I:0,a:Array.prototype.slice.call(arguments,3),t:C[B],l:!!D[B]};a:if(O==="bidir"||O==="c2p"&&B===".."||O==="p2c"&&B!==".."){var da=window.location.href;var Va="?";if(ja==="query")Va="#";else if(ja==="hash")break a;Va=da.lastIndexOf(Va);Va=Va===-1?da.length:Va;da=da.substring(0,Va)}else da=null;da&&(Y.r=da);if(B===".."||f(B)!=null||document.getElementById(B))(da=W[B])||f(B)===null||(da=cb),ea.indexOf("legacy__")===
0&&(da=cb,Y.s=ea.substring(8),Y.c=Y.c?Y.c:I),Y.g=!0,Y.r=za,da?(D[B]&&(da=ch.nA),da.call(B,za,Y)===!1&&(W[B]=Ya,cb.call(B,za,Y))):J[B]?J[B].push(Y):J[B]=[Y]},Hu:m,Yj:n,zC:p,xw:u,ko:function(B){return C[B]},VJ:function(B){delete y[B];delete D[B];delete C[B];delete P[B];delete W[B]},ZS:function(){return cb.AS()},jY:function(B,ea){B.length>4?cb.Goa(B,d):c.apply(null,B.concat(ea))},kY:function(B){B.a=Array.prototype.slice.call(B.a);window.setTimeout(function(){d(B)},0)},getOrigin:e,Do:function(B){var ea=
null,T=m(B);T?ea=T:(T=f(B))?ea=T.origin:B==".."?ea=la.parent:(B=document.getElementById(B))&&B.tagName.toLowerCase()==="iframe"&&(ea=B.src);return e(ea)},zd:function(){cb.zd(d,a)===!1&&(cb=Ya);pa?u(".."):_.ng.register("rpc",null,function(B){B=B.rpc||{};q(B);t(B)})},mO:h,w5:f,Sga:"__ack",Rla:Ha||"..",bma:0,ama:1,Zla:2}}();_.dh.zd()};_.dh.config({WY:function(a){throw Error("z`"+a);}});_.r("gadgets.rpc.config",_.dh.config);_.r("gadgets.rpc.register",_.dh.register);_.r("gadgets.rpc.unregister",_.dh.unregister);_.r("gadgets.rpc.registerDefault",_.dh.nY);_.r("gadgets.rpc.unregisterDefault",_.dh.v0);_.r("gadgets.rpc.forceParentVerifiable",_.dh.hS);_.r("gadgets.rpc.call",_.dh.call);_.r("gadgets.rpc.getRelayUrl",_.dh.Hu);_.r("gadgets.rpc.setRelayUrl",_.dh.Yj);_.r("gadgets.rpc.setAuthToken",_.dh.zC);_.r("gadgets.rpc.setupReceiver",_.dh.xw);_.r("gadgets.rpc.getAuthToken",_.dh.ko);
_.r("gadgets.rpc.removeReceiver",_.dh.VJ);_.r("gadgets.rpc.getRelayChannel",_.dh.ZS);_.r("gadgets.rpc.receive",_.dh.jY);_.r("gadgets.rpc.receiveSameDomain",_.dh.kY);_.r("gadgets.rpc.getOrigin",_.dh.getOrigin);_.r("gadgets.rpc.getTargetOrigin",_.dh.Do);
_.jg=_.jg||{};_.jg.V5=function(a){var b=window;typeof b.addEventListener!="undefined"?b.addEventListener("mousemove",a,!1):typeof b.attachEvent!="undefined"?b.attachEvent("onmousemove",a):_.kg("cannot attachBrowserEvent: mousemove")};_.jg.wda=function(a){var b=window;b.removeEventListener?b.removeEventListener("mousemove",a,!1):b.detachEvent?b.detachEvent("onmousemove",a):_.kg("cannot removeBrowserEvent: mousemove")};
_.Th=function(){function a(m){var n=new _.Sh;n.jx(m);return n.Zi()}var b=window.crypto;if(b&&typeof b.getRandomValues=="function")return function(){var m=new window.Uint32Array(1);b.getRandomValues(m);return Number("0."+m[0])};var c=_.Sf("random/maxObserveMousemove");c==null&&(c=-1);var d=0,e=Math.random(),f=1,h=(screen.width*screen.width+screen.height)*1E6,k=function(m){m=m||window.event;var n=m.screenX+m.clientX<<16;n+=m.screenY+m.clientY;n*=(new Date).getTime()%1E6;f=f*n%h;c>0&&++d==c&&_.jg.wda(k)};
c!=0&&_.jg.V5(k);var l=a(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+e);return function(){var m=f;m+=parseInt(l.substr(0,20),16);l=a(l);return m/(h+Math.pow(16,20))}}();_.r("shindig.random",_.Th);
var Uh=function(a){return{execute:function(b){var c={method:a.httpMethod||"GET",root:a.root,path:a.url,params:a.urlParams,headers:a.headers,body:a.body},d=window.gapi,e=function(){var f=d.config.get("client/apiKey"),h=d.config.get("client/version");try{var k=d.config.get("googleapis.config/developerKey"),l=d.config.get("client/apiKey",k);d.config.update("client/apiKey",l);d.config.update("client/version","1.0.0-alpha");var m=d.client;m.request.call(m,c).then(b,b)}finally{d.config.update("client/apiKey",
f),d.config.update("client/version",h)}};d.client?e():d.load.call(d,"client",e)}}},Vh=function(a,b){return function(c){var d={};c=c.body;var e=_.Ug(c),f={};if(e&&e.length)for(var h=e.length,k=0;k<h;++k){var l=e[k];f[l.id]=l}h=b.length;for(k=0;k<h;++k)l=b[k].id,d[l]=e&&e.length?f[l]:e;a(d,c)}},Wh=function(a){a.transport={name:"googleapis",execute:function(b,c){for(var d=[],e=b.length,f=0;f<e;++f){var h=b[f],k=h.method,l=String(k).split(".")[0];l=_.Sf("googleapis.config/versions/"+k)||_.Sf("googleapis.config/versions/"+
l)||"v1";d.push({jsonrpc:"2.0",id:h.id,method:k,apiVersion:String(l),params:h.params})}b=Uh({httpMethod:"POST",root:a.transport.root,url:"/rpc?pp=0",headers:{"Content-Type":"application/json"},body:d});b.execute.call(b,Vh(c,d))},root:void 0}},Xh=function(a){var b=this.method,c=this.transport;c.execute.call(c,[{method:b,id:b,params:this.rpc}],function(d){d=d[b];d.error||(d=d.data||d.result);a(d)})},Zh=function(){for(var a=Yh,b=a.split("."),c=function(k){k=k||{};k.groupId=k.groupId||"@self";k.userId=
k.userId||"@viewer";k={method:a,rpc:k||{}};Wh(k);k.execute=Xh;return k},d=_.Sa,e=b.length,f=0;f<e;++f){var h=d[b[f]]||{};f+1==e&&(h=c);d=d[b[f]]=h}if(b.length>1&&b[0]!="googleapis")for(b[0]="googleapis",b[b.length-1]=="delete"&&(b[b.length-1]="remove"),d=_.Sa,e=b.length,f=0;f<e;++f)h=d[b[f]]||{},f+1==e&&(h=c),d=d[b[f]]=h},Yh;for(Yh in _.Sf("googleapis.config/methods"))Zh();_.r("googleapis.newHttpRequest",function(a){return Uh(a)});_.r("googleapis.setUrlParameter",function(a,b){if(a!=="trace")throw Error("C");_.Tf("client/trace",b)});
});
// Google Inc.
