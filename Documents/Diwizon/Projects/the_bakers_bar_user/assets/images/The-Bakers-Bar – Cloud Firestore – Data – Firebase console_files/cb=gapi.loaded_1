gapi.loaded_1(function(_){var window=this;
_.Kh=(window.gapi||{}).load;
_.To=_.vf(_.If,"rw",_.wf());
var Uo=function(a,b){(a=_.To[a])&&a.state<b&&(a.state=b)};var Vo=function(a){a=(a=_.To[a])?a.oid:void 0;if(a){var b=_.sf.getElementById(a);b&&b.parentNode.removeChild(b);delete _.To[a];Vo(a)}};_.Wo=function(a){a=a.container;typeof a==="string"&&(a=document.getElementById(a));return a};_.Xo=function(a){var b=a.clientWidth;return"position:absolute;top:-10000px;width:"+(b?b+"px":a.style.width||"300px")+";margin:0px;border-style:none;"};
_.Yo=function(a,b){var c={},d=a.Ac(),e=b&&b.width,f=b&&b.height,h=b&&b.verticalAlign;h&&(c.verticalAlign=h);e||(e=d.width||a.width);f||(f=d.height||a.height);d.width=c.width=e;d.height=c.height=f;d=a.getIframeEl();e=a.getId();Uo(e,2);a:{e=a.getSiteEl();c=c||{};if(_.If.oa){var k=d.id;if(k){f=(f=_.To[k])?f.state:void 0;if(f===1||f===4)break a;Vo(k)}}(f=e.nextSibling)&&f.dataset&&f.dataset.gapistub&&(e.parentNode.removeChild(f),e.style.cssText="");f=c.width;h=c.height;var l=e.style;l.textIndent="0";
l.margin="0";l.padding="0";l.background="transparent";l.borderStyle="none";l.cssFloat="none";l.styleFloat="none";l.lineHeight="normal";l.fontSize="1px";l.verticalAlign="baseline";e=e.style;e.display="inline-block";d=d.style;d.position="static";d.left="0";d.top="0";d.visibility="visible";f&&(e.width=d.width=f+"px");h&&(e.height=d.height=h+"px");c.verticalAlign&&(e.verticalAlign=c.verticalAlign);k&&Uo(k,3)}(k=b?b.title:null)&&a.getIframeEl().setAttribute("title",k);(b=b?b.ariaLabel:null)&&a.getIframeEl().setAttribute("aria-label",
b)};_.Zo=function(a){var b=a.getSiteEl();b&&b.removeChild(a.getIframeEl())};_.$o=function(a){a.where=_.Wo(a);var b=a.messageHandlers=a.messageHandlers||{},c=function(e){_.Yo(this,e)};b._ready=c;b._renderstart=c;var d=a.onClose;a.onClose=function(e){d&&d.call(this,e);_.Zo(this)};a.onCreate=function(e){e=e.getIframeEl();e.style.cssText=_.Xo(e)}};
_.Lj=function(a){var b=window;a=(a||b.location.href).match(RegExp(".*(\\?|#|&)usegapi=([^&#]+)"))||[];return"1"===decodeURIComponent(a[a.length-1]||"")};
_.ap=function(a,b){a.T.where=b;return a};_.bp=function(){_.Jk.apply(this,arguments)};_.A(_.bp,_.Jk);
_.cp=_.wf();
_.dp={};window.iframer=_.dp;
var fp=function(a){var b=[new ep];if(b.length===0)throw Error("m");if(b.map(function(c){if(c instanceof ep)c=c.WX;else throw Error("m");return c}).every(function(c){return"data-gapiscan".indexOf(c)!==0}))throw Error("r`data-gapiscan");a.setAttribute("data-gapiscan","true")},ep=function(){this.WX=gp[0].toLowerCase()},hp,ip,jp,kp,lp,pp,qp;ep.prototype.toString=function(){return this.WX};hp=function(a){if(_.uf.test(Object.keys))return Object.keys(a);var b=[],c;for(c in a)_.xf(a,c)&&b.push(c);return b};
ip={button:!0,div:!0,span:!0};jp=function(a){var b=_.vf(_.If,"sws",[]);return _.tn.call(b,a)>=0};kp=function(a){return _.vf(_.If,"watt",_.wf())[a]};lp=function(a){return function(b,c){return a?_.$m()[c]||a[c]||"":_.$m()[c]||""}};_.mp={callback:1,clientid:1,cookiepolicy:1,openidrealm:-1,includegrantedscopes:-1,requestvisibleactions:1,scope:1};_.np=!1;
_.op=function(){if(!_.np){for(var a=document.getElementsByTagName("meta"),b=0;b<a.length;++b){var c=a[b].name.toLowerCase();if(_.fd(c,"google-signin-")){c=c.substring(14);var d=a[b].content;_.mp[c]&&d&&(_.cp[c]=d)}}if(window.self!==window.top){a=document.location.toString();for(var e in _.mp)_.mp[e]>0&&(b=_.Cf(a,e,""))&&(_.cp[e]=b)}_.np=!0}e=_.wf();_.Af(_.cp,e);return e};pp=function(a){var b;a.match(/^https?%3A/i)&&(b=decodeURIComponent(a));a=b?b:a;return _.Gm(document,a)};
qp=function(a){a=a||"canonical";for(var b=document.getElementsByTagName("link"),c=0,d=b.length;c<d;c++){var e=b[c],f=e.getAttribute("rel");if(f&&f.toLowerCase()==a&&(e=e.getAttribute("href"))&&(e=pp(e))&&e.match(/^https?:\/\/[\w\-_\.]+/i)!=null)return e}return window.location.href};_.rp=function(){return window.location.origin||window.location.protocol+"//"+window.location.host};_.sp=function(a,b,c,d){return(a=typeof a=="string"?a:void 0)?pp(a):qp(d)};
_.tp=function(a,b,c){a==null&&c&&(a=c.db,a==null&&(a=c.gwidget&&c.gwidget.db));return a||void 0};_.up=function(a,b,c){a==null&&c&&(a=c.ecp,a==null&&(a=c.gwidget&&c.gwidget.ecp));return a||void 0};_.vp=function(a,b,c){return _.sp(a,b,c,b.action?void 0:"publisher")};var wp,xp,yp,zp,Ap,Bp,Dp,Cp;wp={se:"0"};xp={post:!0};yp={style:"position:absolute;top:-10000px;width:450px;margin:0px;border-style:none"};zp="onPlusOne _ready _close _open _resizeMe _renderstart oncircled drefresh erefresh".split(" ");Ap=_.vf(_.If,"WI",_.wf());Bp=["style","data-gapiscan"];
Dp=function(a){for(var b=_.wf(),c=a.nodeName.toLowerCase().indexOf("g:")!=0,d=a.attributes.length,e=0;e<d;e++){var f=a.attributes[e],h=f.name,k=f.value;_.tn.call(Bp,h)>=0||c&&h.indexOf("data-")!=0||k==="null"||"specified"in f&&!f.specified||(c&&(h=h.substr(5)),b[h.toLowerCase()]=k)}a=a.style;(c=Cp(a&&a.height))&&(b.height=String(c));(a=Cp(a&&a.width))&&(b.width=String(a));return b};
_.Fp=function(a,b,c,d,e,f){if(c.rd)var h=b;else h=document.createElement("div"),b.dataset.gapistub=!0,h.style.cssText="position:absolute;width:450px;left:-10000px;",b.parentNode.insertBefore(h,b);f.siteElement=h;h.id||(h.id=_.Ep(a));b=_.wf();b[">type"]=a;_.Af(c,b);a=_.dn(d,h,e);f.iframeNode=a;f.id=a.getAttribute("id")};_.Ep=function(a){_.vf(Ap,a,0);return"___"+a+"_"+Ap[a]++};Cp=function(a){var b=void 0;typeof a==="number"?b=a:typeof a==="string"&&(b=parseInt(a,10));return b};var gp=_.pf(["data-"]),Gp,Hp,Ip,Jp,Kp=/(?:^|\s)g-((\S)*)(?:$|\s)/,Lp={plusone:!0,autocomplete:!0,profile:!0,signin:!0,signin2:!0};Gp=_.vf(_.If,"SW",_.wf());Hp=_.vf(_.If,"SA",_.wf());Ip=_.vf(_.If,"SM",_.wf());Jp=_.vf(_.If,"FW",[]);
var Mp=function(a,b){return(typeof a==="string"?document.getElementById(a):a)||b},Qp=function(a,b){var c;Np.ps0=(new Date).getTime();Op("ps0");a=Mp(a,_.sf);var d=_.sf.documentMode;if(a.querySelectorAll&&(!d||d>8)){d=b?[b]:hp(Gp).concat(hp(Hp)).concat(hp(Ip));for(var e=[],f=0;f<d.length;f++){var h=d[f];e.push(".g-"+h,"g\\:"+h)}d=a.querySelectorAll(e.join(","))}else d=a.getElementsByTagName("*");a=_.wf();for(e=0;e<d.length;e++){f=d[e];var k=f;h=b;var l=k.nodeName.toLowerCase(),m=void 0;if(k.hasAttribute("data-gapiscan"))h=
null;else{var n=l.indexOf("g:");n==0?m=l.substr(2):(n=(n=String(k.className||k.getAttribute("class")))&&Kp.exec(n))&&(m=n[1]);h=!m||!(Gp[m]||Hp[m]||Ip[m])||h&&m!==h?null:m}h&&(Lp[h]||f.nodeName.toLowerCase().indexOf("g:")==0||hp(Dp(f)).length!=0)&&(fp(f),_.vf(a,h,[]).push(f))}for(p in a)Jp.push(p);Np.ps1=(new Date).getTime();Op("ps1");if(b=Jp.join(":"))try{_.Bf.load(b,void 0)}catch(q){_.Zg.log(q);return}e=[];for(c in a){d=a[c];var p=0;for(b=d.length;p<b;p++)f=d[p],Pp(c,f,Dp(f),e,b)}};var Rp=function(a,b){var c=kp(a);b&&c?(c(b),(c=b.iframeNode)&&c.setAttribute("data-gapiattached",!0)):_.Bf.load(a,function(){var d=kp(a),e=b&&b.iframeNode,f=b&&b.userParams;e&&d?(d(b),e.setAttribute("data-gapiattached",!0)):(d=_.Bf[a].go,a=="signin2"?d(e,f):d(e&&e.parentNode,f))})},Pp=function(a,b,c,d,e,f,h){switch(Sp(b,a,f)){case 0:a=Ip[a]?a+"_annotation":a;d={};d.iframeNode=b;d.userParams=c;Rp(a,d);break;case 1:if(b.parentNode){for(var k in c){if(f=_.xf(c,k))f=c[k],f=!!f&&typeof f==="object"&&(!f.toString||
f.toString===Object.prototype.toString||f.toString===Array.prototype.toString);if(f)try{c[k]=_.Vg(c[k])}catch(w){delete c[k]}}k=!0;c.dontclear&&(k=!1);delete c.dontclear;var l;f={};var m=l=a;a=="plus"&&c.action&&(l=a+"_"+c.action,m=a+"/"+c.action);(l=_.Sf("iframes/"+l+"/url"))||(l=":im_socialhost:/:session_prefix::im_prefix:_/widget/render/"+m+"?usegapi=1");for(n in wp)f[n]=n+"/"+(c[n]||wp[n])+"/";var n=_.Gm(_.sf,l.replace(_.Zm,lp(f)));m="iframes/"+a+"/params/";f={};_.Af(c,f);(l=_.Sf("lang")||_.Sf("gwidget/lang"))&&
(f.hl=l);xp[a]||(f.origin=_.rp());f.exp=_.Sf(m+"exp");if(m=_.Sf(m+"location"))for(l=0;l<m.length;l++){var p=m[l];f[p]=_.rf.location[p]}switch(a){case "plus":case "follow":f.url=_.vp(f.href,c,null);delete f.href;break;case "plusone":m=(m=c.href)?pp(m):qp();f.url=m;f.db=_.tp(c.db,void 0,_.Sf());f.ecp=_.up(c.ecp,void 0,_.Sf());delete f.href;break;case "signin":f.url=qp()}_.If.ILI&&(f.iloader="1");delete f["data-onload"];delete f.rd;for(var q in wp)f[q]&&delete f[q];f.gsrc=_.Sf("iframes/:source:");q=
_.Sf("inline/css");typeof q!=="undefined"&&e>0&&q>=e&&(f.ic="1");q=/^#|^fr-/;e={};for(var t in f)_.xf(f,t)&&q.test(t)&&(e[t.replace(q,"")]=f[t],delete f[t]);t=_.Sf("iframes/"+a+"/params/si")=="q"?f:e;q=_.op();for(var v in q)!_.xf(q,v)||_.xf(f,v)||_.xf(e,v)||(t[v]=q[v]);v=[].concat(zp);t=_.Sf("iframes/"+a+"/methods");_.sn(t)&&(v=v.concat(t));for(u in c)_.xf(c,u)&&/^on/.test(u)&&(a!="plus"||u!="onconnect")&&(v.push(u),delete f[u]);delete f.callback;e._methods=v.join(",");var u=_.Fm(n,f,e);v=h||{};v.allowPost=
1;v.attributes=yp;v.dontclear=!k;h={};h.userParams=c;h.url=u;h.type=a;_.Fp(a,b,c,u,v,h);b=h.id;c=_.wf();c.id=b;c.userParams=h.userParams;c.url=h.url;c.type=h.type;c.state=1;_.To[b]=c;b=h}else b=null;b&&((c=b.id)&&d.push(c),Rp(a,b))}},Sp=function(a,b,c){if(a&&a.nodeType===1&&b){if(c)return 1;if(Ip[b]){if(ip[a.nodeName.toLowerCase()])return(a=a.innerHTML)&&a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")?0:1}else{if(Hp[b])return 0;if(Gp[b])return 1}}return null};_.vf(_.Bf,"platform",{}).go=function(a,b){Qp(a,b)};var Tp=_.vf(_.If,"perf",_.wf()),Np=_.vf(Tp,"g",_.wf()),Up=_.vf(Tp,"i",_.wf()),Vp,Wp,Xp,Op,Zp,$p,aq;_.vf(Tp,"r",[]);Vp=_.wf();Wp=_.wf();Xp=function(a,b,c,d){Vp[c]=Vp[c]||!!d;_.vf(Wp,c,[]);Wp[c].push([a,b])};Op=function(a,b,c){var d=Tp.r;typeof d==="function"?d(a,b,c):d.push([a,b,c])};Zp=function(a,b,c,d){if(b=="_p")throw Error("P");_.Yp(a,b,c,d)};_.Yp=function(a,b,c,d){$p(b,c)[a]=d||(new Date).getTime();Op(a,b,c)};$p=function(a,b){a=_.vf(Up,a,_.wf());return _.vf(a,b,_.wf())};
aq=function(a,b,c){var d=null;b&&c&&(d=$p(b,c)[a]);return d||Np[a]};(function(){function a(h){this.t={};this.tick=function(k,l,m){this.t[k]=[m!=void 0?m:(new Date).getTime(),l];if(m==void 0)try{window.console.timeStamp("CSI/"+k)}catch(n){}};this.getStartTickTime=function(){return this.t.start[0]};this.tick("start",null,h)}var b;if(window.performance)var c=(b=window.performance.timing)&&b.responseStart;var d=c>0?new a(c):new a;window.__gapi_jstiming__={Timer:a,load:d};if(b){var e=b.navigationStart;e>0&&c>=e&&(window.__gapi_jstiming__.srt=c-e)}if(b){var f=window.__gapi_jstiming__.load;
e>0&&c>=e&&(f.tick("_wtsrt",void 0,e),f.tick("wtsrt_","_wtsrt",c),f.tick("tbsd_","wtsrt_"))}try{b=null,window.chrome&&window.chrome.csi&&(b=Math.floor(window.chrome.csi().pageT),f&&e>0&&(f.tick("_tbnd",void 0,window.chrome.csi().startE),f.tick("tbnd_","_tbnd",e))),b==null&&window.gtbExternal&&(b=window.gtbExternal.pageT()),b==null&&window.external&&(b=window.external.pageT,f&&e>0&&(f.tick("_tbnd",void 0,window.external.startE),f.tick("tbnd_","_tbnd",e))),b&&(window.__gapi_jstiming__.pt=b)}catch(h){}})();if(window.__gapi_jstiming__){window.__gapi_jstiming__.fP={};window.__gapi_jstiming__.Eda=1;var bq=function(a,b,c){var d=a.t[b],e=a.t.start;if(d&&(e||c))return d=a.t[b][0],e=c!=void 0?c:e[0],Math.round(d-e)},cq=function(a,b,c){var d="";window.__gapi_jstiming__.srt&&(d+="&srt="+window.__gapi_jstiming__.srt,delete window.__gapi_jstiming__.srt);window.__gapi_jstiming__.pt&&(d+="&tbsrt="+window.__gapi_jstiming__.pt,delete window.__gapi_jstiming__.pt);try{window.external&&window.external.tran?d+="&tran="+
window.external.tran:window.gtbExternal&&window.gtbExternal.tran?d+="&tran="+window.gtbExternal.tran():window.chrome&&window.chrome.csi&&(d+="&tran="+window.chrome.csi().tran)}catch(p){}var e=window.chrome;if(e&&(e=e.loadTimes)&&typeof e==="function"&&(e=e())){e.wasFetchedViaSpdy&&(d+="&p=s");if(e.wasNpnNegotiated){d+="&npn=1";var f=e.npnNegotiatedProtocol;f&&(d+="&npnv="+(encodeURIComponent||escape)(f))}e.wasAlternateProtocolAvailable&&(d+="&apa=1")}var h=a.t,k=h.start;e=[];f=[];for(var l in h)if(l!=
"start"&&l.indexOf("_")!=0){var m=h[l][1];m?h[m]&&f.push(l+"."+bq(a,l,h[m][0])):k&&e.push(l+"."+bq(a,l))}delete h.start;if(b)for(var n in b)d+="&"+n+"="+b[n];(b=c)||(b="https:"==document.location.protocol?"https://csi.gstatic.com/csi":"http://csi.gstatic.com/csi");return[b,"?v=3","&s="+(window.__gapi_jstiming__.sn||"gwidget")+"&action=",a.name,f.length?"&it="+f.join(","):"",d,"&rt=",e.join(",")].join("")},dq=function(a,b,c){a=cq(a,b,c);if(!a)return"";b=new Image;var d=window.__gapi_jstiming__.Eda++;
window.__gapi_jstiming__.fP[d]=b;b.onload=b.onerror=function(){window.__gapi_jstiming__&&delete window.__gapi_jstiming__.fP[d]};b.src=a;b=null;return a};window.__gapi_jstiming__.report=function(a,b,c){var d=document.visibilityState,e="visibilitychange";d||(d=document.webkitVisibilityState,e="webkitvisibilitychange");if(d=="prerender"){var f=!1,h=function(){if(!f){b?b.prerender="1":b={prerender:"1"};if((document.visibilityState||document.webkitVisibilityState)=="prerender")var k=!1;else dq(a,b,c),
k=!0;k&&(f=!0,document.removeEventListener(e,h,!1))}};document.addEventListener(e,h,!1);return""}return dq(a,b,c)}};var eq={g:"gapi_global",m:"gapi_module",w:"gwidget"},fq=function(a,b){this.type=a?a=="_p"?"m":"w":"g";this.name=a;this.Js=b};fq.prototype.key=function(){switch(this.type){case "g":return this.type;case "m":return this.type+"."+this.Js;case "w":return this.type+"."+this.name+this.Js}};
var gq=new fq,hq=navigator.userAgent.match(/iPhone|iPad|Android|PalmWebOS|Maemo|Bada/),iq=_.vf(Tp,"_c",_.wf()),jq=Math.random()<(_.Sf("csi/rate")||0),lq=function(a,b,c){for(var d=new fq(b,c),e=_.vf(iq,d.key(),_.wf()),f=Wp[a]||[],h=0;h<f.length;++h){var k=f[h],l=k[0],m=a,n=b,p=c;k=aq(k[1],n,p);m=aq(m,n,p);e[l]=k&&m?m-k:null}Vp[a]&&jq&&(kq(gq),kq(d))},mq=function(a,b){b=b||[];for(var c=[],d=0;d<b.length;d++)c.push(a+b[d]);return c},kq=function(a){var b=_.rf.__gapi_jstiming__;b.sn=eq[a.type];var c=new b.Timer(0);
a:{switch(a.type){case "g":var d="global";break a;case "m":d=a.Js;break a;case "w":d=a.name;break a}d=void 0}c.name=d;d=!1;var e=a.key(),f=iq[e];c.tick("_start",null,0);for(var h in f)c.tick(h,"_start",f[h]),d=!0;iq[e]=_.wf();d&&(h=[],h.push("l"+(_.Sf("isPlusUser")?"1":"0")),d="m"+(hq?"1":"0"),h.push(d),a.type=="m"?h.push("p"+a.Js):a.type=="w"&&(e="n"+a.Js,h.push(e),a.Js=="0"&&h.push(d+e)),h.push("u"+(_.Sf("isLoggedIn")?"1":"0")),a=mq("",h),a=mq("abc_",a).join(","),b.report(c,{e:a}))};
Xp("blt","bs0","bs1");Xp("psi","ps0","ps1");Xp("rpcqi","rqe","rqd");Xp("bsprt","bsrt0","bsrt1");Xp("bsrqt","bsrt1","bsrt2");Xp("bsrst","bsrt2","bsrt3");Xp("mli","ml0","ml1");Xp("mei","me0","me1",!0);Xp("wcdi","wrs","wcdi");Xp("wci","wrs","wdc");Xp("wdi","wrs","wrdi");Xp("wdt","bs0","wrdt");Xp("wri","wrs","wrri",!0);Xp("wrt","bs0","wrrt");Xp("wji","wje0","wje1",!0);Xp("wjli","wjl0","wjl1");Xp("whi","wh0","wh1",!0);Xp("wai","waaf0","waaf1",!0);Xp("wadi","wrs","waaf1",!0);Xp("wadt","bs0","waaf1",!0);
Xp("wprt","wrt0","wrt1");Xp("wrqt","wrt1","wrt2");Xp("wrst","wrt2","wrt3",!0);Xp("fbprt","fsrt0","fsrt1");Xp("fbrqt","fsrt1","fsrt2");Xp("fbrst","fsrt2","fsrt3",!0);Xp("fdns","fdns0","fdns1");Xp("fcon","fcon0","fcon1");Xp("freq","freq0","freq1");Xp("frsp","frsp0","frsp1");Xp("fttfb","fttfb0","fttfb1");Xp("ftot","ftot0","ftot1",!0);var nq=Tp.r;if(typeof nq!=="function"){for(var oq;oq=nq.shift();)lq.apply(null,oq);Tp.r=lq};var pq=["div"],qq="onload",rq=!0,sq=!0,tq=function(a){return a},uq=null,vq=function(a){var b=_.Sf(a);return typeof b!=="undefined"?b:_.Sf("gwidget/"+a)},zq,Aq,Bq,Cq,Dq,Eq,Fq,Lq,Gq,Mq,Nq,Oq,Pq,Qq,Hq,Jq,Rq,Iq,Sq,Tq,Uq,Vq,Wq,Xq;uq=_.Sf();_.Sf("gwidget");var wq=vq("parsetags");qq=wq==="explicit"||wq==="onload"?wq:qq;var xq=vq("google_analytics");typeof xq!=="undefined"&&(rq=!!xq);var yq=vq("data_layer");typeof yq!=="undefined"&&(sq=!!yq);zq=function(){var a=this&&this.getId();a&&(_.If.drw=a)};
Aq=function(){_.If.drw=null};Bq=function(a){return function(b){var c=a;typeof b==="number"?c=b:typeof b==="string"&&(c=b.indexOf("px"),c!=-1&&(b=b.substring(0,c)),c=parseInt(b,10));return c}};Cq=function(a){typeof a==="string"&&(a=window[a]);return typeof a==="function"?a:null};Dq=function(){return vq("lang")||"en-US"};
Eq=function(a){if(!_.Pa.Bb("attach")){var b={},c=_.Pa.Bb("inline"),d;for(d in c)c.hasOwnProperty(d)&&(b[d]=c[d]);b.open=function(e){var f=e.Ac().renderData.id;f=document.getElementById(f);if(!f)throw Error("Q");return c.attach(e,f)};_.Pa.Lc("attach",b)}a.style="attach"};Fq=function(){var a={};a.width=[Bq(450)];a.height=[Bq(24)];a.onready=[Cq];a.lang=[Dq,"hl"];a.iloader=[function(){return _.If.ILI},"iloader"];return a}();
Lq=function(a){var b={};b.Ze=a[0];b.gq=-1;b.ssa="___"+b.Ze+"_";b.Jga="g:"+b.Ze;b.tqa="g-"+b.Ze;b.sY=[];b.config={};b.jy=[];b.G0={};b.pD={};var c=function(e){for(var f in e)if(_.xf(e,f)){b.config[f]=[Cq];b.jy.push(f);var h=e[f],k=null,l=null,m=null;typeof h==="function"?k=h:h&&typeof h==="object"&&(k=h.hqa,l=h.qD,m=h.mN);m&&(b.jy.push(m),b.config[m]=[Cq],b.G0[f]=m);k&&(b.config[f]=[k]);l&&(b.pD[f]=l)}},d=function(e){for(var f={},h=0;h<e.length;++h)f[e[h].toLowerCase()]=1;f[b.Jga]=1;b.Dba=f};a[1]&&
(b.parameters=a[1]);(function(e){b.config=e;for(var f in Fq)Fq.hasOwnProperty(f)&&!b.config.hasOwnProperty(f)&&(b.config[f]=Fq[f])})(a[2]||{});a[3]&&c(a[3]);a[4]&&d(a[4]);a[5]&&(b.bn=a[5]);b.isa=a[6]===!0;b.eda=a[7];b.yga=a[8];b.Dba||d(pq);b.hJ=function(e){b.gq++;Zp("wrs",b.Ze,String(b.gq));var f=[],h=e.element,k=e.config,l=":"+b.Ze;l==":plus"&&e.Ld&&e.Ld.action&&(l+="_"+e.Ld.action);var m=Gq(b,k),n={};_.Af(_.op(),n);for(var p in e.Ld)e.Ld[p]!=null&&(n[p]=e.Ld[p]);p={container:h.id,renderData:e.zda,
style:"inline",height:k.height,width:k.width};Eq(p);b.bn&&(f[2]=p,f[3]=n,f[4]=m,b.bn("i",f));l=_.Pa.open(l,p,n,m);e=e.W6;Hq(l,k);Iq(l,h);Jq(b,l,e);Kq(b.Ze,b.gq.toString(),l);f[5]=l;b.bn&&b.bn("e",f)};return b};
Gq=function(a,b){for(var c={},d=a.jy.length-1;d>=0;--d){var e=a.jy[d],f=b[a.G0[e]||e]||b[e],h=b[e];h&&f!==h&&(f=function(l,m){return function(n){m.apply(this,arguments);l.apply(this,arguments)}}(f,h));f&&(c[e]=f)}for(var k in a.pD)a.pD.hasOwnProperty(k)&&(c[k]=Mq(c[k]||function(){},a.pD[k]));c.drefresh=zq;c.erefresh=Aq;return c};
Mq=function(a,b){return function(c){var d=b(c);if(d){var e=c.href||null;if(rq){if(window._gat)try{var f=window._gat._getTrackerByName("~0");f&&f._getAccount()!="UA-XXXXX-X"?f._trackSocial("Google",d,e):window._gaq&&window._gaq.push(["_trackSocial","Google",d,e])}catch(k){}if(window.ga&&window.ga.getAll)try{var h=window.ga.getAll();for(f=0;f<h.length;f++)h[f].send("social","Google",d,e)}catch(k){}}if(sq&&window.dataLayer)try{window.dataLayer.push({event:"social",socialNetwork:"Google",socialAction:d,
socialTarget:e})}catch(k){}}a.call(this,c)}};Nq=function(a){return _.qo&&a instanceof _.qo};Oq=function(a){return Nq(a)?"_renderstart":"renderstart"};Pq=function(a){return Nq(a)?"_ready":"ready"};Qq=function(){return!0};Hq=function(a,b){if(b.onready){var c=!1,d=function(){c||(c=!0,b.onready.call(null))};a.register(Pq(a),d,Qq);a.register(Oq(a),d,Qq)}};
Jq=function(a,b,c){var d=a.Ze,e=String(a.gq),f=!1,h=function(){f||(f=!0,b.getIframeEl(),c&&Zp("wrdt",d,e),Zp("wrdi",d,e))};b.register(Oq(b),h,Qq);var k=!1;a=function(){k||(k=!0,h(),c&&Zp("wrrt",d,e),Zp("wrri",d,e))};b.register(Pq(b),a,Qq);Nq(b)?b.register("widget-interactive-"+b.id,a,Qq):_.dh.register("widget-interactive-"+b.id,a);_.dh.register("widget-csi-tick-"+b.id,function(l,m,n){l==="wdc"?Zp("wdc",d,e,n):l==="wje0"?Zp("wje0",d,e,n):l==="wje1"?Zp("wje1",d,e,n):l=="wh0"?_.Yp("wh0",d,e,n):l=="wh1"?
_.Yp("wh1",d,e,n):l=="wcdi"&&_.Yp("wcdi",d,e,n)})};Rq=function(a){return typeof a=="number"?a+"px":a=="100%"?a:null};Iq=function(a,b){var c=function(d){d=d||a;var e=Rq(d.width);e&&b.style.width!=e&&(b.style.width=e);(d=Rq(d.height))&&b.style.height!=d&&(b.style.height=d)};Nq(a)?a.setParam("onRestyle",c):(a.register("ready",c,Qq),a.register("renderstart",c,Qq),a.register("resize",c,Qq))};Sq=function(a,b){for(var c in Fq)if(Fq.hasOwnProperty(c)){var d=Fq[c][1];d&&!b.hasOwnProperty(d)&&(b[d]=a[d])}return b};
Tq=function(a,b){var c={},d;for(d in a)a.hasOwnProperty(d)&&(c[a[d][1]||d]=(a[d]&&a[d][0]||tq)(b[d.toLowerCase()],b,uq));return c};Uq=function(a){if(a=a.eda)for(var b=0;b<a.length;b++)(new Image).src=a[b]};Vq=function(a,b){var c=b.userParams,d=b.siteElement;d||(d=(d=b.iframeNode)&&d.parentNode);if(d&&d.nodeType===1){var e=Tq(a.config,c);a.sY.push({element:d,config:e,Ld:Sq(e,Tq(a.parameters,c)),nra:3,W6:!!c["data-onload"],zda:b})}b=a.sY;for(a=a.hJ;b.length>0;)a(b.shift())};
Wq=function(a,b){a.gq++;Zp("wrs",a.Ze,String(a.gq));var c=b.userParams,d=Tq(a.config,c),e=[],f=b.iframeNode,h=b.siteElement,k=Gq(a,d),l=Tq(a.parameters,c);_.Af(_.op(),l);l=Sq(d,l);c=!!c["data-onload"];var m=_.Hn,n=_.wf();n.renderData=b;n.height=d.height;n.width=d.width;n.id=b.id;n.url=b.url;n.iframeEl=f;n.where=n.container=h;n.apis=["_open"];n.messageHandlers=k;n.messageHandlersFilter=_.Kn;_.$o(n);f=l;a.bn&&(e[2]=n,e[3]=f,e[4]=k,a.bn("i",e));k=m.attach(n);k.id=b.id;k.EL(k,n);Hq(k,d);Iq(k,h);Jq(a,
k,c);Kq(a.Ze,a.gq.toString(),k);e[5]=k;a.bn&&a.bn("e",e)};Xq=function(a,b){var c=b.url;a.yga||_.Lj(c)?Wq(a,b):_.Pa.open?Vq(a,b):(0,_.Kh)("iframes",function(){Vq(a,b)})};
_.Yq=function(a){var b=Lq(a);Uq(b);_.bh(b.Ze,function(d){Xq(b,d)});Gp[b.Ze]=!0;var c={Aa:function(d,e,f){var h=e||{};h.type=b.Ze;e=h.type;delete h.type;var k=Mp(d);if(k){d={};for(var l in h)_.xf(h,l)&&(d[l.toLowerCase()]=h[l]);d.rd=1;(l=!!d.ri)&&delete d.ri;Pp(e,k,d,[],0,l,f)}else _.Zg.log("gapi."+e+".render: missing element "+typeof d==="string"?d:"")},go:function(d){Qp(d,b.Ze)},pra:function(){var d=_.vf(_.If,"WI",_.wf()),e;for(e in d)delete d[e]}};a=function(){qq==="onload"&&c.go()};if(!jp(b.Ze)){if(!_.$g())try{a()}catch(d){}_.ah(a)}_.r("gapi."+
b.Ze+".go",c.go);_.r("gapi."+b.Ze+".render",c.Aa);return c};var Zq=function(){var a=window;return!!a.performance&&!!a.performance.getEntries},Kq=function(a,b,c){if(Zq()){var d=function(){var f=!1;return function(){if(f)return!0;f=!0;return!1}}(),e=function(){d()||window.setTimeout(function(){var f=c.getIframeEl().src;var h=f.indexOf("#");h!=-1&&(f=f.substring(0,h));f=window.performance.getEntriesByName(f);f.length<1?f=null:(f=f[0],f=f.responseStart==0?null:f);if(f){h=Math.round(f.requestStart);var k=Math.round(f.responseStart),l=Math.round(f.responseEnd);
Zp("wrt0",a,b,Math.round(f.startTime));Zp("wrt1",a,b,h);Zp("wrt2",a,b,k);Zp("wrt3",a,b,l)}},1E3)};c.register(Oq(c),e,Qq);c.register(Pq(c),e,Qq)}};_.r("gapi.widget.make",_.Yq);
_.jg=_.jg||{};_.jg.yv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};_.jg.WA=function(a){var b,c,d={};for(b=0;c=a[b];++b)d[c]=c;return d};
_.jg=_.jg||{};
(function(){function a(c,d){return String.fromCharCode(d)}var b={0:!1,10:!0,13:!0,34:!0,39:!0,60:!0,62:!0,92:!0,8232:!0,8233:!0,65282:!0,65287:!0,65308:!0,65310:!0,65340:!0};_.jg.escape=function(c,d){if(c){if(typeof c==="string")return _.jg.EF(c);if(typeof c==="Array"){var e=0;for(d=c.length;e<d;++e)c[e]=_.jg.escape(c[e])}else if(typeof c==="object"&&d){d={};for(e in c)c.hasOwnProperty(e)&&(d[_.jg.EF(e)]=_.jg.escape(c[e],!0));return d}}return c};_.jg.EF=function(c){if(!c)return c;for(var d=[],e,f,
h=0,k=c.length;h<k;++h)e=c.charCodeAt(h),f=b[e],f===!0?d.push("&#",e,";"):f!==!1&&d.push(c.charAt(h));return d.join("")};_.jg.jsa=function(c){return c?c.replace(/&#([0-9]+);/g,a):c}})();
_.Pa.Pa={};_.Pa.Pa.Ti={};_.Pa.Pa.Ti.m6=function(a){try{return!!a.document}catch(b){}return!1};_.Pa.Pa.Ti.lT=function(a){var b=a.parent;return a!=b&&_.Pa.Pa.Ti.m6(b)?_.Pa.Pa.Ti.lT(b):a};_.Pa.Pa.Ti.iqa=function(a){var b=a.userAgent||"";a=a.product||"";return b.indexOf("Opera")!=0&&b.indexOf("WebKit")==-1&&a=="Gecko"&&b.indexOf("rv:1.")>0};
_.Pa.Pa.Ti.yv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};
var fr,gr,hr,ir,lr,mr,nr,or,pr,qr,rr,sr,tr;fr=function(){_.dh.register("_noop_echo",function(){this.callback(_.Pa.f9(_.Pa.Em[this.f]))})};gr=function(){window.setTimeout(function(){_.dh.call("..","_noop_echo",_.Pa.Oca)},0)};hr=function(a,b,c){var d=function(e){var f=Array.prototype.slice.call(arguments,0),h=f[f.length-1];if(typeof h==="function"){var k=h;f.pop()}f.unshift(b,a,k,c);_.dh.call.apply(_.dh,f)};d._iframe_wrapped_rpc_=!0;return d};
ir=function(a){_.Pa.Mj[a]||(_.Pa.Mj[a]={},_.dh.register(a,function(b,c){var d=this.f;if(!(typeof b!="string"||b in{}||d in{})){var e=this.callback,f=_.Pa.Mj[a][d],h;f&&Object.hasOwnProperty.call(f,b)?h=f[b]:Object.hasOwnProperty.call(_.Pa.Xq,a)&&(h=_.Pa.Xq[a]);if(h)return d=Array.prototype.slice.call(arguments,1),h._iframe_wrapped_rpc_&&e&&d.push(e),h.apply({},d)}_.Zg.error(['Unregistered call in window "',window.name,'" for method "',a,'", via proxyId "',b,'" from frame "',d,'".'].join(""));return null}));
return _.Pa.Mj[a]};_.jr=function(){var a={};var b=window.location.href;var c=b.indexOf("?"),d=b.indexOf("#");b=(d===-1?b.substr(c+1):[b.substr(c+1,d-c-1),"&",b.substr(d+1)].join("")).split("&");c=window.decodeURIComponent?decodeURIComponent:unescape;d=0;for(var e=b.length;d<e;++d){var f=b[d].indexOf("=");if(f!==-1){var h=b[d].substring(0,f);f=b[d].substring(f+1);f=f.replace(/\+/g," ");try{a[h]=c(f)}catch(k){}}}return a};_.kr=function(){return _.rf.location.origin||_.rf.location.protocol+"//"+_.rf.location.host};
lr=function(a){_.If.h=a};mr=function(a){_.If.bsh=a};nr=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};or=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
pr=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!or(a[d])&&!or(b[d])?pr(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=or(b[d])?[]:{},pr(a[d],b[d])):a[d]=b[d])};
qr=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
rr=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};
sr=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=nr("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&qr())if(e=rr(c),d.push(25),typeof e===
"object")return e;return{}}};tr=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());pr(c,b);a.push(c)};
_.ur=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;_.Yi(!0);d=window.___gcfg;b=nr("cu");a=window.___gu;d&&d!==a&&(tr(b,d),window.___gu=d);d=nr("cu");e=document.getElementsByTagName("script")||[];a=[];f=[];f.push.apply(f,nr("us"));for(h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&a.push(k);a.length==0&&e.length>0&&e[e.length-1].src&&a.push(e[e.length-1]);for(e=0;e<
a.length;++e)a[e].getAttribute("gapi_processed")||(a[e].setAttribute("gapi_processed",!0),(f=a[e])?(h=f.nodeType,f=h==3||h==4?f.nodeValue:f.textContent||""):f=void 0,h=a[e].nonce||a[e].getAttribute("nonce"),(f=sr(f,h))&&d.push(f));c&&tr(b,c);a=nr("cd");c=0;for(d=a.length;c<d;++c)pr(_.Yi(),a[c],!0);a=nr("ci");c=0;for(d=a.length;c<d;++c)pr(_.Yi(),a[c],!0);c=0;for(d=b.length;c<d;++c)pr(_.Yi(),b[c],!0)};var vr,wr=window.location.href,xr=wr.indexOf("?"),yr=wr.indexOf("#");
vr=(yr===-1?wr.substr(xr+1):[wr.substr(xr+1,yr-xr-1),"&",wr.substr(yr+1)].join("")).split("&");for(var zr=window.decodeURIComponent?decodeURIComponent:unescape,Ar=0,Br=vr.length;Ar<Br;++Ar){var Cr=vr[Ar].indexOf("=");if(Cr!==-1){vr[Ar].substring(0,Cr);var Dr=vr[Ar].substring(Cr+1);Dr=Dr.replace(/\+/g," ");try{zr(Dr)}catch(a){}}};if(window.ToolbarApi)Er=window.ToolbarApi,Er.Oa=window.ToolbarApi.getInstance,Er.prototype=window.ToolbarApi.prototype,_.g=Er.prototype,_.g.openWindow=Er.prototype.openWindow,_.g.NP=Er.prototype.closeWindow,_.g.CZ=Er.prototype.setOnCloseHandler,_.g.xP=Er.prototype.canClosePopup,_.g.AY=Er.prototype.resizeWindow;else{var Er=function(){};Er.Oa=function(){!Fr&&window.external&&window.external.GTB_IsToolbar&&(Fr=new Er);return Fr};_.g=Er.prototype;_.g.openWindow=function(a){return window.external.GTB_OpenPopup&&
window.external.GTB_OpenPopup(a)};_.g.NP=function(a){window.external.GTB_ClosePopupWindow&&window.external.GTB_ClosePopupWindow(a)};_.g.CZ=function(a,b){window.external.GTB_SetOnCloseHandler&&window.external.GTB_SetOnCloseHandler(a,b)};_.g.xP=function(a){return window.external.GTB_CanClosePopup&&window.external.GTB_CanClosePopup(a)};_.g.AY=function(a,b){return window.external.GTB_ResizeWindow&&window.external.GTB_ResizeWindow(a,b)};var Fr=null;window.ToolbarApi=Er;window.ToolbarApi.getInstance=Er.Oa};var Gr=/^[-_.0-9A-Za-z]+$/,Hr={open:"open",onready:"ready",close:"close",onresize:"resize",onOpen:"open",onReady:"ready",onClose:"close",onResize:"resize",onRenderStart:"renderstart"},Ir={onBeforeParentOpen:"beforeparentopen"},Jr={onOpen:function(a){var b=a.Ac();a.wh(b.container||b.element);return a},onClose:function(a){a.remove()}},Kr=function(){_.Pa.wU++;return["I",_.Pa.wU,"_",(new Date).getTime()].join("")},Lr,Mr,Nr,Qr,Rr,Sr,Tr,Wr,Vr;_.Pa.wo=function(a){var b=_.wf();_.Af(_.Qm,b);_.Af(a,b);return b};
Lr=function(a){return a instanceof Array?a.join(","):a instanceof Object?_.Vg(a):a};Mr=function(a){var b=_.Zi("googleapis.config/elog");if(b)try{b(a)}catch(c){}};Nr=function(a){a&&a.match(Gr)&&_.ur("googleapis.config/gcv",a)};_.Or=function(a,b){b=b||{};for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};
_.Pr=function(a,b,c,d,e){var f=[],h;for(h in a)if(a.hasOwnProperty(h)){var k=b,l=c,m=a[h],n=d,p=ir(h);p[k]=p[k]||{};n=_.Pa.Pa.Ti.yv(n,m);m._iframe_wrapped_rpc_&&(n._iframe_wrapped_rpc_=!0);p[k][l]=n;f.push(h)}if(e)for(var q in _.Pa.Xq)_.Pa.Xq.hasOwnProperty(q)&&f.push(q);return f.join(",")};Qr=function(a,b,c){var d={};if(a&&a._methods){a=a._methods.split(",");for(var e=0;e<a.length;e++){var f=a[e];d[f]=hr(f,b,c)}}return d};
Rr=function(a){if(a&&a.disableMultiLevelParentRelay)a=!1;else{var b;if(b=_.dp&&_.dp._open&&a.style!="inline"&&a.inline!==!0)a=a.container,b=!(a&&(typeof a=="string"&&document.getElementById(a)||document==(a.ownerDocument||a.document)));a=b}return a};Sr=function(a,b){var c={};b=b.params||{};for(var d in a)d.charAt(0)=="#"&&(c[d.substring(1)]=a[d]),d.indexOf("fr-")==0&&(c[d.substring(3)]=a[d]),b[d]=="#"&&(c[d]=a[d]);for(var e in c)delete a["fr-"+e],delete a["#"+e],delete a[e];return c};
Tr=function(a){if(a.charAt(0)==":"){a="iframes/"+a.substring(1);var b=_.Zi(a);a={};_.Af(b,a);(b=a.url)&&(a.url=_.bn(b));a.params||(a.params={});return a}return{url:_.bn(a)}};Wr=function(a){function b(){}b.prototype=Vr.prototype;a.prototype=new b};
Vr=function(a,b,c,d,e,f,h,k){this.config=Tr(a);this.openParams=this.sB=b||{};this.params=c||{};this.methods=d;this.gD=!1;Xr(this,b.style);this.callbacks={};Yr(this,function(){var l;(l=this.sB.style)&&_.Pa.Gw[l]?l=_.Pa.Gw[l]:l?(_.Zg.warn(['Missing handler for style "',l,'". Continuing with default handler.'].join("")),l=null):l=Jr;if(l){if(typeof l==="function")var m=l(this);else{var n={};for(m in l){var p=l[m];n[m]=typeof p==="function"?_.Pa.Pa.Ti.yv(l,p,this):p}m=n}for(var q in e)l=m[q],typeof l===
"function"&&Zr(this,e[q],_.Pa.Pa.Ti.yv(m,l))}f&&Zr(this,"close",f)});this.Uk=this.ac=h;this.oJ=(k||[]).slice();h&&this.oJ.unshift(h.getId())};Vr.prototype.Ac=function(){return this.sB};Vr.prototype.DG=function(){return this.params};Vr.prototype.Fz=function(){return this.methods};Vr.prototype.xd=function(){return this.Uk};
var Xr=function(a,b){a.gD||((b=b&&!_.Pa.Gw[b]&&_.Pa.nF[b])?(a.mF=[],b(function(){a.gD=!0;for(var c=a.mF.length,d=0;d<c;++d)a.mF[d].call(a)})):a.gD=!0)},Yr=function(a,b){a.gD?b.call(a):a.mF.push(b)};Vr.prototype.Za=function(a,b){Yr(this,function(){Zr(this,a,b)})};var Zr=function(a,b,c){a.callbacks[b]=a.callbacks[b]||[];a.callbacks[b].push(c)};Vr.prototype.Bp=function(a,b){Yr(this,function(){var c=this.callbacks[a];if(c)for(var d=c.length,e=0;e<d;++e)if(c[e]===b){c.splice(e,1);break}})};
Vr.prototype.ti=function(a,b){var c=this.callbacks[a];if(c)for(var d=Array.prototype.slice.call(arguments,1),e=c.length,f=0;f<e;++f)try{var h=c[f].apply({},d)}catch(k){_.Zg.error(['Exception when calling callback "',a,'" with exception "',k.name,": ",k.message,'".'].join("")),Mr(k)}return h};var $r=function(a){return typeof a=="number"?{value:a,YF:a+"px"}:a=="100%"?{value:100,YF:"100%",gV:!0}:null};Vr.prototype.send=function(a,b,c){_.Pa.YY(this,a,b,c)};
Vr.prototype.register=function(a,b){var c=this;c.Za(a,function(d){b.call(c,d)})};var as=function(a,b,c,d,e,f,h){var k=this;Vr.call(this,a,b,c,d,Hr,e,f,h);this.id=b.id||Kr();this.cw=b.rpctoken&&String(b.rpctoken)||Math.round(_.Aj()*1E9);this.Z$=Sr(this.params,this.config);this.LF={};Yr(this,function(){k.ti("open");_.Or(k.LF,k)})};Wr(as);_.g=as.prototype;
_.g.wh=function(a,b){if(!this.config.url)return _.Zg.error("Cannot open iframe, empty URL."),this;var c=this.id;_.Pa.Em[c]=this;var d=_.Or(this.methods);d._ready=this.rB;d._close=this.close;d._open=this.dX;d._resizeMe=this.BY;d._renderstart=this.WW;var e=this.Z$;this.cw&&(e.rpctoken=this.cw);e._methods=_.Pr(d,c,"",this,!0);this.el=a=typeof a==="string"?document.getElementById(a):a;d={id:c};if(b){d.attributes=b;var f=b.style;if(typeof f==="string"){if(f){var h=[];f=f.split(";");for(var k=f.length,
l=0;l<k;++l){var m=f[l];if(m.length!=0||l+1!=k)m=m.split(":"),m.length==2&&m[0].match(/^[ a-zA-Z_-]+$/)&&m[1].match(/^[ +.%0-9a-zA-Z_-]+$/)?h.push(m.join(":")):_.Zg.error(['Iframe style "',f[l],'" not allowed.'].join(""))}h=h.join(";")}else h="";b.style=h}}this.Ac().allowPost&&(d.allowPost=!0);this.Ac().forcePost&&(d.forcePost=!0);d.queryParams=this.params;d.fragmentParams=e;d.paramsSerializer=Lr;this.vi=_.dn(this.config.url,a,d);a=this.vi.getAttribute("data-postorigin")||this.vi.src;_.Pa.Em[c]=this;
_.dh.zC(this.id,this.cw);_.dh.Yj(this.id,a);return this};_.g.Yh=function(a,b){this.LF[a]=b};_.g.getId=function(){return this.id};_.g.getIframeEl=function(){return this.vi};_.g.getSiteEl=function(){return this.el};_.g.setSiteEl=function(a){this.el=a};_.g.rB=function(a){var b=Qr(a,this.id,"");this.Uk&&typeof this.methods._ready=="function"&&(a._methods=_.Pr(b,this.Uk.getId(),this.id,this,!1),this.methods._ready(a));_.Or(a,this);_.Or(b,this);this.ti("ready",a)};
_.g.WW=function(a){this.ti("renderstart",a)};_.g.close=function(a){a=this.ti("close",a);delete _.Pa.Em[this.id];return a};_.g.remove=function(){var a=document.getElementById(this.id);a&&a.parentNode&&a.parentNode.removeChild(a)};
_.g.dX=function(a){var b=Qr(a.params,this.id,a.proxyId);delete a.params._methods;a.openParams.anchor=="_parent"&&(a.openParams.anchor=this.el);if(Rr(a.openParams))new bs(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain);else{var c=new as(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain),d=this;Yr(c,function(){var e={childId:c.getId()},f=c.LF;f._toclose=c.close;e._methods=_.Pr(f,d.id,c.id,c,!1);b._onopen(e)})}};
_.g.BY=function(a){if(this.ti("resize",a)===void 0&&this.vi){var b=$r(a.width);b!=null&&(this.vi.style.width=b.YF);a=$r(a.height);a!=null&&(this.vi.style.height=a.YF);this.vi.parentElement&&(b!=null&&b.gV||a!=null&&a.gV)&&(this.vi.parentElement.style.display="block")}};
var bs=function(a,b,c,d,e,f,h){var k=this;Vr.call(this,a,b,c,d,Ir,e,f,h);this.url=a;this.Wp=null;this.JJ=Kr();Yr(this,function(){k.ti("beforeparentopen");var l=_.Or(k.methods);l._onopen=k.Eca;l._ready=k.rB;l._onclose=k.Cca;k.params._methods=_.Pr(l,"..",k.JJ,k,!0);l={};for(var m in k.params)l[m]=Lr(k.params[m]);_.dp._open({url:k.config.url,openParams:k.sB,params:l,proxyId:k.JJ,openedByProxyChain:k.oJ})})};Wr(bs);bs.prototype.s9=function(){return this.Wp};
bs.prototype.Eca=function(a){this.Wp=a.childId;var b=Qr(a,"..",this.Wp);_.Or(b,this);this.close=b._toclose;_.Pa.Em[this.Wp]=this;this.Uk&&this.methods._onopen&&(a._methods=_.Pr(b,this.Uk.getId(),this.Wp,this,!1),this.methods._onopen(a))};bs.prototype.rB=function(a){var b=String(this.Wp),c=Qr(a,"..",b);_.Or(a,this);_.Or(c,this);this.ti("ready",a);this.Uk&&this.methods._ready&&(a._methods=_.Pr(c,this.Uk.getId(),b,this,!1),this.methods._ready(a))};
bs.prototype.Cca=function(a){if(this.Uk&&this.methods._onclose)this.methods._onclose(a);else return a=this.ti("close",a),delete _.Pa.Em[this.Wp],a};
var cs=function(a,b,c,d,e,f,h){Vr.call(this,a,b,c,d,Ir,f,h);this.id=b.id||Kr();this.iga=e;d._close=this.close;this.onClosed=this.PW;this.V0=0;Yr(this,function(){this.ti("beforeparentopen");var k=_.Or(this.methods);this.params._methods=_.Pr(k,"..",this.JJ,this,!0);k={};k.queryParams=this.params;a=_.Vm(_.sf,this.config.url,this.id,k);var l=e.openWindow(a);this.canAutoClose=function(m){m(e.xP(l))};e.CZ(l,this);this.V0=l})};Wr(cs);
cs.prototype.close=function(a){a=this.ti("close",a);this.iga.NP(this.V0);return a};cs.prototype.PW=function(){this.ti("close")};_.dp.send=function(a,b,c){_.Pa.YY(_.dp,a,b,c)};
(function(){function a(h){return _.Pa.Gw[h]}function b(h,k){_.Pa.Gw[h]=k}function c(h){h=h||{};h.height==="auto"&&(h.height=_.fn());var k=window&&Er&&Er.Oa();k?k.AY(h.width||0,h.height||0):_.dp&&_.dp._resizeMe&&_.dp._resizeMe(h)}function d(h){Nr(h)}_.Pa.Em={};_.Pa.Gw={};_.Pa.nF={};_.Pa.wU=0;_.Pa.Mj={};_.Pa.Xq={};_.Pa.CB=null;_.Pa.BB=[];_.Pa.Oca=function(h){var k=!1;try{if(h!=null){var l=window.parent.frames[h.id];k=l.iframer.id==h.id&&l.iframes.openedId_(_.dp.id)}}catch(m){}try{_.Pa.CB={origin:this.origin,
referer:this.referer,claimedOpenerId:h&&h.id,claimedOpenerProxyChain:h&&h.proxyChain||[],sameOrigin:k};for(h=0;h<_.Pa.BB.length;++h)_.Pa.BB[h](_.Pa.CB);_.Pa.BB=[]}catch(m){Mr(m)}};_.Pa.f9=function(h){var k=h&&h.Uk,l=null;k&&(l={},l.id=k.getId(),l.proxyChain=h.oJ);return l};fr();if(window.parent!=window){var e=_.jr();e.gcv&&Nr(e.gcv);var f=e.jsh;f&&lr(f);_.Or(Qr(e,"..",""),_.dp);_.Or(e,_.dp);gr()}_.Pa.Bb=a;_.Pa.Lc=b;_.Pa.Zea=d;_.Pa.resize=c;_.Pa.x8=function(h){return _.Pa.nF[h]};_.Pa.KK=function(h,
k){_.Pa.nF[h]=k};_.Pa.zY=c;_.Pa.ufa=d;_.Pa.dA={};_.Pa.dA.get=a;_.Pa.dA.set=b;_.Pa.allow=function(h,k){ir(h);_.Pa.Xq[h]=k||window[h]};_.Pa.lpa=function(h){delete _.Pa.Xq[h]};_.Pa.open=function(h,k,l,m,n,p){arguments.length==3?m={}:arguments.length==4&&typeof m==="function"&&(n=m,m={});var q=k.style==="bubble"&&Er?Er.Oa():null;return q?new cs(h,k,l,m,q,n,p):Rr(k)?new bs(h,k,l,m,n,p):new as(h,k,l,m,n,p)};_.Pa.close=function(h,k){_.dp&&_.dp._close&&_.dp._close(h,k)};_.Pa.ready=function(h,k,l){arguments.length==
2&&typeof k==="function"&&(l=k,k={});var m=h||{};"height"in m||(m.height=_.fn());m._methods=_.Pr(k||{},"..","",_.dp,!0);_.dp&&_.dp._ready&&_.dp._ready(m,l)};_.Pa.WS=function(h){_.Pa.CB?h(_.Pa.CB):_.Pa.BB.push(h)};_.Pa.Gca=function(h){return!!_.Pa.Em[h]};_.Pa.I8=function(){return["https://ssl.gstatic.com/gb/js/",_.Zi("googleapis.config/gcv")].join("")};_.Pa.YX=function(h){var k={mouseover:1,mouseout:1};if(_.dp._event)for(var l=0;l<h.length;l++){var m=h[l];m in k&&document.addEventListener(m,function(n){_.dp._event({event:n.type,
timestamp:(new Date).getTime()})},!0)}};_.Pa.YY=function(h,k,l,m){var n=this,p=[];l!==void 0&&p.push(l);m&&p.push(function(q){m.call(n,[q])});h[k]&&h[k].apply(h,p)};_.Pa.CROSS_ORIGIN_IFRAMES_FILTER=function(){return!0};_.Pa.l6=function(h,k,l){var m=Array.prototype.slice.call(arguments);_.Pa.WS(function(n){n.sameOrigin&&(m.unshift("/"+n.claimedOpenerId+"|"+window.location.protocol+"//"+window.location.host),_.dh.call.apply(_.dh,m))})};_.Pa.sda=function(h,k){_.dh.register(h,k)};_.Pa.ffa=lr;_.Pa.gZ=
mr;_.Pa.TV=Mr;_.Pa.xU=_.dp})();_.r("iframes.allow",_.Pa.allow);_.r("iframes.callSiblingOpener",_.Pa.l6);_.r("iframes.registerForOpenedSibling",_.Pa.sda);_.r("iframes.close",_.Pa.close);_.r("iframes.getGoogleConnectJsUri",_.Pa.I8);_.r("iframes.getHandler",_.Pa.Bb);_.r("iframes.getDeferredHandler",_.Pa.x8);_.r("iframes.getParentInfo",_.Pa.WS);_.r("iframes.iframer",_.Pa.xU);_.r("iframes.open",_.Pa.open);_.r("iframes.openedId_",_.Pa.Gca);_.r("iframes.propagate",_.Pa.YX);_.r("iframes.ready",_.Pa.ready);_.r("iframes.resize",_.Pa.resize);
_.r("iframes.setGoogleConnectJsVersion",_.Pa.Zea);_.r("iframes.setBootstrapHint",_.Pa.gZ);_.r("iframes.setJsHint",_.Pa.ffa);_.r("iframes.setHandler",_.Pa.Lc);_.r("iframes.setDeferredHandler",_.Pa.KK);_.r("IframeBase",Vr);_.r("IframeBase.prototype.addCallback",Vr.prototype.Za);_.r("IframeBase.prototype.getMethods",Vr.prototype.Fz);_.r("IframeBase.prototype.getOpenerIframe",Vr.prototype.xd);_.r("IframeBase.prototype.getOpenParams",Vr.prototype.Ac);_.r("IframeBase.prototype.getParams",Vr.prototype.DG);
_.r("IframeBase.prototype.removeCallback",Vr.prototype.Bp);_.r("Iframe",as);_.r("Iframe.prototype.close",as.prototype.close);_.r("Iframe.prototype.exposeMethod",as.prototype.Yh);_.r("Iframe.prototype.getId",as.prototype.getId);_.r("Iframe.prototype.getIframeEl",as.prototype.getIframeEl);_.r("Iframe.prototype.getSiteEl",as.prototype.getSiteEl);_.r("Iframe.prototype.openInto",as.prototype.wh);_.r("Iframe.prototype.remove",as.prototype.remove);_.r("Iframe.prototype.setSiteEl",as.prototype.setSiteEl);
_.r("Iframe.prototype.addCallback",as.prototype.Za);_.r("Iframe.prototype.getMethods",as.prototype.Fz);_.r("Iframe.prototype.getOpenerIframe",as.prototype.xd);_.r("Iframe.prototype.getOpenParams",as.prototype.Ac);_.r("Iframe.prototype.getParams",as.prototype.DG);_.r("Iframe.prototype.removeCallback",as.prototype.Bp);_.r("IframeProxy",bs);_.r("IframeProxy.prototype.getTargetIframeId",bs.prototype.s9);_.r("IframeProxy.prototype.addCallback",bs.prototype.Za);_.r("IframeProxy.prototype.getMethods",bs.prototype.Fz);
_.r("IframeProxy.prototype.getOpenerIframe",bs.prototype.xd);_.r("IframeProxy.prototype.getOpenParams",bs.prototype.Ac);_.r("IframeProxy.prototype.getParams",bs.prototype.DG);_.r("IframeProxy.prototype.removeCallback",bs.prototype.Bp);_.r("IframeWindow",cs);_.r("IframeWindow.prototype.close",cs.prototype.close);_.r("IframeWindow.prototype.onClosed",cs.prototype.PW);_.r("iframes.util.getTopMostAccessibleWindow",_.Pa.Pa.Ti.lT);_.r("iframes.handlers.get",_.Pa.dA.get);_.r("iframes.handlers.set",_.Pa.dA.set);
_.r("iframes.resizeMe",_.Pa.zY);_.r("iframes.setVersionOverride",_.Pa.ufa);_.r("iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Pa.CROSS_ORIGIN_IFRAMES_FILTER);_.r("IframeBase.prototype.send",Vr.prototype.send);_.r("IframeBase.prototype.register",Vr.prototype.register);_.r("Iframe.prototype.send",as.prototype.send);_.r("Iframe.prototype.register",as.prototype.register);_.r("IframeProxy.prototype.send",bs.prototype.send);_.r("IframeProxy.prototype.register",bs.prototype.register);
_.r("IframeWindow.prototype.send",cs.prototype.send);_.r("IframeWindow.prototype.register",cs.prototype.register);_.r("iframes.iframer.send",_.Pa.xU.send);
var eu=_.Pa.Lc,fu={open:function(a){var b=_.Wo(a.Ac());return a.wh(b,{style:_.Xo(b)})},attach:function(a,b){var c=_.Wo(a.Ac()),d=b.id,e=b.getAttribute("data-postorigin")||b.src,f=/#(?:.*&)?rpctoken=(\d+)/.exec(e);f=f&&f[1];a.id=d;a.cw=f;a.el=c;a.vi=b;_.Pa.Em[d]=a;b=_.Or(a.methods);b._ready=a.rB;b._close=a.close;b._open=a.dX;b._resizeMe=a.BY;b._renderstart=a.WW;_.Pr(b,d,"",a,!0);_.dh.zC(a.id,a.cw);_.dh.Yj(a.id,e);c=_.Pa.wo({style:_.Xo(c)});for(var h in c)Object.prototype.hasOwnProperty.call(c,h)&&
(h=="style"?a.vi.style.cssText=c[h]:a.vi.setAttribute(h,c[h]))}};fu.onready=_.Yo;fu.onRenderStart=_.Yo;fu.close=_.Zo;eu("inline",fu);
_.Di=function(a){for(var b in a)return!1;return!0};_.Ei=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Fi,Gi,Ii;Fi={};Gi=null;_.Hi=_.ke||_.le||!_.Ci&&typeof _.Sa.atob=="function";_.Ji=function(a,b){b===void 0&&(b=0);Ii();b=Fi[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Ki=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Gi[m];if(n!=null)return n;if(!_.gd(m))throw Error("E`"+m);}return l}Ii();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Ii=function(){if(!Gi){Gi={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Fi[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Gi[f]===void 0&&(Gi[f]=e)}}}};
var dj;_.cj=function(a){this.Rb=a||{cookie:""}};_.g=_.cj.prototype;_.g.isEnabled=function(){if(!_.Sa.navigator.cookieEnabled)return!1;if(!this.isEmpty())return!0;this.set("TESTCOOKIESENABLED","1",{AI:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
_.g.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.vra;d=c.secure||!1;var f=c.domain||void 0;var h=c.path||void 0;var k=c.AI}if(/[;=\s]/.test(a))throw Error("H`"+a);if(/[;\r\n]/.test(b))throw Error("I`"+b);k===void 0&&(k=-1);this.Rb.cookie=a+"="+b+(f?";domain="+f:"")+(h?";path="+h:"")+(k<0?"":k==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+k*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};
_.g.get=function(a,b){for(var c=a+"=",d=(this.Rb.cookie||"").split(";"),e=0,f;e<d.length;e++){f=(0,_.hd)(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};_.g.remove=function(a,b,c){var d=this.Yi(a);this.set(a,"",{AI:0,path:b,domain:c});return d};_.g.Cg=function(){return dj(this).keys};_.g.Hd=function(){return dj(this).values};_.g.isEmpty=function(){return!this.Rb.cookie};_.g.Sb=function(){return this.Rb.cookie?(this.Rb.cookie||"").split(";").length:0};
_.g.Yi=function(a){return this.get(a)!==void 0};_.g.uk=function(a){for(var b=dj(this).values,c=0;c<b.length;c++)if(b[c]==a)return!0;return!1};_.g.clear=function(){for(var a=dj(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};dj=function(a){a=(a.Rb.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=(0,_.hd)(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};
_.ej=new _.cj(typeof document=="undefined"?null:document);
_.sj={};_.tj=function(a){return _.sj[a||"token"]||null};
_.Mj=function(a){a&&typeof a.dispose=="function"&&a.dispose()};_.Oj=function(){this.eh=this.eh;this.jp=this.jp};_.Oj.prototype.eh=!1;_.Oj.prototype.isDisposed=function(){return this.eh};_.Oj.prototype.dispose=function(){this.eh||(this.eh=!0,this.va())};_.Oj.prototype[Symbol.dispose]=function(){this.dispose()};_.Qj=function(a,b){_.Pj(a,_.$a(_.Mj,b))};_.Pj=function(a,b){a.eh?b():(a.jp||(a.jp=[]),a.jp.push(b))};_.Oj.prototype.va=function(){if(this.jp)for(;this.jp.length;)this.jp.shift()()};
var Vj=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.Ne.prototype.O=_.nb(1,function(a){return _.Qe(this.Rb,a)});_.Wj=function(a,b){var c=a.length-b.length;return c>=0&&a.indexOf(b,c)==c};_.Xj=function(a,b,c){return Object.prototype.hasOwnProperty.call(a,b)?a[b]:a[b]=c(b)};_.Yj=function(a){var b;if(_.le&&(b=a.parentElement))return b;b=a.parentNode;return _.jf(b)?b:null};_.ak=function(a,b){this.type="function"==typeof _.Zj&&a instanceof _.Zj?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.Sv=!1};_.ak.prototype.stopPropagation=function(){this.Sv=!0};_.ak.prototype.preventDefault=function(){this.defaultPrevented=!0};var bk=function(){if(!_.Sa.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.Sa.addEventListener("test",c,b);_.Sa.removeEventListener("test",c,b)}catch(d){}return a}();_.ck=function(a,b){_.ak.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.BJ=!1;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.Mf=null;a&&this.zd(a,b)};_.bb(_.ck,_.ak);var dk={2:"touch",3:"pen",4:"mouse"};
_.ck.prototype.zd=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;(b=a.relatedTarget)?_.ke&&(_.fe(b,"nodeName")||(b=null)):c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.le||a.offsetX!==
void 0?a.offsetX:a.layerX,this.offsetY=_.le||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.BJ=_.ne?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||
0;this.pointerType=typeof a.pointerType==="string"?a.pointerType:dk[a.pointerType]||"";this.state=a.state;this.timeStamp=a.timeStamp;this.Mf=a;a.defaultPrevented&&_.ck.N.preventDefault.call(this)};_.ck.prototype.stopPropagation=function(){_.ck.N.stopPropagation.call(this);this.Mf.stopPropagation?this.Mf.stopPropagation():this.Mf.cancelBubble=!0};_.ck.prototype.preventDefault=function(){_.ck.N.preventDefault.call(this);var a=this.Mf;a.preventDefault?a.preventDefault():a.returnValue=!1};_.ek="closure_listenable_"+(Math.random()*1E6|0);_.fk=function(a){return!(!a||!a[_.ek])};var gk=0;var hk=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Rf=e;this.key=++gk;this.Xv=this.hy=!1},ik=function(a){a.Xv=!0;a.listener=null;a.proxy=null;a.src=null;a.Rf=null};var jk=function(a){this.src=a;this.ze={};this.bx=0};jk.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.ze[f];a||(a=this.ze[f]=[],this.bx++);var h=kk(a,b,d,e);h>-1?(b=a[h],c||(b.hy=!1)):(b=new hk(b,this.src,f,!!d,e),b.hy=c,a.push(b));return b};jk.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.ze))return!1;var e=this.ze[a];b=kk(e,b,c,d);return b>-1?(ik(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.ze[a],this.bx--),!0):!1};
var lk=function(a,b){var c=b.type;if(!(c in a.ze))return!1;var d=_.ub(a.ze[c],b);d&&(ik(b),a.ze[c].length==0&&(delete a.ze[c],a.bx--));return d};jk.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.ze)if(!a||c==a){for(var d=this.ze[c],e=0;e<d.length;e++)++b,ik(d[e]);delete this.ze[c];this.bx--}return b};jk.prototype.Qq=function(a,b,c,d){a=this.ze[a.toString()];var e=-1;a&&(e=kk(a,b,c,d));return e>-1?a[e]:null};
jk.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return Vj(this.ze,function(f){for(var h=0;h<f.length;++h)if(!(c&&f[h].type!=d||e&&f[h].capture!=b))return!0;return!1})};var kk=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.Xv&&f.listener==b&&f.capture==!!c&&f.Rf==d)return e}return-1};var mk,nk,ok,sk,uk,vk,wk,zk;mk="closure_lm_"+(Math.random()*1E6|0);nk={};ok=0;_.qk=function(a,b,c,d,e){if(d&&d.once)return _.pk(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.qk(a,b[f],c,d,e);return null}c=_.rk(c);return _.fk(a)?a.qa(b,c,_.yc(d)?!!d.capture:!!d,e):sk(a,b,c,!1,d,e)};
sk=function(a,b,c,d,e,f){if(!b)throw Error("J");var h=_.yc(e)?!!e.capture:!!e,k=_.tk(a);k||(a[mk]=k=new jk(a));c=k.add(b,c,d,h,f);if(c.proxy)return c;d=uk();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)bk||(e=h),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(vk(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("K");ok++;return c};uk=function(){var a=wk,b=function(c){return a.call(b.src,b.listener,c)};return b};
_.pk=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.pk(a,b[f],c,d,e);return null}c=_.rk(c);return _.fk(a)?a.Dr(b,c,_.yc(d)?!!d.capture:!!d,e):sk(a,b,c,!0,d,e)};_.xk=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.xk(a,b[f],c,d,e);else d=_.yc(d)?!!d.capture:!!d,c=_.rk(c),_.fk(a)?a.Ec(b,c,d,e):a&&(a=_.tk(a))&&(b=a.Qq(b,c,d,e))&&_.yk(b)};
_.yk=function(a){if(typeof a==="number"||!a||a.Xv)return!1;var b=a.src;if(_.fk(b))return b.bN(a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(vk(c),d):b.addListener&&b.removeListener&&b.removeListener(d);ok--;(c=_.tk(b))?(lk(c,a),c.bx==0&&(c.src=null,b[mk]=null)):ik(a);return!0};vk=function(a){return a in nk?nk[a]:nk[a]="on"+a};
wk=function(a,b){if(a.Xv)a=!0;else{b=new _.ck(b,this);var c=a.listener,d=a.Rf||a.src;a.hy&&_.yk(a);a=c.call(d,b)}return a};_.tk=function(a){a=a[mk];return a instanceof jk?a:null};zk="__closure_events_fn_"+(Math.random()*1E9>>>0);_.rk=function(a){if(typeof a==="function")return a;a[zk]||(a[zk]=function(b){return a.handleEvent(b)});return a[zk]};_.Uj(function(a){wk=a(wk)});_.Ak=function(){_.Oj.call(this);this.Dk=new jk(this);this.C5=this;this.tJ=null};_.bb(_.Ak,_.Oj);_.Ak.prototype[_.ek]=!0;_.g=_.Ak.prototype;_.g.zo=function(){return this.tJ};_.g.MC=function(a){this.tJ=a};_.g.addEventListener=function(a,b,c,d){_.qk(this,a,b,c,d)};_.g.removeEventListener=function(a,b,c,d){_.xk(this,a,b,c,d)};
_.g.dispatchEvent=function(a){var b,c=this.zo();if(c)for(b=[];c;c=c.zo())b.push(c);c=this.C5;var d=a.type||a;if(typeof a==="string")a=new _.ak(a,c);else if(a instanceof _.ak)a.target=a.target||c;else{var e=a;a=new _.ak(d,c);_.Bb(a,e)}e=!0;if(b)for(var f=b.length-1;!a.Sv&&f>=0;f--){var h=a.currentTarget=b[f];e=h.ju(d,!0,a)&&e}a.Sv||(h=a.currentTarget=c,e=h.ju(d,!0,a)&&e,a.Sv||(e=h.ju(d,!1,a)&&e));if(b)for(f=0;!a.Sv&&f<b.length;f++)h=a.currentTarget=b[f],e=h.ju(d,!1,a)&&e;return e};
_.g.va=function(){_.Ak.N.va.call(this);this.UJ();this.tJ=null};_.g.qa=function(a,b,c,d){return this.Dk.add(String(a),b,!1,c,d)};_.g.Dr=function(a,b,c,d){return this.Dk.add(String(a),b,!0,c,d)};_.g.Ec=function(a,b,c,d){return this.Dk.remove(String(a),b,c,d)};_.g.bN=function(a){return lk(this.Dk,a)};_.g.UJ=function(){this.Dk&&this.Dk.removeAll(void 0)};
_.g.ju=function(a,b,c){a=this.Dk.ze[String(a)];if(!a)return!0;a=a.concat();for(var d=!0,e=0;e<a.length;++e){var f=a[e];if(f&&!f.Xv&&f.capture==b){var h=f.listener,k=f.Rf||f.src;f.hy&&this.bN(f);d=h.call(k,c)!==!1&&d}}return d&&!c.defaultPrevented};_.g.Qq=function(a,b,c,d){return this.Dk.Qq(String(a),b,c,d)};_.g.hasListener=function(a,b){return this.Dk.hasListener(a!==void 0?String(a):void 0,b)};
var ds;ds=function(){var a=_.Xb();if(_.fc())return _.oc(a);a=_.cc(a);var b=_.nc(a);return _.ec()?b(["Version","Opera"]):_.gc()?b(["Edge"]):_.hc()?b(["Edg"]):_.bc("Silk")?b(["Silk"]):_.kc()?b(["Chrome","CriOS","HeadlessChrome"]):(a=a[2])&&a[1]||""};_.es=function(a){return _.rd(ds(),a)>=0};_.gs=function(){return _.Yb&&_.Zb?_.Zb.mobile:!_.fs()&&(_.bc("iPod")||_.bc("iPhone")||_.bc("Android")||_.bc("IEMobile"))};
_.fs=function(){return _.Yb&&_.Zb?!_.Zb.mobile&&(_.bc("iPad")||_.bc("Android")||_.bc("Silk")):_.bc("iPad")||_.bc("Android")&&!_.bc("Mobile")||_.bc("Silk")};_.js=function(){return!_.gs()&&!_.fs()};
var tt;tt=function(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};_.ut=function(a,b,c,d){return Array.prototype.splice.apply(a,tt(arguments,1))};_.vt=function(a,b,c){if(a!==null&&b in a)throw Error("k`"+b);a[b]=c};_.wt=function(a,b){var c=b||document;if(c.getElementsByClassName)a=c.getElementsByClassName(a)[0];else{c=document;var d=b||c;a=d.querySelectorAll&&d.querySelector&&a?d.querySelector(a?"."+a:""):_.Re(c,"*",a,b)[0]||null}return a||null};
_.xt=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.zt=function(a,b,c){a&&!c&&(a=a.parentNode);for(c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null};_.At=function(a){_.Oj.call(this);this.Hg=a;this.kc={}};_.bb(_.At,_.Oj);var Bt=[];_.At.prototype.qa=function(a,b,c,d){return this.vv(a,b,c,d)};
_.At.prototype.vv=function(a,b,c,d,e){Array.isArray(b)||(b&&(Bt[0]=b.toString()),b=Bt);for(var f=0;f<b.length;f++){var h=_.qk(a,b[f],c||this.handleEvent,d||!1,e||this.Hg||this);if(!h)break;this.kc[h.key]=h}return this};_.At.prototype.Dr=function(a,b,c,d){return Ct(this,a,b,c,d)};var Ct=function(a,b,c,d,e,f){if(Array.isArray(c))for(var h=0;h<c.length;h++)Ct(a,b,c[h],d,e,f);else{b=_.pk(b,c,d||a.handleEvent,e,f||a.Hg||a);if(!b)return a;a.kc[b.key]=b}return a};
_.At.prototype.Ec=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.Ec(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.yc(d)?!!d.capture:!!d,e=e||this.Hg||this,c=_.rk(c),d=!!d,b=_.fk(a)?a.Qq(b,c,d,e):a?(a=_.tk(a))?a.Qq(b,c,d,e):null:null,b&&(_.yk(b),delete this.kc[b.key]);return this};_.At.prototype.removeAll=function(){_.wb(this.kc,function(a,b){this.kc.hasOwnProperty(b)&&_.yk(a)},this);this.kc={}};_.At.prototype.va=function(){_.At.N.va.call(this);this.removeAll()};
_.At.prototype.handleEvent=function(){throw Error("S");};
var dv,ev,fv,gv,hv,jv,kv,lv,mv,ov;_.bv=function(a,b){for(var c in a)if(!(c in b)||a[c]!==b[c])return!1;for(var d in b)if(!(d in a))return!1;return!0};_.cv=!1;dv=function(a){try{_.cv&&window.console&&window.console.log&&window.console.log(a)}catch(b){}};ev=function(a){try{window.console&&window.console.warn&&window.console.warn(a)}catch(b){}};fv=function(a,b){if(!a)return-1;if(a.indexOf)return a.indexOf(b,void 0);for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1};
gv=function(a,b){function c(){}if(!a)throw Error("V");if(!b)throw Error("W");c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};hv=function(a){return Object.prototype.toString.call(a)==="[object Function]"};_.iv=function(a){var b={};if(a)for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};jv=function(a){var b=location.hash;a=new RegExp("[&#]"+a+"=([^&]*)");b=decodeURIComponent(b);b=a.exec(b);return b==null?"":b[1].replace(/\+/g," ")};
kv=function(a,b,c){if(a.addEventListener)a.addEventListener(b,c,!1);else if(a.attachEvent)a.attachEvent("on"+b,c);else throw Error("X`"+b);};lv={token:1,id_token:1};mv=function(){var a=navigator.userAgent.toLowerCase();return a.indexOf("msie")!=-1&&parseInt(a.split("msie")[1],10)==8};_.nv=window.JSON;ov=function(a){this.oN=a||[];this.uc={}};
ov.prototype.addEventListener=function(a,b){if(!(fv(this.oN,a)>=0))throw Error("Z`"+a);if(!hv(b))throw Error("$`"+a);this.uc[a]||(this.uc[a]=[]);fv(this.uc[a],b)<0&&this.uc[a].push(b)};ov.prototype.removeEventListener=function(a,b){if(!(fv(this.oN,a)>=0))throw Error("Z`"+a);hv(b)&&this.uc[a]&&this.uc[a].length&&(b=fv(this.uc[a],b),b>=0&&this.uc[a].splice(b,1))};
ov.prototype.dispatchEvent=function(a){var b=a.type;if(!(b&&fv(this.oN,b)>=0))throw Error("aa`"+b);if(this.uc[b]&&this.uc[b].length)for(var c=this.uc[b].length,d=0;d<c;d++)this.uc[b][d](a)};var pv,qv,rv,tv,uv,yv,zv,Av,Rv,Sv,Uv,Vv,Xv,aw,bw,cw,gw;pv=_.pf(["https://accounts.google.com/gsi/ottoken"]);qv={};rv={};_.sv=function(){if(_.vc()&&!_.es("118"))return!1;var a=_.kc()&&!_.hc()&&!_.ic(),b=_.qc()||_.js(),c;if(c="IdentityCredential"in window){try{var d=window.self!==window.top}catch(e){d=!0}c=!d}return c&&a&&b&&(_.js()&&_.es("126")||_.qc()&&_.es("128"))};tv=function(){var a=_.mf(pv),b=document.createElement("script");_.of(b,a);document.head.append(b)};
uv={google:{fedcmConfigUrl:"https://accounts.google.com/o/fedcm/config.json",authServerUrl:"https://accounts.google.com/o/oauth2/auth",idpIFrameUrl:"https://accounts.google.com/o/oauth2/iframe"}};_.vv=function(a,b){if(a=uv[a])return a[b]};
_.wv=function(a,b){if(!a)throw Error("ba");if(!b.authServerUrl)throw Error("ca");if(!b.idpIFrameUrl)throw Error("da");uv[a]={authServerUrl:b.authServerUrl,idpIFrameUrl:b.idpIFrameUrl};b.fedcmConfigUrl?uv[a].fedcmConfigUrl=b.fedcmConfigUrl:a==="google"&&(uv[a].fedcmConfigUrl="https://accounts.google.com/o/fedcm/config.json")};_.xv=void 0;
yv=function(a){a.style.position="absolute";a.style.width="1px";a.style.height="1px";a.style.left="-9999px";a.style.top="-9999px";a.style.right="-9999px";a.style.bottom="-9999px";a.style.display="none";a.setAttribute("aria-hidden","true")};
zv=function(){var a=document.createElement("meta");a.httpEquiv="origin-trial";a.content="A3IiS3DuZmYjNGGyWyY6dfaTI4/4jBQNPrC2qWVxBZ5VbDEzh8JahwaD3mKQ0H5DPkFxQKSxIzl5WPEMuHOC+wAAAABseyJvcmlnaW4iOiJodHRwczovL2FwaXMuZ29vZ2xlLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmVkQ21CdXR0b25Nb2RlIiwiZXhwaXJ5IjoxNzI3ODI3MTk5LCJpc1RoaXJkUGFydHkiOnRydWV9";document.head.append(a);a=document.createElement("meta");a.httpEquiv="origin-trial";a.content="A4xxYntAsoZRYAFhtHK2hMqRbeVovVWfMGfYwwfKtQv6dUhUZWsN08vkn77E1/Ah9j/THaSAKOjDlL5AQw4cZwQAAAByeyJvcmlnaW4iOiJodHRwczovL2FwaXMuZ29vZ2xlLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmVkQ21Db250aW51ZU9uQnVuZGxlIiwiZXhwaXJ5IjoxNzMzMjcwMzk5LCJpc1RoaXJkUGFydHkiOnRydWV9";
document.head.append(a)};Av=function(){this.Ri=window;this.Ey=this.Qn=this.Pv=this.Fi=null};Av.prototype.open=function(a,b,c,d){Bv(this);this.Pv?(this.Qn&&(this.Qn(),this.Qn=null),Cv(this)):this.Pv="authPopup"+Math.floor(Math.random()*1E6+1);a:{this.Fi=this.Ri.open(a,this.Pv,b);try{this.Fi.focus();if(this.Fi.closed||typeof this.Fi.closed=="undefined")throw Error("fa");_.xv=this.Fi}catch(e){d&&setTimeout(d,0);this.Fi=null;break a}c&&(this.Qn=c,Dv(this))}};
var Bv=function(a){try{if(a.Fi==null||a.Fi.closed)a.Fi=null,a.Pv=null,Cv(a),a.Qn&&(a.Qn(),a.Qn=null)}catch(b){a.Fi=null,a.Pv=null,Cv(a)}},Dv=function(a){a.Ey=window.setInterval(function(){Bv(a)},300)},Cv=function(a){a.Ey&&(window.clearInterval(a.Ey),a.Ey=null)};rv=rv||{};var Ev=function(a,b){this.lc=a;this.NH=b;this.Pd=null;this.Ko=!1};Ev.prototype.start=function(){if(!this.Ko&&!this.Pd){var a=this;this.Pd=window.setTimeout(function(){a.clear();a.Ko||(a.lc(),a.Ko=!0)},rv.jT(this.NH))}};
Ev.prototype.clear=function(){this.Pd&&(window.clearTimeout(this.Pd),this.Pd=null)};var Fv=function(a,b){var c=rv.lt;this.aaa=rv.Zs;this.O0=c;this.lc=a;this.NH=b;this.Pd=null;this.Ko=!1;var d=this;this.P0=function(){document[d.aaa]||(d.clear(),d.start())}};Fv.prototype.start=function(){if(!this.Ko&&!this.Pd){kv(document,this.O0,this.P0);var a=this;this.Pd=window.setTimeout(function(){a.clear();a.Ko||(a.lc(),a.Ko=!0)},rv.jT(this.NH))}};
Fv.prototype.clear=function(){var a=this.O0,b=this.P0,c=document;if(c.removeEventListener)c.removeEventListener(a,b,!1);else if(c.detachEvent)c.detachEvent("on"+a,b);else throw Error("Y`"+a);this.Pd&&(window.clearTimeout(this.Pd),this.Pd=null)};rv.Zs=null;rv.lt=null;
rv.Daa=function(){var a=document;typeof a.hidden!=="undefined"?(rv.Zs="hidden",rv.lt="visibilitychange"):typeof a.msHidden!=="undefined"?(rv.Zs="msHidden",rv.lt="msvisibilitychange"):typeof a.webkitHidden!=="undefined"&&(rv.Zs="webkitHidden",rv.lt="webkitvisibilitychange")};rv.Daa();rv.U6=function(a,b){return rv.Zs&&rv.lt?new Fv(a,b):new Ev(a,b)};rv.jT=function(a){return Math.max(1,a-(new Date).getTime())};
var Gv=function(a,b){document.cookie="G_ENABLED_IDPS="+a+";domain=."+b+";expires=Fri, 31 Dec 9999 12:00:00 GMT;path=/"},Hv=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,t=0;t<64;t+=4)q[t/4]=p[t]<<24|p[t+1]<<16|p[t+2]<<8|p[t+3];for(t=16;t<80;t++)p=q[t-3]^q[t-8]^q[t-14]^q[t-16],q[t]=(p<<1|p>>>31)&4294967295;p=e[0];var v=e[1],u=e[2],w=e[3],y=e[4];for(t=0;t<80;t++){if(t<40)if(t<20){var D=w^v&(u^w);var C=1518500249}else D=
v^u^w,C=1859775393;else t<60?(D=v&u|w&(v|u),C=2400959708):(D=v^u^w,C=3395469782);D=((p<<5|p>>>27)&4294967295)+D+y+C+q[t]&4294967295;y=w;w=u;u=(v<<30|v>>>2)&4294967295;v=p;p=D}e[0]=e[0]+p&4294967295;e[1]=e[1]+v&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+w&4294967295;e[4]=e[4]+y&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var t=[],v=0,u=p.length;v<u;++v)t.push(p.charCodeAt(v));p=t}q||(q=p.length);t=0;if(m==0)for(;t+64<q;)b(p.slice(t,t+64)),t+=64,n+=64;for(;t<
q;)if(f[m++]=p[t++],n++,m==64)for(m=0,b(f);t+64<q;)b(p.slice(t,t+64)),t+=64,n+=64}function d(){var p=[],q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var t=63;t>=56;t--)f[t]=q&255,q>>>=8;b(f);for(t=q=0;t<5;t++)for(var v=24;v>=0;v-=8)p[q++]=e[t]>>v&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Zi:function(){for(var p=d(),q="",t=0;t<p.length;t++)q+="0123456789ABCDEF".charAt(Math.floor(p[t]/16))+"0123456789ABCDEF".charAt(p[t]%16);return q}}},Iv=
window.crypto,Jv=!1,Kv=0,Lv=1,Mv=0,Nv="",Ov=function(a){a=a||window.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Lv=Lv*b%Mv;if(++Kv==3)if(a=window,b=Ov,a.removeEventListener)a.removeEventListener("mousemove",b,!1);else if(a.detachEvent)a.detachEvent("onmousemove",b);else throw Error("Y`mousemove");},Pv=function(a){var b=Hv();b.update(a);return b.Zi()};Jv=!!Iv&&typeof Iv.getRandomValues=="function";
Jv||(Mv=(screen.width*screen.width+screen.height)*1E6,Nv=Pv(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+Math.random()),kv(window,"mousemove",Ov));qv=qv||{};qv.S2="ssIFrame_";
_.Qv=function(a,b,c){c=c===void 0?!1:c;this.Gb=a;if(!this.Gb)throw Error("ga");a=_.vv(a,"idpIFrameUrl");if(!a)throw Error("ha");this.uU=a;if(!b)throw Error("ia");this.nn=b;a=this.uU;b=document.createElement("a");b.setAttribute("href",a);a=[b.protocol,"//",b.hostname];b.protocol=="http:"&&b.port!=""&&b.port!="0"&&b.port!="80"?(a.push(":"),a.push(b.port)):b.protocol=="https:"&&b.port!=""&&b.port!="0"&&b.port!="443"&&(a.push(":"),a.push(b.port));this.xH=a.join("");this.eea=[location.protocol,"//",location.host].join("");
this.Pw=this.wH=this.Oo=!1;this.qU=null;this.qB=[];this.Wr=[];this.nk={};this.Po=void 0;this.An=c};_.g=_.Qv.prototype;_.g.show=function(){var a=this.Po;a.style.position="fixed";a.style.width="100%";a.style.height="100%";a.style.left="0px";a.style.top="0px";a.style.right="0px";a.style.bottom="0px";a.style.display="block";a.style.zIndex="9999999";a.style.overflow="hidden";a.setAttribute("aria-hidden","false")};_.g.hide=function(){yv(this.Po)};
_.g.UA=function(a){if(this.Oo)a&&a(this);else{if(!this.Po){var b=qv.S2+this.Gb;var c=this.Gb;var d=location.hostname;var e,f=document.cookie.match("(^|;) ?G_ENABLED_IDPS=([^;]*)(;|$)");f&&f.length>2&&(e=f[2]);(f=e&&fv(e.split("|"),c)>=0)?Gv(e,d):Gv(e?e+"|"+c:c,d);c=!f;var h=this.uU,k=this.eea;d=this.nn;e=this.An;e=e===void 0?!1:e;f=document.createElement("iframe");f.setAttribute("id",b);b=f.setAttribute;var l="allow-scripts allow-same-origin";document.requestStorageAccess&&hv(document.requestStorageAccess)&&
(l+=" allow-storage-access-by-user-activation");b.call(f,"sandbox",l);f.setAttribute("allow","identity-credentials-get");yv(f);f.setAttribute("frame-border","0");b=[h,"#origin=",encodeURIComponent(k)];b.push("&rpcToken=");b.push(encodeURIComponent(d));c&&b.push("&clearCache=1");_.cv&&b.push("&debug=1");e&&b.push("&supportBlocked3PCookies=1");document.body.appendChild(f);f.setAttribute("src",b.join(""));this.Po=f}a&&this.qB.push(a)}};_.g.qV=function(){return this.Oo&&this.Pw};_.g.xo=function(){return this.qU};
Rv=function(a){for(var b=0;b<a.qB.length;b++)a.qB[b](a);a.qB=[]};_.Tv=function(a,b,c,d){if(a.Oo){if(a.Oo&&a.wH)throw a="Failed to communicate with IDP IFrame due to unitialization error: "+a.xo(),dv(a),Error(a);Sv(a,{method:b,params:c},d)}else a.Wr.push({rpc:{method:b,params:c},callback:d}),a.UA()};Sv=function(a,b,c){if(c){for(var d=b.id;!d||a.nk[d];)d=(new Date).getMilliseconds()+"-"+(Math.random()*1E6+1);b.id=d;a.nk[d]=c}b.rpcToken=a.nn;a.Po.contentWindow.postMessage(_.nv.stringify(b),a.xH)};
Uv=function(a){if(a&&a.indexOf("::")>=0)throw Error("ja");};_.Qv.prototype.Gj=function(a,b,c,d,e,f,h,k,l){l=l===void 0?!1:l;Uv(f);b=_.iv(b);_.Tv(this,"getTokenResponse",{clientId:a,loginHint:c,request:b,sessionSelector:d,forceRefresh:h,skipCache:k,id:f,userInteracted:l},e)};_.Qv.prototype.SA=function(a,b,c,d,e){b=_.iv(b);_.Tv(this,"listIdpSessions",{clientId:a,request:b,sessionSelector:c,forceRefresh:e},d)};Vv=function(a,b,c){Uv(b.identifier);_.Tv(a,"getSessionSelector",b,c)};
_.Wv=function(a,b,c,d,e){Uv(b.identifier);_.Tv(a,"setSessionSelector",{domain:b.domain,crossSubDomains:b.crossSubDomains,policy:b.policy,id:b.id,hint:d,disabled:!!c},e)};Xv=function(a,b,c,d,e,f,h){b={clientId:b};c&&(b.pluginName=c);d&&(b.ackExtensionDate=d);b.useFedCm=e;f&&(b.fedCmEnabled=f);_.Tv(a,"monitorClient",b,h)};_.Qv.prototype.revoke=_.gb(8);_.Qv.prototype.Dt=_.gb(10);qv.mA={};qv.wG=function(a){return qv.mA[a]};
qv.UA=function(a,b,c){c=c===void 0?!1:c;var d=qv.wG(a);if(!d){d=String;if(Jv){var e=new window.Uint32Array(1);Iv.getRandomValues(e);e=Number("0."+e[0])}else e=Lv,e+=parseInt(Nv.substr(0,20),16),Nv=Pv(Nv),e/=Mv+Math.pow(16,20);d=new _.Qv(a,d(2147483647*e),c);qv.mA[a]=d}d.UA(b)};qv.z8=function(a){for(var b in qv.mA){var c=qv.wG(b);if(c&&c.Po&&c.Po.contentWindow==a.source&&c.xH==a.origin)return c}};qv.h9=function(a){for(var b in qv.mA){var c=qv.wG(b);if(c&&c.xH==a)return c}};qv=qv||{};
var Zv=function(){var a=[],b;for(b in _.Yv)a.push(_.Yv[b]);ov.call(this,a);this.Fm={};dv("EventBus is ready.")};gv(Zv,ov);_.Yv={D4:"sessionSelectorChanged",dE:"sessionStateChanged",Xs:"authResult",P1:"displayIFrame"};aw=function(a,b){var c=$v;a&&b&&(c.Fm[a]||(c.Fm[a]=[]),fv(c.Fm[a],b)<0&&c.Fm[a].push(b))};bw=function(a){var b=$v;a&&(b.Fm[a]||(b.Fm[a]=[]))};cw=function(a,b,c){return b&&a.Fm[b]&&fv(a.Fm[b],c)>=0};_.g=Zv.prototype;
_.g.jda=function(a){var b,c=!!a.source&&(a.source===_.xv||a.source.opener===window);if(b=c?qv.h9(a.origin):qv.z8(a)){try{var d=_.nv.parse(a.data)}catch(e){dv("Bad event, an error happened when parsing data.");return}if(!c){if(!d||!d.rpcToken||d.rpcToken!=b.nn){dv("Bad event, no RPC token.");return}if(d.id&&!d.method){c=d;if(a=b.nk[c.id])delete b.nk[c.id],a(c.result,c.error);return}}d.method!="fireIdpEvent"?dv("Bad IDP event, method unknown."):(a=d.params)&&a.type&&this.tU[a.type]?(d=this.tU[a.type],
c&&!d.K5?dv("Bad IDP event. Source window cannot be a popup."):d.Ss&&!d.Ss.call(this,b,a)?dv("Bad IDP event."):d.Rf.call(this,b,a)):dv("Bad IDP event.")}else dv("Bad event, no corresponding Idp Stub.")};_.g.Iea=function(a,b){return cw(this,a.Gb,b.clientId)};_.g.Hea=function(a,b){a=a.Gb;b=b.clientId;return!b||cw(this,a,b)};_.g.Y5=function(a,b){return cw(this,a.Gb,b.clientId)};
_.g.nca=function(a,b){a.Oo=!0;a.Pw=!!b.cookieDisabled;Rv(a);for(b=0;b<a.Wr.length;b++)Sv(a,a.Wr[b].rpc,a.Wr[b].callback);a.Wr=[]};_.g.mca=function(a,b){b={error:b.error};a.Oo=!0;a.wH=!0;a.qU=b;a.Wr=[];Rv(a)};_.g.QB=function(a,b){b.originIdp=a.Gb;this.dispatchEvent(b)};var $v=new Zv,dw=$v,ew={};ew.idpReady={Rf:dw.nca};ew.idpError={Rf:dw.mca};ew.sessionStateChanged={Rf:dw.QB,Ss:dw.Iea};ew.sessionSelectorChanged={Rf:dw.QB,Ss:dw.Hea};ew.authResult={Rf:dw.QB,Ss:dw.Y5,K5:!0};ew.displayIFrame={Rf:dw.QB};
$v.tU=ew||{};kv(window,"message",function(a){$v.jda.call($v,a)});
_.fw=function(a,b){this.Xe=!1;if(!a)throw Error("ka");var c=[],d;for(d in a)c.push(a[d]);ov.call(this,c);this.Jd=[location.protocol,"//",location.host].join("");this.ke=b.crossSubDomains?b.domain||this.Jd:this.Jd;if(!b)throw Error("la");if(!b.idpId)throw Error("ma");if(!_.vv(b.idpId,"authServerUrl")||!_.vv(b.idpId,"idpIFrameUrl"))throw Error("na`"+b.idpId);this.Gb=b.idpId;this.Ub=void 0;this.c7=!!b.disableTokenRefresh;this.X7=!!b.forceTokenRefresh;this.Hfa=!!b.skipTokenCache;this.An=!!b.supportBlocked3PCookies;
b.pluginName&&(this.Yca=b.pluginName);b.ackExtensionDate&&(this.y5=b.ackExtensionDate);this.xga=b.useFedCm;this.H7=this.An&&_.sv();this.setOptions(b);this.Ot=[];this.Pw=this.Nk=this.cV=!1;this.zj=void 0;this.oY();this.Wd=void 0;var e=this,f=function(){dv("Token Manager is ready.");if(e.Ot.length)for(var h=0;h<e.Ot.length;h++)e.Ot[h].call(e);e.cV=!0;e.Ot=[]};this.An&&(zv(),tv());qv.UA(this.Gb,function(h){e.Wd=h;h.Oo&&h.wH?(e.Nk=!0,e.zj=h.xo(),e.Or(e.zj)):(e.Pw=h.qV(),e.Ub?Xv(e.Wd,e.Ub,e.Yca,e.y5,e.xga,
e.H7,function(k){var l=!!k.validOrigin,m=!!k.blocked,n=!!k.suppressed;k.invalidExtension?(e.zj={error:"Invalid value for ack_extension_date. Please refer to [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Nk=!0,e.Or(e.zj)):l?m?n?(ev("You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
aw(e.Gb,e.Ub),f()):(e.zj={error:"You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Nk=!0,e.Or(e.zj)):(ev("Your client application uses libraries for user authentication or authorization that are deprecated. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
aw(e.Gb,e.Ub),f()):(e.zj={error:"Not a valid origin for the client: "+e.Jd+" has not been registered for client ID "+e.Ub+". Please go to https://console.developers.google.com/ and register this origin for your project's client ID."},e.Nk=!0,e.Or(e.zj))}):(bw(e.Gb),f()))},this.An)};gv(_.fw,ov);_.g=_.fw.prototype;_.g.setOptions=function(){};_.g.oY=function(){};_.g.Or=function(){};_.g.qV=function(){return this.Pw};_.g.xo=function(){return this.zj};gw=function(a,b,c){return function(){b.apply(a,c)}};
_.hw=function(a,b,c){if(a.cV)b.apply(a,c);else{if(a.Nk)throw a.zj;a.Ot.push(gw(a,b,c))}};_.fw.prototype.FP=_.gb(11);_.fw.prototype.Dt=_.gb(9);_.jw=function(a,b){_.fw.call(this,a,b);this.HX=new Av;this.Zk=this.np=null;iw(this)};gv(_.jw,_.fw);_.jw.prototype.setOptions=function(){};
var kw=function(a,b){a.Ue={crossSubDomains:!!b.crossSubDomains,id:b.sessionSelectorId,domain:a.ke};b.crossSubDomains&&(a.Ue.policy=b.policy)},lw=function(a,b){if(!b.authParameters)throw Error("oa");if(!b.authParameters.scope)throw Error("pa");if(!b.authParameters.response_type)throw Error("qa");a.In=b.authParameters;a.In.redirect_uri||(a.In.redirect_uri=[location.protocol,"//",location.host,location.pathname].join(""));a.Qj=_.iv(b.rpcAuthParameters||a.In);if(!a.Qj.scope)throw Error("ra");if(!a.Qj.response_type)throw Error("sa");
a:{var c=a.Qj.response_type.split(" ");for(var d=0,e=c.length;d<e;d++)if(c[d]&&!lv[c[d]]){c=!0;break a}c=!1}if(c)throw Error("ta");if(b.enableSerialConsent||b.enableGranularConsent)a.In.enable_granular_consent=!0,a.Qj.enable_serial_consent=!0;b.authResultIdentifier&&(a.Z5=b.authResultIdentifier);b.spec_compliant&&(a.Qj.spec_compliant=b.spec_compliant)};
_.jw.prototype.oY=function(){var a=this;$v.addEventListener(_.Yv.D4,function(b){a.Xe&&a.Ue&&b.originIdp==a.Gb&&!b.crossSubDomains==!a.Ue.crossSubDomains&&b.domain==a.Ue.domain&&b.id==a.Ue.id&&a.YW(b)});$v.addEventListener(_.Yv.dE,function(b){a.Xe&&b.originIdp==a.Gb&&b.clientId==a.Ub&&a.ZW(b)});$v.addEventListener(_.Yv.Xs,function(b){_.xv=void 0;a.Xe&&b.originIdp==a.Gb&&b.clientId==a.Ub&&b.id==a.Vk&&(a.np&&(window.clearTimeout(a.np),a.np=null),a.Vk=void 0,a.hp(b))});$v.addEventListener(_.Yv.P1,function(b){a.Xe&&
b.originIdp==a.Gb&&(b.hide?a.Wd.hide():a.Wd.show())})};_.jw.prototype.YW=function(){};_.jw.prototype.ZW=function(){};_.jw.prototype.hp=function(){};var nw=function(a,b){mw(a);a.c7||(a.Zk=rv.U6(function(){a.Gj(!0)},b-3E5),navigator.onLine&&a.Zk.start())},mw=function(a){a.Zk&&(a.Zk.clear(),a.Zk=null)},iw=function(a){var b=window;mv()&&(b=document.body);kv(b,"online",function(){a.Zk&&a.Zk.start()});kv(b,"offline",function(){a.Zk&&a.Zk.clear()})};_.jw.prototype.Gj=function(){};_.jw.prototype.AW=_.gb(12);
_.jw.prototype.kba=function(a,b){if(!this.Ub)throw Error("xa");this.Wd.SA(this.Ub,this.Qj,this.Ue,a,b)};_.jw.prototype.SA=function(a,b){_.hw(this,this.kba,[a,b])};_.pw=function(a){this.Pe=void 0;this.Wh=!1;this.rs=void 0;_.jw.call(this,ow,a)};gv(_.pw,_.jw);var ow={UN:"noSessionBound",kt:"userLoggedOut",d1:"activeSessionChanged",dE:"sessionStateChanged",e5:"tokenReady",d5:"tokenFailed",Xs:"authResult",ERROR:"error"};
_.pw.prototype.setOptions=function(a){if(!a.clientId)throw Error("ya");this.Ub=a.clientId;this.Ha=a.id;kw(this,a);lw(this,a)};_.pw.prototype.Or=function(a){this.dispatchEvent({type:ow.ERROR,error:"idpiframe_initialization_failed",details:a.error,idpId:this.Gb})};var qw=function(a){mw(a);a.rs=void 0;a.mI=void 0};_.g=_.pw.prototype;
_.g.YW=function(a){var b=a.newValue||{};if(this.Pe!=b.hint||this.Wh!=!!b.disabled){a=this.Pe;var c=!this.Pe||this.Wh;qw(this);this.Pe=b.hint;this.Wh=!!b.disabled;(b=!this.Pe||this.Wh)&&!c?this.dispatchEvent({type:ow.kt,idpId:this.Gb}):b||(a!=this.Pe&&this.dispatchEvent({type:ow.d1,idpId:this.Gb}),this.Pe&&this.Gj())}};
_.g.ZW=function(a){this.Wh||(this.Pe?a.user||this.rs?a.user==this.Pe&&(this.rs?a.sessionState?this.rs=a.sessionState:(qw(this),this.dispatchEvent({type:ow.kt,idpId:this.Gb})):a.sessionState&&(this.rs=a.sessionState,this.Gj())):this.Gj():this.dispatchEvent({type:ow.dE,idpId:this.Gb}))};_.g.hp=function(a){this.dispatchEvent({type:ow.Xs,authResult:a.authResult})};_.g.zu=_.gb(14);_.g.su=function(a){_.hw(this,this.jG,[a])};_.g.jG=function(a){Vv(this.Wd,this.Ue,a)};
_.g.fD=function(a,b,c,d){d=d===void 0?!1:d;if(!a)throw Error("za");qw(this);this.Pe=a;this.Wh=!1;b&&_.Wv(this.Wd,this.Ue,!1,this.Pe);this.Xe=!0;this.Gj(c,!0,d)};_.g.start=function(){_.hw(this,this.Ew,[])};
_.g.Ew=function(){var a=this.Ub==jv("client_id")?jv("login_hint"):void 0;var b=this.Ub==jv("client_id")?jv("state"):void 0;this.VI=b;if(a)window.history.replaceState?window.history.replaceState(null,document.title,window.location.href.split("#")[0]):window.location.href.hash="",this.fD(a,!0,!0,!0);else{var c=this;this.su(function(d){c.Xe=!0;d&&d.hint?(qw(c),c.Pe=d.hint,c.Wh=!!d.disabled,c.Wh?c.dispatchEvent({type:ow.kt,idpId:c.Gb}):c.fD(d.hint)):(qw(c),c.Pe=void 0,c.Wh=!(!d||!d.disabled),c.dispatchEvent({type:ow.UN,
autoOpenAuthUrl:!c.Wh,idpId:c.Gb}))})}};_.g.T7=function(){var a=this;this.su(function(b){b&&b.hint?b.disabled?a.dispatchEvent({type:ow.kt,idpId:a.Gb}):a.Gj(!0):a.dispatchEvent({type:ow.UN,idpId:a.Gb})})};_.g.iS=function(){_.hw(this,this.T7,[])};
_.g.Gj=function(a,b,c){var d=this;this.Wd.Gj(this.Ub,this.Qj,this.Pe,this.Ue,function(e,f){(f=f||e.error)?f=="user_logged_out"?(qw(d),d.dispatchEvent({type:ow.kt,idpId:d.Gb})):(d.mI=null,d.dispatchEvent({type:ow.d5,idpId:d.Gb,error:f})):(d.mI=e,d.rs=e.session_state,nw(d,e.expires_at),e.idpId=d.Gb,b&&d.VI&&(e.state=d.VI,d.VI=void 0),d.dispatchEvent({type:ow.e5,idpId:d.Gb,response:e}))},this.Ha,a,!1,c===void 0?!1:c)};_.g.revoke=_.gb(7);_.g.HY=_.gb(15);
_.rw=function(a){this.Jn=null;_.jw.call(this,{},a);this.Xe=!0};gv(_.rw,_.jw);_.g=_.rw.prototype;_.g.setOptions=function(a){if(!a.clientId)throw Error("ya");this.Ub=a.clientId;this.Ha=a.id;kw(this,a);lw(this,a)};_.g.Or=function(a){this.Jn&&(this.Jn({authResult:{error:"idpiframe_initialization_failed",details:a.error}}),this.Jn=null)};_.g.hp=function(a){if(this.Jn){var b=this.Jn;this.Jn=null;b(a)}};_.g.zu=_.gb(13);_.g.su=function(a){this.Nk?a(this.xo()):_.hw(this,this.jG,[a])};
_.g.jG=function(a){Vv(this.Wd,this.Ue,a)};_.sw=function(a,b,c){a.Nk?c(a.xo()):_.hw(a,a.Dca,[b,c])};_.rw.prototype.Dca=function(a,b){this.Wd.Gj(this.Ub,this.Qj,a,this.Ue,function(c,d){d?b({error:d}):b(c)},this.Ha,this.X7,this.Hfa)};_.rw.prototype.NV=_.gb(16);
var tw=function(a,b){b=_.Tb(b);b!==void 0&&a.assign(b)},uw=function(a){return Array.prototype.concat.apply([],arguments)},vw=function(){try{var a=Array.from((window.crypto||window.msCrypto).getRandomValues(new Uint8Array(64)))}catch(c){a=[];for(var b=0;b<64;b++)a[b]=Math.floor(Math.random()*256)}return _.Ji(a,3).substring(0,64)},ww=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(d===null||d===void 0)d="";b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))}return b.join("&")},
xw=function(a){window.addEventListener("hashchange",function(){location.hash.includes("client_id")&&window.location.reload()});tw(window.location,_.Pb(a))},yw=function(a,b,c){if(!a.Xe)throw Error("ua");b?_.Wv(a.Wd,a.Ue,!0,void 0,c):_.Wv(a.Wd,a.Ue,!0,a.Pe,c)},zw=function(a){if(!a.Xe)throw Error("ua");return a.mI},Aw,Bw,Cw,Dw,Ew,Fw,Gw,Hw,Iw,Jw,Kw,Lw,Ow;
_.rw.prototype.NV=_.nb(16,function(a,b){var c=this.Wd,d=this.Ub,e=this.Ue,f=_.iv(this.Qj);delete f.response_type;_.Tv(c,"getOnlineCode",{clientId:d,loginHint:a,request:f,sessionSelector:e},b)});_.pw.prototype.HY=_.nb(15,function(a){zw(this)&&zw(this).access_token&&(this.Wd.revoke(this.Ub,zw(this).access_token,a),yw(this,!0))});
_.pw.prototype.zu=_.nb(14,function(){var a=this;return function(b){b&&b.authResult&&b.authResult.login_hint&&(a.MB?(b.authResult.client_id=a.Ub,xw(a.MB+"#"+ww(b.authResult))):a.fD(b.authResult.login_hint,a.Wh||b.authResult.login_hint!=a.Pe,!0,!0))}});
_.rw.prototype.zu=_.nb(13,function(a){var b=this;return function(c){c&&c.authResult&&c.authResult.login_hint?b.su(function(d){_.Wv(b.Wd,b.Ue,d&&d.disabled,c.authResult.login_hint,function(){_.sw(b,c.authResult.login_hint,a)})}):a(c&&c.authResult&&c.authResult.error?c.authResult:c&&c.authResult&&!c.authResult.login_hint?{error:"wrong_response_type"}:{error:"unknown_error"})}});_.jw.prototype.AW=_.nb(12,function(){this.Ub&&_.Tv(this.Wd,"startPolling",{clientId:this.Ub,origin:this.Jd,id:this.Vk})});
_.Qv.prototype.revoke=_.nb(8,function(a,b,c){_.Tv(this,"revoke",{clientId:a,token:b},c)});_.pw.prototype.revoke=_.nb(7,function(a){_.hw(this,this.HY,[a])});Aw="openid email profile https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/plus.me https://www.googleapis.com/auth/plus.login".split(" ");
Bw=function(){var a=navigator.userAgent,b;if(b=!!a&&a.indexOf("CriOS")!=-1)b=-1,(a=a.match(/CriOS\/(\d+)/))&&a[1]&&(b=parseInt(a[1],10)||-1),b=b<48;return b};
Cw=function(){var a=navigator.userAgent.toLowerCase();if(!(a.indexOf("safari/")>-1&&a.indexOf("chrome/")<0&&a.indexOf("crios/")<0&&a.indexOf("android")<0))return!1;var b=RegExp("version/(\\d+)\\.(\\d+)[\\.0-9]*").exec(navigator.userAgent.toLowerCase());if(!b||b.length<3)return!1;a=parseInt(b[1],10);b=parseInt(b[2],10);return a>12||a==12&&b>=1};Dw=function(a){return a.length>0&&a.every(function(b){return Aw.includes(b)})};
Ew=function(a,b,c,d,e,f,h){var k=_.vv(a,"authServerUrl");if(!k)throw Error("ea`"+a);a=_.iv(d);a.response_type=h||"permission";a.client_id=c;a.ss_domain=b;if(f&&f.extraQueryParams)for(var l in f.extraQueryParams)a[l]=f.extraQueryParams[l];(b=e)&&!(b=Cw())&&(b=navigator.userAgent.toLowerCase(),b.indexOf("ipad;")>-1||b.indexOf("iphone;")>-1?(b=RegExp("os (\\d+)_\\d+(_\\d+)? like mac os x").exec(navigator.userAgent.toLowerCase()),b=!b||b.length<2?!1:parseInt(b[1],10)>=14):b=!1);b&&!a.prompt&&(a.prompt=
"select_account");return k+(k.indexOf("?")<0?"?":"&")+ww(a)};Fw=function(a,b,c,d){if(!a.Ub)throw Error("va");a.Vk=c||a.Z5||"auth"+Math.floor(Math.random()*1E6+1);b=b||{};b.extraQueryParams=b.extraQueryParams||{};if(!b.extraQueryParams.redirect_uri){var e=a.Jd.split("//");c=b.extraQueryParams;var f=e[0],h=e[1];e=a.Vk;var k=f.indexOf(":");k>0&&(f=f.substring(0,k));f=["storagerelay://",f,"/",h,"?"];f.push("id="+e);c.redirect_uri=f.join("")}return Ew(a.Gb,a.ke,a.Ub,a.In,!0,b,d)};
Gw=function(a,b,c){if(!a.Ub)throw Error("va");return Ew(a.Gb,a.ke,a.Ub,a.In,!1,b,c)};Hw=function(a,b){a.np&&window.clearTimeout(a.np);a.np=window.setTimeout(function(){a.Vk==b&&(_.xv=void 0,a.np=null,a.Vk=void 0,a.hp({authResult:{error:"popup_closed_by_user"}}))},1E3)};
Iw=function(a,b,c){if(!a.Ub)throw Error("wa");c=c||{};c=Fw(a,c.sessionMeta,c.oneTimeId,c.responseType);(Object.hasOwnProperty.call(window,"ActiveXObject")&&!window.ActiveXObject||Bw())&&_.hw(a,a.AW,[]);var d=a.Vk;a.HX.open(c,b,function(){a.Vk==d&&Hw(a,d)},function(){a.Vk=void 0;a.hp({authResult:{error:"popup_blocked_by_browser"}})})};
Jw=function(a,b){var c=b||{};b=_.iv(a.In);if(c.sessionMeta&&c.sessionMeta.extraQueryParams)for(var d in c.sessionMeta.extraQueryParams)b[d]=c.sessionMeta.extraQueryParams[d];var e;c.sessionMeta.extraQueryParams.scope&&(e=c.sessionMeta.extraQueryParams.scope.split(" "));!e&&b.scope&&(e=b.scope.split(" "));delete b.redirect_uri;delete b.origin;delete b.client_id;delete b.scope;b.prompt=="select_account"&&delete b.prompt;b.gsiwebsdk="fedcm";b.ss_domain=a.ke;b.nonce||(b.nonce="notprovided");d=_.vv(a.Gb,
"fedcmConfigUrl");b.response_type=c.responseType.split(" ");b.scope=e.join(" ");c=navigator.userActivation.isActive?"button":"widget";e=Dw(e)?["name","email","picture"]:[];return{identity:{providers:[{configURL:d,clientId:a.Ub,fields:e,params:b}],mode:c},mediation:"required"}};
Kw=function(a,b){if(!a.Ub)throw Error("wa");b=Jw(a,b);navigator.credentials.get(b).then(function(c){c=JSON.parse(c.token);var d={client_id:c.client_id,login_hint:c.login_hint,expires_in:3600,scope:c.scope};c.code&&(d.code=c.code);c.id_token&&(d.id_token=c.id_token);a.hp({type:_.Yv.Xs,idpId:a.Gb,authResult:d})},function(c){a.hp({type:_.Yv.Xs,idpId:a.Gb,authResult:{error:c}})})};Lw=function(a,b,c){a.Nk?c(a.xo()):_.hw(a,a.NV,[b,c])};_.Mw=function(a){_.vf(_.If,"le",[]).push(a)};
_.Nw=function(a){for(var b=[],c=0,d=0;c<a.length;){var e=a[c++];if(e<128)b[d++]=String.fromCharCode(e);else if(e>191&&e<224){var f=a[c++];b[d++]=String.fromCharCode((e&31)<<6|f&63)}else if(e>239&&e<365){f=a[c++];var h=a[c++],k=a[c++];e=((e&7)<<18|(f&63)<<12|(h&63)<<6|k&63)-65536;b[d++]=String.fromCharCode(55296+(e>>10));b[d++]=String.fromCharCode(56320+(e&1023))}else f=a[c++],h=a[c++],b[d++]=String.fromCharCode((e&15)<<12|(f&63)<<6|h&63)}return b.join("")};
Ow=function(a){var b=[];_.Ki(a,function(c){b.push(c)});return b};_.Pw=function(a,b){_.sj[b||"token"]=a};_.Qw=function(a){delete _.sj[a||"token"]};_.nv={parse:function(a){a=_.Ug("["+String(a)+"]");if(a===!1||a.length!==1)throw new SyntaxError("JSON parsing failed.");return a[0]},stringify:function(a){return _.Vg(a)}};_.rw.prototype.TF=function(a,b){_.hw(this,this.J7,[a,b])};_.rw.prototype.J7=function(a,b){this.Wd.TF(this.Ub,a,this.Qj,this.Ue,b)};_.Qv.prototype.TF=function(a,b,c,d,e){c=_.iv(c);_.Tv(this,"gsi:fetchLoginHint",{clientId:a,loginHint:b,request:c,sessionSelector:d},e)};var Rw,Sw=["client_id","cookie_policy","scope"],Tw="client_id cookie_policy fetch_basic_profile hosted_domain scope openid_realm disable_token_refresh login_hint ux_mode redirect_uri state prompt oidc_spec_compliant nonce enable_serial_consent enable_granular_consent include_granted_scopes response_type session_selection plugin_name ack_extension_date use_fedcm gsiwebsdk".split(" "),Uw=["authuser","after_redirect","access_type","hl"],Vw=["login_hint","prompt"],Ww={clientid:"client_id",cookiepolicy:"cookie_policy"},
Xw=["approval_prompt","authuser","login_hint","prompt","hd"],Yw=["login_hint","g-oauth-window","status"],Zw=Math.min(_.Sf("oauth-flow/authWindowWidth",599),screen.width-20),$w=Math.min(_.Sf("oauth-flow/authWindowHeight",600),screen.height-30);var ax=function(a){_.rb.call(this,a)};_.A(ax,_.rb);ax.prototype.name="gapi.auth2.ExternallyVisibleError";var bx=function(){};bx.prototype.select=function(a,b){if(a.sessions&&a.sessions.length==1&&(a=a.sessions[0],a.login_hint)){b(a);return}b()};var cx=function(){};cx.prototype.select=function(a,b){if(a.sessions&&a.sessions.length)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.login_hint){b(d);return}}b()};var dx=function(a){this.a6=a};
dx.prototype.select=function(a,b){if(a.sessions)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.session_state&&d.session_state.extraQueryParams&&d.session_state.extraQueryParams.authuser==this.a6){d.login_hint?b(d):b();return}}b()};var ex=function(a){this.Ee=a;this.xC=[]};ex.prototype.select=function(a){var b=0,c=this,d=function(e){if(e)a(e);else{var f=c.xC[b];f?(b++,c.Ee.SA(function(h){h?f.select(h,d):d()})):a()}};d()};var fx=function(a){a=new ex(a);a.xC.push(new bx);return a},gx=function(a){a=new ex(a);a.xC.push(new cx);return a},hx=function(a,b){b===void 0||b===null?b=fx(a):(a=new ex(a),a.xC.push(new dx(b)),b=a);return b};var ix=function(a){this.Rf=a;this.isActive=!0};ix.prototype.remove=function(){this.isActive=!1};ix.prototype.trigger=function(){};var jx=function(a){this.remove=function(){a.remove()};this.trigger=function(){a.trigger()}},kx=function(){this.uc=[]};kx.prototype.add=function(a){this.uc.push(a)};kx.prototype.notify=function(a){for(var b=this.uc,c=[],d=0;d<b.length;d++){var e=b[d];e.isActive&&(c.push(e),_.Wk(lx(e.Rf,a)))}this.uc=c};var lx=function(a,b){return function(){a(b)}};var nx=function(a){this.Ca=null;this.Fga=new mx(this);this.uc=new kx;a!=void 0&&this.set(a)};nx.prototype.set=function(a){a!=this.Ca&&(this.Ca=a,this.Fga.value=a,this.uc.notify(this.Ca))};nx.prototype.get=function(){return this.Ca};nx.prototype.qa=function(a){a=new ox(this,a);this.uc.add(a);return a};nx.prototype.get=nx.prototype.get;var ox=function(a,b){ix.call(this,b);this.oba=a};_.A(ox,ix);ox.prototype.trigger=function(){var a=this.Rf;a(this.oba.get())};
var mx=function(a){this.value=null;this.qa=function(b){return new jx(a.qa(b))}};var px={Xia:"fetch_basic_profile",Yja:"login_hint",xla:"prompt",Dla:"redirect_uri",Vla:"scope",rna:"ux_mode",Hma:"state"},qx=function(a){this.Na={};if(a&&!_.Di(a))if(typeof a.get=="function")this.Na=a.get();else for(var b in px){var c=px[b];c in a&&(this.Na[c]=a[c])}};qx.prototype.get=function(){return this.Na};qx.prototype.KZ=function(a){this.Na.scope=a;return this};qx.prototype.Iu=function(){return this.Na.scope};
var rx=function(a,b){var c=a.Na.scope;b=uw(b.split(" "),c?c.split(" "):[]);_.Mk(b);a.Na.scope=b.join(" ")};_.g=qx.prototype;_.g.ifa=function(a){this.Na.prompt=a;return this};_.g.j9=function(){return this.Na.prompt};_.g.Lea=function(){_.Zg.warn("Property app_package_name no longer supported and was not set");return this};_.g.h8=function(){_.Zg.warn("Property app_package_name no longer supported")};_.g.Ff=function(a){this.Na.state=a};_.g.getState=function(){return this.Na.state};var sx=function(){return["toolbar=no","location="+(window.opera?"no":"yes"),"directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no","width="+Zw,"height="+$w,"top="+(screen.height-$w)/2,"left="+(screen.width-Zw)/2].join()},tx=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+).?.?.?$/,"$1");return JSON.parse(_.Nw(Ow(a)))},ux=function(){Rw=_.Sf("auth2/idpValue","google");var a=_.Sf("oauth-flow/authUrl","https://accounts.google.com/o/oauth2/auth"),
b=_.Sf("oauth-flow/idpIframeUrl","https://accounts.google.com/o/oauth2/iframe");a={fedcmConfigUrl:_.Sf("oauth-flow/fedcmConfigUrl","https://accounts.google.com/o/fedcm/config.json"),authServerUrl:a,idpIFrameUrl:b};_.wv(Rw,a)},vx=function(a,b,c){for(var d=0;d<b.length;d++){var e=b[d];if(d===b.length-1){a[e]=c;break}_.yc(a[e])||(a[e]={});a=a[e]}},wx=function(){var a=window.location.origin;a||(a=window.location.protocol+"//"+window.location.host);return a},yx=function(){var a=xx();a.storage_path&&window.sessionStorage.setItem(a.storage_path,
wx()+window.location.pathname);if(a.status.toLowerCase()=="enforced")throw new ax("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href);a.status.toLowerCase()=="informational"&&_.Zg.warn("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href)},zx=function(a){return _.ej.get("GSI_ALLOW_3PCD")==="1"?!0:a===!1?!1:a===!0||(_.If.le||[]).includes("fedcm_migration_mod")?!0:!1};var Ax=function(a){var b=a?(b=tx(a))?b.sub:null:null;this.Ha=b;this.Oc=a?_.Nk(a):null};_.g=Ax.prototype;_.g.getId=function(){return this.Ha};_.g.tG=function(){var a=tx(this.Oc);return a?a.hd:null};_.g.Kg=function(){return!!this.Oc};_.g.jm=function(a){if(a)return this.Oc;a=Bx;var b=_.Nk(this.Oc);!a.zA||a.rH||a.P$||(delete b.access_token,delete b.scope);return b};_.g.SJ=function(){return Bx.SJ()};_.g.xl=function(){this.Oc=null};_.g.J8=function(){return this.Oc?this.Oc.scope:null};
_.g.update=function(a){this.Ha=a.Ha;this.Oc=a.Oc;this.Oc.id_token?this.Ux=new Cx(this.Oc):this.Ux&&(this.Ux=null)};var Dx=function(a){return a.Oc&&typeof a.Oc.session_state=="object"?_.Nk(a.Oc.session_state.extraQueryParams||{}):{}};_.g=Ax.prototype;_.g.gG=function(){var a=Dx(this);return a&&a.authuser!==void 0&&a.authuser!==null?a.authuser:null};
_.g.wl=function(a){var b=Bx,c=new qx(a);b.rH=c.Iu()?!0:!1;Bx.zA&&rx(c,"openid profile email");return new _.gl(function(d,e){var f=Dx(this);f.login_hint=this.getId();f.scope=c.Iu();Ex(b,d,e,f)},this)};_.g.Mu=function(a){return new _.gl(function(b,c){var d=a||{},e=Bx;d.login_hint=this.getId();e.Mu(d).then(b,c)},this)};_.g.D9=function(a){return this.wl(a)};_.g.disconnect=function(){return Bx.disconnect()};_.g.j8=function(){return this.Ux};
_.g.gA=function(a){if(!this.Kg())return!1;var b=this.Oc&&this.Oc.scope?this.Oc.scope.split(" "):"";return _.ed(a?a.split(" "):[],function(c){return _.tb(b,c)})};var Cx=function(a){a=tx(a);this.c8=a.sub;this.Xf=a.name;this.z9=a.given_name;this.G7=a.family_name;this.yU=a.picture;this.Sy=a.email};_.g=Cx.prototype;_.g.getId=function(){return this.c8};_.g.Dg=function(){return this.Xf};_.g.H8=function(){return this.z9};_.g.B8=function(){return this.G7};_.g.P8=function(){return this.yU};_.g.oo=function(){return this.Sy};var xx,Fx;xx=function(){var a=_.ej.get("G_AUTH2_MIGRATION");if(!a)return{status:"none"};a=/(enforced|informational)(?::(.*))?/i.exec(a);return a?{status:a[1].toLowerCase(),storage_path:a[2]}:(_.Zg.warn("The G_AUTH2_MIGRATION cookie value is not valid."),{status:"none"})};Fx=function(a){var b=location;if(a&&a!="none")return a=="single_host_origin"?b.protocol+"//"+b.host:a};
_.Gx=function(a){if(!a)throw new ax("No cookiePolicy");var b=window.location.hostname;a=="single_host_origin"&&(a=window.location.protocol+"//"+b);if(a=="none")return null;var c=/^(https?:\/\/)([0-9.\-_A-Za-z]+)(?::(\d+))?$/.exec(a);if(!c)throw new ax("Invalid cookiePolicy");a=c[2];c=c[1];var d={};d.dotValue=a.split(".").length;d.isSecure=c.indexOf("https")!=-1;d.domain=a;if(!_.Wj(b,"."+a)&&!_.Wj(b,a))throw new ax("Invalid cookiePolicy domain");return d};var Ix=function(a){var b=a||{},c=Hx();_.$c(Tw,function(d){typeof b[d]==="undefined"&&typeof c[d]!=="undefined"&&(b[d]=c[d])});return b},Hx=function(){for(var a={},b=document.getElementsByTagName("meta"),c=0;c<b.length;++c)if(b[c].name){var d=b[c].name;if(d.indexOf("google-signin-")==0){d=d.substring(14);var e=b[c].content;Ww[d]&&(d=Ww[d]);_.tb(Tw,d)&&e&&(a[d]=e=="true"?!0:e=="false"?!1:e)}}return a},Jx=function(a){return String(a).replace(/_([a-z])/g,function(b,c){return c.toUpperCase()})},Kx=function(a){_.$c(Tw,
function(b){var c=Jx(b);typeof a[c]!=="undefined"&&typeof a[b]==="undefined"&&(a[b]=a[c],delete a[c])})},Lx=function(a){a=Ix(a);Kx(a);a.cookie_policy||(a.cookie_policy="single_host_origin");var b=Tw+Uw,c;for(c in a)b.indexOf(c)<0&&delete a[c];return a},Mx=function(a,b){if(!a)throw new ax("Empty initial options.");for(var c=0;c<Sw.length;++c)if(!(b&&Sw[c]=="scope"||a[Sw[c]]))throw new ax("Missing required parameter '"+Sw[c]+"'");_.Gx(a.cookie_policy)},Ox=function(a){var b={authParameters:{redirect_uri:void 0,
response_type:"token id_token",scope:a.scope,"openid.realm":a.openid_realm,include_granted_scopes:!0},clientId:a.client_id,crossSubDomains:!0,domain:Fx(a.cookie_policy),disableTokenRefresh:!!a.disable_token_refresh,idpId:Rw};Nx(b,a);_.$c(Vw,function(d){a[d]&&(b.authParameters[d]=a[d])});typeof a.enable_serial_consent=="boolean"&&(b.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent=="boolean"&&(b.enableGranularConsent=a.enable_granular_consent);if(a.plugin_name)b.pluginName=
a.plugin_name;else{var c=_.Sf("auth2/pluginName");c&&(b.pluginName=c)}a.ack_extension_date&&(b.authParameters.ack_extension_date=a.ack_extension_date,b.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(b.useFedCm=a.use_fedcm);return b},Nx=function(a,b){var c=b.oidc_spec_compliant;b=b.nonce;c&&(a.spec_compliant=c,b=b||vw());b&&(a.authParameters.nonce=b,a.forceTokenRefresh=!0,a.skipTokenCache=!0)},Sx=function(a){var b=a.client_id,c=a.cookie_policy,d=a.scope,e=a.openid_realm,f=
a.hosted_domain,h=a.oidc_spec_compliant,k=a.nonce,l=Px(a),m={authParameters:{response_type:l,scope:d,"openid.realm":e},rpcAuthParameters:{response_type:l,scope:d,"openid.realm":e},clientId:b,crossSubDomains:!0,domain:Fx(c),idpId:Rw};f&&(m.authParameters.hd=f,m.rpcAuthParameters.hd=f);h&&(m.rpcAuthParameters.spec_compliant=h,k=k||vw());k&&(m.authParameters.nonce=k,m.rpcAuthParameters.nonce=k,m.forceTokenRefresh=!0,m.skipTokenCache=!0);_.$c(Vw.concat(Uw),function(n){a[n]&&(m.authParameters[n]=a[n])});
a.authuser!==void 0&&a.authuser!==null&&(m.authParameters.authuser=a.authuser);typeof a.include_granted_scopes=="boolean"&&(b=new Qx(a.response_type||"token"),b.fr()&&(m.authParameters.include_granted_scopes=a.include_granted_scopes),Rx(b)&&(m.rpcAuthParameters.include_granted_scopes=a.include_granted_scopes,a.include_granted_scopes===!1&&(m.forceTokenRefresh=!0,m.skipTokenCache=!0)));typeof a.enable_serial_consent=="boolean"&&(m.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent==
"boolean"&&(m.enableGranularConsent=a.enable_granular_consent);a.plugin_name?m.pluginName=a.plugin_name:(b=_.Sf("auth2/pluginName"))&&(m.pluginName=b);a.ack_extension_date&&(m.authParameters.ack_extension_date=a.ack_extension_date,m.rpcAuthParameters.ack_extension_date=a.ack_extension_date,m.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(m.useFedCm=a.use_fedcm);return m},Px=function(a){a=new Qx(a.response_type||"token");var b=[];Rx(a)&&b.push("token");Tx(a,"id_token")&&b.push("id_token");
b.length==0&&(b=["token","id_token"]);return b.join(" ")},Ux=["permission","id_token"],Vx=/(^|[^_])token/,Qx=function(a){this.bs=[];this.JH(a)};Qx.prototype.JH=function(a){a?((a.indexOf("permission")>=0||a.match(Vx))&&this.bs.push("permission"),a.indexOf("id_token")>=0&&this.bs.push("id_token"),a.indexOf("code")>=0&&this.bs.push("code")):this.bs=Ux};Qx.prototype.fr=function(){return Tx(this,"code")};var Rx=function(a){return Tx(a,"permission")};Qx.prototype.toString=function(){return this.bs.join(" ")};
var Tx=function(a,b){var c=!1;_.$c(a.bs,function(d){d==b&&(c=!0)});return c};var Xx=function(a,b,c){this.gp=b;this.bca=a;for(var d in a)a.hasOwnProperty(d)&&Wx(this,d);if(c&&c.length)for(a=0;a<c.length;a++)this[c[a]]=this.gp[c[a]]},Wx=function(a,b){a[b]=function(){return a.bca[b].apply(a.gp,arguments)}};Xx.prototype.then=function(a,b,c){var d=this;return _.kl().then(function(){return Yx(d.gp,a,b,c)})};_.Qk(Xx);var Bx,Zx,ay;Bx=null;_.$x=function(){return Bx?Zx():null};Zx=function(){return new Xx(ay.prototype,Bx,["currentUser","isSignedIn"])};ay=function(a){delete a.include_granted_scopes;this.Na=Ox(a);this.R6=a.cookie_policy;this.P$=!!a.scope;(this.zA=a.fetch_basic_profile!==!1)&&(this.Na.authParameters.scope=by(this,"openid profile email"));this.Na.supportBlocked3PCookies=zx(a.use_fedcm);this.Xu=a.hosted_domain;this.Dga=a.ux_mode||"popup";this.MB=a.redirect_uri||null;this.So()};
ay.prototype.So=function(){this.currentUser=new nx(new Ax(null));this.isSignedIn=new nx(!1);this.Ee=new _.pw(this.Na);this.EA=this.tr=null;this.aba=new _.gl(function(a,b){this.tr=a;this.EA=b},this);this.pB={};this.nv=!0;cy(this);this.Ee.start()};
var cy=function(a){a.Ee.addEventListener("error",function(b){a.nv&&a.tr&&(a.nv=!1,a.EA({error:b.error,details:b.details}),a.tr=null,a.EA=null)});a.Ee.addEventListener("authResult",function(b){b&&b.authResult&&a.Lf(b);a.Ee.zu()(b)});a.Ee.addEventListener("tokenReady",function(b){var c=new Ax(b.response);if(a.Xu&&a.Xu!=c.tG())a.Lf({type:"tokenFailed",reason:"Account domain does not match hosted_domain specified by gapi.auth2.init.",accountDomain:c.tG(),expectedDomain:a.Xu});else{a.currentUser.get().update(c);
var d=a.currentUser;d.uc.notify(d.Ca);a.isSignedIn.set(!0);c=c.gG();(d=_.Gx(a.R6))&&c&&_.ej.set(["G_AUTHUSER_",window.location.protocol==="https:"&&d.wf?"S":"H",d.cj].join(""),c,{domain:d.domain,secure:d.isSecure});_.Pw(b.response);a.Lf(b)}});a.Ee.addEventListener("noSessionBound",function(b){a.nv&&b.autoOpenAuthUrl?(a.nv=!1,fx(a.Ee).select(function(c){if(c&&c.login_hint){var d=a.Ee;_.hw(d,d.fD,[c.login_hint,!0])}else a.currentUser.set(new Ax(null)),a.isSignedIn.set(!1),_.Qw(),a.Lf(b)})):(a.currentUser.set(new Ax(null)),
a.isSignedIn.set(!1),_.Qw(),a.Lf(b))});a.Ee.addEventListener("tokenFailed",function(b){a.Lf(b)});a.Ee.addEventListener("userLoggedOut",function(b){a.currentUser.get().xl();var c=a.currentUser;c.uc.notify(c.Ca);a.isSignedIn.set(!1);_.Qw();a.Lf(b)})},Yx=function(a,b,c,d){return a.aba.then(function(e){if(b)return b(e.B9)},c,d)};ay.prototype.Lf=function(a){if(a){this.nv=!1;var b=a.type||"";if(this.pB[b])this.pB[b](a);this.tr&&(this.tr({B9:this}),this.EA=this.tr=null)}};
var dy=function(a,b){_.wb(b,function(c,d){a.pB[d]=function(e){a.pB={};c(e)}})},Ex=function(a,b,c,d){d=_.Nk(d);a.Xu&&(d.hd=a.Xu);var e=d.ux_mode||a.Dga;delete d.ux_mode;delete d.app_package_name;var f={sessionMeta:{extraQueryParams:d},responseType:"permission id_token"};e=="redirect"?(d.redirect_uri||(d.redirect_uri=a.MB||wx()+window.location.pathname),ey(a,f)):(delete d.redirect_uri,fy(a,f),dy(a,{authResult:function(h){h.authResult&&h.authResult.error?c(h.authResult):dy(a,{tokenReady:function(){b(a.currentUser.get())},
tokenFailed:c})}}))};ay.prototype.wl=function(a){return new _.gl(function(b,c){var d=new qx(a);this.rH=d.Iu()?!0:!1;this.zA?(d.Na.fetch_basic_profile=!0,rx(d,"email profile openid")):d.Na.fetch_basic_profile=!1;var e=by(this,d.Iu());d.KZ(e);Ex(this,b,c,d.get())},this)};
ay.prototype.Mu=function(a){var b=a||{};this.rH=!!b.scope;a=by(this,b.scope);if(a=="")return _.ll({error:"Missing required parameter: scope"});var c={scope:a,access_type:"offline",include_granted_scopes:!0};_.$c(Xw,function(d){b[d]!=null&&(c[d]=b[d])});c.hasOwnProperty("prompt")||c.hasOwnProperty("approval_prompt")||(c.prompt="consent");b.redirect_uri=="postmessage"||b.redirect_uri==void 0?a=gy(this,c):(c.redirect_uri=b.redirect_uri,ey(this,{sessionMeta:{extraQueryParams:c},responseType:"code id_token"}),
a=_.kl({message:"Redirecting to IDP."}));return a};
var gy=function(a,b){b.origin=wx();delete b.redirect_uri;fy(a,{sessionMeta:{extraQueryParams:b},responseType:"code permission id_token"});return new _.gl(function(c,d){dy(this,{authResult:function(e){(e=e&&e.authResult)&&e.code?c({code:e.code}):d(e&&e.error?e:{error:"unknown_error"})}})},a)},fy=function(a,b){vx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");a=a.Ee;var c=sx();a.An&&_.sv()?Kw(a,b):Iw(a,c,b)},ey=function(a,b){vx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");a=a.Ee;b=b||
{};a.An&&_.sv()?(a.MB=b.sessionMeta.extraQueryParams.redirect_uri,Kw(a,b)):window.location.assign(Gw(a,b.sessionMeta,b.responseType))};ay.prototype.xl=function(a){var b=a||!1;return new _.gl(function(c){yw(this.Ee,b,function(){c()})},this)};ay.prototype.QS=function(){return this.Na.authParameters.scope};var by=function(a,b){a=a.QS();b=uw(b?b.split(" "):[],a?a.split(" "):[]);_.Mk(b);return b.join(" ")};
ay.prototype.SJ=function(){var a=this;return new _.gl(function(b,c){dy(a,{noSessionBound:c,tokenFailed:c,userLoggedOut:c,tokenReady:function(d){b(d.response)}});a.Ee.iS()})};ay.prototype.aP=function(a,b,c,d){if(a=typeof a==="string"?document.getElementById(a):a){var e=this;_.qk(a,"click",function(){var f=b;typeof b=="function"&&(f=b());e.wl(f).then(function(h){c&&c(h)},function(h){d&&d(h)})})}else d&&d({error:"Could not attach click handler to the element. Reason: element not found."})};
ay.prototype.disconnect=function(){return new _.gl(function(a){this.Ee.revoke(function(){a()})},this)};ay.prototype.attachClickHandler=ay.prototype.aP;var hy;_.gl.prototype["catch"]=_.gl.prototype.Ow;hy=null;_.iy=function(a){yx();a=Lx(a);if(Bx){if(_.bv(a,hy||{}))return Zx();throw new ax("gapi.auth2 has been initialized with different options. Consider calling gapi.auth2.getAuthInstance() instead of gapi.auth2.init().");}Mx(a,a.fetch_basic_profile!==!1);ux();hy=a;Bx=new ay(a);_.If.ga=1;return Zx()};var ky,my,jy,oy,ny,yy;
_.ly=function(a,b){yx();ux();a=Lx(a);Mx(a);var c=Sx(a);c.supportBlocked3PCookies=zx(a.use_fedcm);var d=new _.rw(c);a.prompt=="none"?jy(d,a,function(e){e.status=e.error?{signed_in:!1,method:null,google_logged_in:!1}:{signed_in:!0,method:"AUTO",google_logged_in:!0};b(e)}):ky(d,a,function(e){if(e.error)e.status={signed_in:!1,method:null,google_logged_in:!1};else{var f=e.access_token||e.id_token;e.status={signed_in:!!f,method:"PROMPT",google_logged_in:!!f}}e["g-oauth-window"]=d.HX.Fi;b(e)})};
ky=function(a,b,c){var d=new Qx(b.response_type);c=my(a,d,c);var e={responseType:d.toString()};vx(e,["sessionMeta","extraQueryParams","gsiwebsdk"],b.gsiwebsdk||"2");d.fr()&&vx(e,["sessionMeta","extraQueryParams","access_type"],b.access_type||"offline");b.redirect_uri&&vx(e,["sessionMeta","extraQueryParams","redirect_uri"],b.redirect_uri);b.state&&vx(e,["sessionMeta","extraQueryParams","state"],b.state);b=sx();a.Nk?c({authResult:{error:"idpiframe_initialization_failed",details:a.xo().error}}):(a.Jn=
c,a.An&&_.sv()?Kw(a,e):Iw(a,b,e))};my=function(a,b,c){if(Rx(b)){var d=ny(c);return function(e){e&&e.authResult&&!e.authResult.error?a.zu(function(f){f&&!f.error?(f=_.Nk(f),b.fr()&&(f.code=e.authResult.code),d(f)):d(f?f:{error:"unknown_error"})})(e):d(e&&e.authResult?e.authResult:{error:"unknown_error"})}}return function(e){e&&e.authResult&&!e.authResult.error?c(_.Nk(e.authResult)):c(e&&e.authResult?e.authResult:{error:"unknown_error"})}};
jy=function(a,b,c){if((new Qx(b.response_type)).fr()&&b.access_type=="offline")c({error:"immediate_failed",error_subtype:"access_denied"});else{var d=ny(c);b.login_hint?a.TF(b.login_hint,function(e){e?oy(a,b,e,d):c({error:"immediate_failed",error_subtype:"access_denied"})}):b.authuser!==void 0&&b.authuser!==null?hx(a,b.authuser).select(function(e){e&&e.login_hint?oy(a,b,e.login_hint,d):d({error:"immediate_failed",error_subtype:"access_denied"})}):a.su(function(e){e&&e.hint?oy(a,b,e.hint,d):e&&e.disabled?
d({error:"immediate_failed",error_subtype:"no_user_bound"}):(b.session_selection=="first_valid"?gx(a):fx(a)).select(function(f){f&&f.login_hint?oy(a,b,f.login_hint,d):d({error:"immediate_failed",error_subtype:"no_user_bound"})})})}};oy=function(a,b,c,d){b=new Qx(b.response_type);var e=0,f={},h=function(k){!k||k.error?d(k):(e--,_.Bb(f,k),e==0&&d(f))};(Rx(b)||Tx(b,"id_token"))&&e++;b.fr()&&e++;(Rx(b)||Tx(b,"id_token"))&&_.sw(a,c,h);b.fr()&&Lw(a,c,h)};
ny=function(a){return function(b){if(!b||b.error)_.Qw(),b?a(b):a({error:"unknown_error"});else{if(b.access_token){var c=_.Nk(b);yy(c);delete c.id_token;delete c.code;_.Pw(c)}a(b)}}};yy=function(a){_.$c(Yw,function(b){delete a[b]})};_.r("gapi.auth2.init",_.iy);_.r("gapi.auth2.authorize",function(a,b){if(Bx!=null)throw new ax("gapi.auth2.authorize cannot be called after GoogleAuth has been initialized (i.e. with a call to gapi.auth2.init, or gapi.client.init when given a 'clientId' and a 'scope' parameters).");_.ly(a,function(c){yy(c);b(c)})});_.r("gapi.auth2._gt",function(){return _.tj()});_.r("gapi.auth2.enableDebugLogs",function(a){a=a!==!1;_.cv=a!="0"&&!!a});_.r("gapi.auth2.getAuthInstance",_.$x);
_.r("gapi.auth2.BasicProfile",Cx);_.r("gapi.auth2.BasicProfile.prototype.getId",Cx.prototype.getId);_.r("gapi.auth2.BasicProfile.prototype.getName",Cx.prototype.Dg);_.r("gapi.auth2.BasicProfile.prototype.getGivenName",Cx.prototype.H8);_.r("gapi.auth2.BasicProfile.prototype.getFamilyName",Cx.prototype.B8);_.r("gapi.auth2.BasicProfile.prototype.getImageUrl",Cx.prototype.P8);_.r("gapi.auth2.BasicProfile.prototype.getEmail",Cx.prototype.oo);_.r("gapi.auth2.GoogleAuth",ay);
_.r("gapi.auth2.GoogleAuth.prototype.attachClickHandler",ay.prototype.aP);_.r("gapi.auth2.GoogleAuth.prototype.disconnect",ay.prototype.disconnect);_.r("gapi.auth2.GoogleAuth.prototype.grantOfflineAccess",ay.prototype.Mu);_.r("gapi.auth2.GoogleAuth.prototype.signIn",ay.prototype.wl);_.r("gapi.auth2.GoogleAuth.prototype.signOut",ay.prototype.xl);_.r("gapi.auth2.GoogleAuth.prototype.getInitialScopes",ay.prototype.QS);_.r("gapi.auth2.GoogleUser",Ax);_.r("gapi.auth2.GoogleUser.prototype.grant",Ax.prototype.D9);
_.r("gapi.auth2.GoogleUser.prototype.getId",Ax.prototype.getId);_.r("gapi.auth2.GoogleUser.prototype.isSignedIn",Ax.prototype.Kg);_.r("gapi.auth2.GoogleUser.prototype.getAuthResponse",Ax.prototype.jm);_.r("gapi.auth2.GoogleUser.prototype.getBasicProfile",Ax.prototype.j8);_.r("gapi.auth2.GoogleUser.prototype.getGrantedScopes",Ax.prototype.J8);_.r("gapi.auth2.GoogleUser.prototype.getHostedDomain",Ax.prototype.tG);_.r("gapi.auth2.GoogleUser.prototype.grantOfflineAccess",Ax.prototype.Mu);
_.r("gapi.auth2.GoogleUser.prototype.hasGrantedScopes",Ax.prototype.gA);_.r("gapi.auth2.GoogleUser.prototype.reloadAuthResponse",Ax.prototype.SJ);_.r("gapi.auth2.LiveValue",nx);_.r("gapi.auth2.LiveValue.prototype.listen",nx.prototype.qa);_.r("gapi.auth2.LiveValue.prototype.get",nx.prototype.get);_.r("gapi.auth2.SigninOptionsBuilder",qx);_.r("gapi.auth2.SigninOptionsBuilder.prototype.getAppPackageName",qx.prototype.h8);_.r("gapi.auth2.SigninOptionsBuilder.prototype.setAppPackageName",qx.prototype.Lea);
_.r("gapi.auth2.SigninOptionsBuilder.prototype.getScope",qx.prototype.Iu);_.r("gapi.auth2.SigninOptionsBuilder.prototype.setScope",qx.prototype.KZ);_.r("gapi.auth2.SigninOptionsBuilder.prototype.getPrompt",qx.prototype.j9);_.r("gapi.auth2.SigninOptionsBuilder.prototype.setPrompt",qx.prototype.ifa);_.r("gapi.auth2.SigninOptionsBuilder.prototype.get",qx.prototype.get);
_.jg=_.jg||{};
(function(){function a(b){var c="";if(b.nodeType==3||b.nodeType==4)c=b.nodeValue;else if(b.innerText)c=b.innerText;else if(b.innerHTML)c=b.innerHTML;else if(b.firstChild){c=[];for(b=b.firstChild;b;b=b.nextSibling)c.push(a(b));c=c.join("")}return c}_.jg.createElement=function(b){if(!document.body||document.body.namespaceURI)try{var c=document.createElementNS("http://www.w3.org/1999/xhtml",b)}catch(d){}return c||document.createElement(b)};_.jg.bQ=function(b){var c=_.jg.createElement("iframe");try{var d=
["<","iframe"],e=b||{},f;for(f in e)e.hasOwnProperty(f)&&(d.push(" "),d.push(f),d.push('="'),d.push(_.jg.EF(e[f])),d.push('"'));d.push("></");d.push("iframe");d.push(">");var h=_.jg.createElement(d.join(""));h&&(!c||h.tagName==c.tagName&&h.namespaceURI==c.namespaceURI)&&(c=h)}catch(l){}d=c;b=b||{};for(var k in b)b.hasOwnProperty(k)&&(d[k]=b[k]);return c};_.jg.vS=function(){if(document.body)return document.body;try{var b=document.getElementsByTagNameNS("http://www.w3.org/1999/xhtml","body");if(b&&
b.length==1)return b[0]}catch(c){}return document.documentElement||document};_.jg.Kpa=function(b){return a(b)}})();
_.Lh=window.gapi&&window.gapi.util||{};
_.Lh=_.Lh={};_.Lh.getOrigin=function(a){return _.Nh(a)};
_.Xy=function(a){if(a.indexOf("GCSC")!==0)return null;var b={Ej:!1};a=a.substr(4);if(!a)return b;var c=a.charAt(0);a=a.substr(1);var d=a.lastIndexOf("_");if(d==-1)return b;var e=_.Vy(a.substr(d+1));if(e==null)return b;a=a.substring(0,d);if(a.charAt(0)!=="_")return b;d=c==="E"&&e.wf;return!d&&(c!=="U"||e.wf)||d&&!_.Wy?b:{Ej:!0,wf:d,E6:a.substr(1),domain:e.domain,cj:e.cj}};_.Yy=function(a,b){this.Xf=a;a=b||{};this.Fba=Number(a.maxAge)||0;this.ke=a.domain;this.Xm=a.path;this.sea=!!a.secure};_.Yy.prototype.read=function(){for(var a=this.Xf+"=",b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c];if(d.indexOf(a)==0)return d.substr(a.length)}};
_.Yy.prototype.write=function(a,b){if(!Zy.test(this.Xf))throw"Invalid cookie name";if(!$y.test(a))throw"Invalid cookie value";a=this.Xf+"="+a;this.ke&&(a+=";domain="+this.ke);this.Xm&&(a+=";path="+this.Xm);b=typeof b==="number"?b:this.Fba;if(b>=0){var c=new Date;c.setSeconds(c.getSeconds()+b);a+=";expires="+c.toUTCString()}this.sea&&(a+=";secure");document.cookie=a;return!0};_.Yy.prototype.clear=function(){this.write("",0)};var $y=/^[-+/_=.:|%&a-zA-Z0-9@]*$/,Zy=/^[A-Z_][A-Z0-9_]{0,63}$/;
_.Yy.iterate=function(a){for(var b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c].split("="),e=d.shift();a(e,d.join("="))}};_.az=function(a){this.Xd=a};_.az.prototype.read=function(){if(bz.hasOwnProperty(this.Xd))return bz[this.Xd]};_.az.prototype.write=function(a){bz[this.Xd]=a;return!0};_.az.prototype.clear=function(){delete bz[this.Xd]};var bz={};_.az.iterate=function(a){for(var b in bz)bz.hasOwnProperty(b)&&a(b,bz[b])};var cz=function(){this.Ca=null;this.key=function(){return null};this.getItem=function(){return this.Ca};this.setItem=function(a,b){this.Ca=b;this.length=1};this.removeItem=function(){this.clear()};this.clear=function(){this.Ca=null;this.length=0};this.length=0},dz=function(a){try{var b=a||window.sessionStorage;if(!b)return!1;b.setItem("gapi.sessionStorageTest","gapi.sessionStorageTest"+b.length);b.removeItem("gapi.sessionStorageTest");return!0}catch(c){return!1}},ez=function(a,b){this.Xf=a;this.OM=
dz(b)?b||window.sessionStorage:new cz};ez.prototype.read=function(){return this.OM.getItem(this.Xf)};ez.prototype.write=function(a){try{this.OM.setItem(this.Xf,a)}catch(b){return!1}return!0};ez.prototype.clear=function(){this.OM.removeItem(this.Xf)};ez.iterate=function(a){if(dz())for(var b=0,c=window.sessionStorage.length;b<c;++b){var d=window.sessionStorage.key(b);a(d,window.sessionStorage[d])}};_.Wy=window.location.protocol==="https:";_.fz=_.Wy||window.location.protocol==="http:"?_.Yy:_.az;_.Vy=function(a){var b=a.substr(1),c="",d=window.location.hostname;if(b!==""){c=parseInt(b,10);if(isNaN(c))return null;b=d.split(".");if(b.length<c-1)return null;b.length==c-1&&(d="."+d)}else d="";return{wf:a.charAt(0)=="S",domain:d,cj:c}};var gz,hz,kz,lz;gz=_.wf();hz=_.wf();_.iz=_.wf();_.jz=_.wf();kz="state code cookie_policy g_user_cookie_policy authuser prompt g-oauth-window status".split(" ");lz=function(a){this.BX=a;this.CI=null};
lz.prototype.write=function(a){var b=_.wf(),c=_.wf(),d=window.decodeURIComponent?decodeURIComponent:unescape,e;for(e in a)if(_.xf(a,e)){var f=a[e];f=f.replace(/\+/g," ");c[e]=d(f);b[e]=a[e]}d=kz.length;for(e=0;e<d;++e)delete c[kz[e]];a=String(a.authuser||0);d=_.wf();d[a]=c;c=_.Vg(d);this.BX.write(c);this.CI=b};lz.prototype.read=function(){return this.CI};lz.prototype.clear=function(){this.BX.clear();this.CI=_.wf()};_.mz=function(a){return a?{domain:a.domain,path:"/",secure:a.wf}:null};
ez.iterate(function(a){var b=_.Xy(a);b&&b.Ej&&(gz[a]=new lz(new ez(a)))});_.fz.iterate(function(a){gz[a]&&(hz[a]=new _.fz(a,_.mz(_.Xy(a))))});
_.fj=function(a,b){var c=_.Gc(b),d=c?b:arguments;for(c=c?0:1;c<d.length;c++){if(a==null)return;a=a[d[c]]}return a};
_.gj=function(a){if(!a||typeof a!=="object")return a;if(typeof a.clone==="function")return a.clone();if(typeof Map!=="undefined"&&a instanceof Map)return new Map(a);if(typeof Set!=="undefined"&&a instanceof Set)return new Set(a);if(a instanceof Date)return new Date(a.getTime());var b=Array.isArray(a)?[]:typeof ArrayBuffer!=="function"||typeof ArrayBuffer.isView!=="function"||!ArrayBuffer.isView(a)||a instanceof DataView?{}:new a.constructor(a.length),c;for(c in a)b[c]=_.gj(a[c]);return b};
_.hj=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,t=0;t<64;t+=4)q[t/4]=p[t]<<24|p[t+1]<<16|p[t+2]<<8|p[t+3];for(t=16;t<80;t++)p=q[t-3]^q[t-8]^q[t-14]^q[t-16],q[t]=(p<<1|p>>>31)&4294967295;p=e[0];var v=e[1],u=e[2],w=e[3],y=e[4];for(t=0;t<80;t++){if(t<40)if(t<20){var D=w^v&(u^w);var C=1518500249}else D=v^u^w,C=1859775393;else t<60?(D=v&u|w&(v|u),C=2400959708):(D=v^u^w,C=3395469782);D=((p<<5|p>>>27)&4294967295)+
D+y+C+q[t]&4294967295;y=w;w=u;u=(v<<30|v>>>2)&4294967295;v=p;p=D}e[0]=e[0]+p&4294967295;e[1]=e[1]+v&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+w&4294967295;e[4]=e[4]+y&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var t=[],v=0,u=p.length;v<u;++v)t.push(p.charCodeAt(v));p=t}q||(q=p.length);t=0;if(m==0)for(;t+64<q;)b(p.slice(t,t+64)),t+=64,n+=64;for(;t<q;)if(f[m++]=p[t++],n++,m==64)for(m=0,b(f);t+64<q;)b(p.slice(t,t+64)),t+=64,n+=64}function d(){var p=[],
q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var t=63;t>=56;t--)f[t]=q&255,q>>>=8;b(f);for(t=q=0;t<5;t++)for(var v=24;v>=0;v-=8)p[q++]=e[t]>>v&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Zi:function(){for(var p=d(),q="",t=0;t<p.length;t++)q+="0123456789ABCDEF".charAt(Math.floor(p[t]/16))+"0123456789ABCDEF".charAt(p[t]%16);return q}}};var jj=function(a,b,c){var d=String(_.Sa.location.href);return d&&a&&b?[b,ij(_.Nh(d),a,c||null)].join(" "):null},ij=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],_.$c(d,function(k){e.push(k)}),kj(e.join(" "));var f=[],h=[];_.$c(c,function(k){h.push(k.key);f.push(k.value)});c=Math.floor((new Date).getTime()/1E3);e=f.length==0?[c,b,a]:[f.join(":"),c,b,a];_.$c(d,function(k){e.push(k)});a=kj(e.join(" "));a=[c,a];h.length==0||a.push(h.join(""));return a.join("_")},kj=function(a){var b=
_.hj();b.update(a);return b.Zi().toLowerCase()};_.lj={};var oj;_.mj=function(a){return!!_.lj.FPA_SAMESITE_PHASE2_MOD||!(a===void 0||!a)};_.nj=function(a){a=a===void 0?!1:a;var b=_.Sa.__SAPISID||_.Sa.__APISID||_.Sa.__3PSAPISID||_.Sa.__OVERRIDE_SID;_.mj(a)&&(b=b||_.Sa.__1PSAPISID);if(b)return!0;if(typeof document!=="undefined"){var c=new _.cj(document);b=c.get("SAPISID")||c.get("APISID")||c.get("__Secure-3PAPISID");_.mj(a)&&(b=b||c.get("__Secure-1PAPISID"))}return!!b};
oj=function(a,b,c,d){(a=_.Sa[a])||typeof document==="undefined"||(a=(new _.cj(document)).get(b));return a?jj(a,c,d):null};
_.pj=function(a,b){b=b===void 0?!1:b;var c=_.Nh(String(_.Sa.location.href)),d=[];if(_.nj(b)){c=c.indexOf("https:")==0||c.indexOf("chrome-extension:")==0||c.indexOf("chrome-untrusted://new-tab-page")==0||c.indexOf("moz-extension:")==0;var e=c?_.Sa.__SAPISID:_.Sa.__APISID;e||typeof document==="undefined"||(e=new _.cj(document),e=e.get(c?"SAPISID":"APISID")||e.get("__Secure-3PAPISID"));(e=e?jj(e,c?"SAPISIDHASH":"APISIDHASH",a):null)&&d.push(e);c&&_.mj(b)&&((b=oj("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",
a))&&d.push(b),(a=oj("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",a))&&d.push(a))}return d.length==0?null:d.join(" ")};
_.qj=function(a,b){var c={SAPISIDHASH:!0,SAPISID3PHASH:!0,APISIDHASH:!0};_.mj(b===void 0?!1:b)&&(c.SAPISID1PHASH=!0);return a&&(a.OriginToken||a.Authorization&&c[String(a.Authorization).split(" ")[0]])?!0:!1};_.rj={UT:_.qj,Xaa:_.nj,TS:function(){var a=null;_.nj()&&(a=window.__PVT,a==null&&(a=(new _.cj(document)).get("BEAT")));return a},tS:_.pj};
var Js,Ks;_.Cs=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.Ds=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};_.g=_.Ds.prototype;_.g.clone=function(){return new _.Ds(this.x,this.y)};_.g.equals=function(a){return a instanceof _.Ds&&_.Cs(this,a)};_.g.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};_.g.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};
_.g.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.g.translate=function(a,b){a instanceof _.Ds?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};_.g.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};_.Es=function(a){return a.scrollingElement?a.scrollingElement:!_.le&&_.We(a)?a.documentElement:a.body||a.documentElement};
_.Fs=function(a){var b=_.Es(a);a=a.parentWindow||a.defaultView;return new _.Ds(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)};_.Gs=function(a,b,c,d){return _.Re(a.Rb,b,c,d)};_.Hs=function(a){if(a instanceof _.ud&&a.constructor===_.ud)return a.UX;_.Fc(a);return"type_error:SafeStyle"};_.Is=function(a){if(a instanceof _.Bd&&a.constructor===_.Bd)return a.TX;_.Fc(a);return"type_error:SafeStyleSheet"};Js=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};
Ks=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};_.Ls=function(a,b,c){return _.af(document,arguments)};_.Ms=function(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d};_.g=_.Ms.prototype;_.g.Xb=function(){return this.right-this.left};_.g.Qc=function(){return this.bottom-this.top};_.g.clone=function(){return new _.Ms(this.top,this.right,this.bottom,this.left)};_.g.contains=function(a){return this&&a?a instanceof _.Ms?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
_.g.expand=function(a,b,c,d){_.yc(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};_.g.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
_.g.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};_.g.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
_.g.translate=function(a,b){a instanceof _.Ds?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,typeof b==="number"&&(this.top+=b,this.bottom+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};var Ps,Ns,Ts,Vs;_.Os=function(a,b,c){if(typeof b==="string")(b=Ns(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=Ns(c,d);f&&(c.style[f]=e)}};Ps={};Ns=function(a,b){var c=Ps[b];if(!c){var d=Js(b);c=d;a.style[d]===void 0&&(d=(_.le?"Webkit":_.ke?"Moz":_.he?"ms":null)+Ks(d),a.style[d]!==void 0&&(c=d));Ps[b]=c}return c};_.Qs=function(a,b){var c=a.style[Js(b)];return typeof c!=="undefined"?c:a.style[Ns(a,b)]||""};
_.Rs=function(a,b){var c=_.Oe(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""};_.Ss=function(a,b){return _.Rs(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]};Ts=function(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}};
_.Ws=function(a,b){b=b||_.Es(document);var c=b||_.Es(document);var d=_.Us(a),e=_.Us(c),f=_.Rs(c,"borderLeftWidth");var h=_.Rs(c,"borderRightWidth");var k=_.Rs(c,"borderTopWidth"),l=_.Rs(c,"borderBottomWidth");h=new _.Ms(parseFloat(k),parseFloat(h),parseFloat(l),parseFloat(f));c==_.Es(document)?(f=d.x-c.scrollLeft,d=d.y-c.scrollTop):(f=d.x-e.x-h.left,d=d.y-e.y-h.top);a=Vs(a);e=c.clientHeight-a.height;h=c.scrollLeft;k=c.scrollTop;h+=Math.min(f,Math.max(f-(c.clientWidth-a.width),0));k+=Math.min(d,Math.max(d-
e,0));c=new _.Ds(h,k);b.scrollLeft=c.x;b.scrollTop=c.y};_.Us=function(a){var b=_.Oe(a),c=new _.Ds(0,0);if(a==(b?_.Oe(b):document).documentElement)return c;a=Ts(a);b=_.Fs(_.Pe(b).Rb);c.x=a.left+b.x;c.y=a.top+b.y;return c};_.Ys=function(a,b){var c=new _.Ds(0,0),d=_.Ye(_.Oe(a));if(!_.fe(d,"parent"))return c;do{var e=d==b?_.Us(a):_.Xs(a);c.x+=e.x;c.y+=e.y}while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};_.Xs=function(a){a=Ts(a);return new _.Ds(a.left,a.top)};
_.$s=function(a,b,c){if(b instanceof _.Je)c=b.height,b=b.width;else if(c==void 0)throw Error("R");a.style.width=_.Zs(b,!0);a.style.height=_.Zs(c,!0)};_.Zs=function(a,b){typeof a=="number"&&(a=(b?Math.round(a):a)+"px");return a};_.at=function(a){var b=Vs;if(_.Ss(a,"display")!="none")return b(a);var c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a};
Vs=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=_.le&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=Ts(a),new _.Je(a.right-a.left,a.bottom-a.top)):new _.Je(b,c)};_.bt=function(a,b){a.style.display=b?"":"none"};_.dt=function(a){var b=_.Pe(void 0),c=_.Gs(b,"HEAD")[0];if(!c){var d=_.Gs(b,"BODY")[0];c=b.wa("HEAD");d.parentNode.insertBefore(c,d)}d=b.wa("STYLE");var e;(e=_.Ie('style[nonce],link[rel="stylesheet"][nonce]'))&&d.setAttribute("nonce",e);_.ct(d,a);b.appendChild(c,d)};
_.ct=function(a,b){b=_.Is(b);_.Sa.trustedTypes?_.lf(a,b):a.innerHTML=b};_.et=_.ke?"MozUserSelect":_.le||_.ie?"WebkitUserSelect":null;
_.Cy=function(a,b){_.Ak.call(this);this.Lm=a||1;this.Sw=b||_.Sa;this.nP=(0,_.z)(this.gga,this);this.EV=_.Jc()};_.bb(_.Cy,_.Ak);_.g=_.Cy.prototype;_.g.enabled=!1;_.g.Nc=null;_.g.setInterval=function(a){this.Lm=a;this.Nc&&this.enabled?(this.stop(),this.start()):this.Nc&&this.stop()};
_.g.gga=function(){if(this.enabled){var a=_.Jc()-this.EV;a>0&&a<this.Lm*.8?this.Nc=this.Sw.setTimeout(this.nP,this.Lm-a):(this.Nc&&(this.Sw.clearTimeout(this.Nc),this.Nc=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}};_.g.start=function(){this.enabled=!0;this.Nc||(this.Nc=this.Sw.setTimeout(this.nP,this.Lm),this.EV=_.Jc())};_.g.stop=function(){this.enabled=!1;this.Nc&&(this.Sw.clearTimeout(this.Nc),this.Nc=null)};_.g.va=function(){_.Cy.N.va.call(this);this.stop();delete this.Sw};
_.Dy=function(a,b,c){if(typeof a==="function")c&&(a=(0,_.z)(a,c));else if(a&&typeof a.handleEvent=="function")a=(0,_.z)(a.handleEvent,a);else throw Error("Ba");return Number(b)>2147483647?-1:_.Sa.setTimeout(a,b||0)};_.Ey=function(a){_.Sa.clearTimeout(a)};
_.nz=function(a){_.Oj.call(this);this.Xd=1;this.GB=[];this.LB=0;this.hg=[];this.dk={};this.U5=!!a};_.bb(_.nz,_.Oj);_.g=_.nz.prototype;_.g.subscribe=function(a,b,c){var d=this.dk[a];d||(d=this.dk[a]=[]);var e=this.Xd;this.hg[e]=a;this.hg[e+1]=b;this.hg[e+2]=c;this.Xd=e+3;d.push(e);return e};_.g.Hw=_.gb(18);_.g.unsubscribe=function(a,b,c){if(a=this.dk[a]){var d=this.hg;if(a=a.find(function(e){return d[e+1]==b&&d[e+2]==c}))return this.El(a)}return!1};
_.g.El=function(a){var b=this.hg[a];if(b){var c=this.dk[b];this.LB!=0?(this.GB.push(a),this.hg[a+1]=function(){}):(c&&_.ub(c,a),delete this.hg[a],delete this.hg[a+1],delete this.hg[a+2])}return!!b};
_.g.zp=function(a,b){var c=this.dk[a];if(c){for(var d=Array(arguments.length-1),e=1,f=arguments.length;e<f;e++)d[e-1]=arguments[e];if(this.U5)for(e=0;e<c.length;e++){var h=c[e];oz(this.hg[h+1],this.hg[h+2],d)}else{this.LB++;try{for(e=0,f=c.length;e<f&&!this.isDisposed();e++)h=c[e],this.hg[h+1].apply(this.hg[h+2],d)}finally{if(this.LB--,this.GB.length>0&&this.LB==0)for(;c=this.GB.pop();)this.El(c)}}return e!=0}return!1};var oz=function(a,b,c){_.dl(function(){a.apply(b,c)})};
_.nz.prototype.clear=function(a){if(a){var b=this.dk[a];b&&(b.forEach(this.El,this),delete this.dk[a])}else this.hg.length=0,this.dk={}};_.nz.prototype.Sb=function(a){if(a){var b=this.dk[a];return b?b.length:0}a=0;for(b in this.dk)a+=this.Sb(b);return a};_.nz.prototype.va=function(){_.nz.N.va.call(this);this.clear();this.GB.length=0};
_.pz=function(a){this.jga=a};_.pz.prototype.toString=function(){return this.jga};_.qz=function(a){_.Oj.call(this);this.Ae=new _.nz(a);_.Qj(this,this.Ae)};_.bb(_.qz,_.Oj);_.g=_.qz.prototype;_.g.subscribe=function(a,b,c){return this.Ae.subscribe(a.toString(),b,c)};_.g.Hw=_.gb(17);_.g.unsubscribe=function(a,b,c){return this.Ae.unsubscribe(a.toString(),b,c)};_.g.El=function(a){return this.Ae.El(a)};_.g.zp=function(a,b){return this.Ae.zp(a.toString(),b)};_.g.clear=function(a){this.Ae.clear(a!==void 0?a.toString():void 0)};
_.g.Sb=function(a){return this.Ae.Sb(a!==void 0?a.toString():void 0)};
var rz=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.rb.call(this,c+a[d])},sz=function(a){for(var b=!0,c=/^[-_a-zA-Z0-9]$/,d=0;d<a.length;d++){var e=a.charAt(d);if(e=="]"){if(b)return!1;b=!0}else if(e=="["){if(!b)return!1;b=!1}else if(!b&&!c.test(e))return!1}return b},tz=function(a){return a.replace(_.xd,function(b,c,d,e){var f="";d=d.replace(/^(['"])(.*)\1$/,function(h,k,l){f=k;return l});b=_.Pb(d).toString();return c+f+b+f+e})},vz=function(a){if(a instanceof
_.Jb)return'url("'+a.toString().replace(/</g,"%3c").replace(/[\\"]/g,"\\$&")+'")';if(a instanceof _.Oc)a=_.Pc(a);else{a=String(a);var b=a.replace(_.yd,"$1").replace(_.yd,"$1").replace(_.xd,"url");if(_.wd.test(b)){if(b=!uz.test(a)){for(var c=b=!0,d=0;d<a.length;d++){var e=a.charAt(d);e=="'"&&c?b=!b:e=='"'&&b&&(c=!c)}b=b&&c&&sz(a)}a=b?tz(a):"zClosurez"}else a="zClosurez"}if(/[{;}]/.test(a))throw new rz("Value does not allow [{;}], got: %s.",[a]);return a},wz,uz,yz,zz,Az,Bz,Cz;_.bb(rz,_.rb);
rz.prototype.name="AssertionError";wz={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0};uz=/\/\*/;_.xz=function(a){var b="",c;for(c in a)if(Object.prototype.hasOwnProperty.call(a,c)){if(!/^[-_a-zA-Z0-9]+$/.test(c))throw Error("n`"+c);var d=a[c];d!=null&&(d=Array.isArray(d)?d.map(vz).join(" "):vz(d),b+=c+":"+d+";")}return b?new _.ud(b,_.td):_.vd};yz=/^[a-zA-Z0-9-]+$/;
zz={APPLET:!0,BASE:!0,EMBED:!0,IFRAME:!0,LINK:!0,MATH:!0,META:!0,OBJECT:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0};Az=function(a){var b=_.Fd(_.Id),c=[],d=function(e){Array.isArray(e)?e.forEach(d):(e=_.Fd(e),c.push(_.wc(e).toString()))};a.forEach(d);return _.Vb(c.join(_.wc(b).toString()))};Bz=function(a){return Az(Array.prototype.slice.call(arguments))};Cz={action:!0,cite:!0,data:!0,formaction:!0,href:!0,manifest:!0,poster:!0,src:!0};
_.Dz=function(a,b){Array.isArray(b)||(b=[b]);b=b.map(function(c){return typeof c==="string"?c:c.yp+" "+c.duration+"s "+c.timing+" "+c.delay+"s"});_.Os(a,"transition",b.join(","))};
_.Ez=_.Yc(function(){var a=_.bf("DIV"),b=_.le?"-webkit":_.ke?"-moz":_.he?"-ms":null,c={transition:"opacity 1s linear"};b&&(c[b+"-transition"]="opacity 1s linear");c={style:c};if(!yz.test("div"))throw Error("m");if("DIV"in zz)throw Error("m");b=void 0;var d="";if(c)for(h in c)if(Object.prototype.hasOwnProperty.call(c,h)){if(!yz.test(h))throw Error("m");var e=c[h];if(e!=null){var f=h;if(e instanceof _.Oc)e=_.Pc(e);else if(f.toLowerCase()=="style"){if(!_.yc(e))throw Error("m");e instanceof _.ud||(e=
_.xz(e));e=_.Hs(e)}else{if(/^on/i.test(f))throw Error("m");if(f.toLowerCase()in Cz)if(e instanceof _.Tc)e=_.Uc(e).toString();else if(e instanceof _.Jb)e=_.Kb(e);else if(typeof e==="string")e=_.Pb(e).toString();else throw Error("m");}f=f+'="'+_.pd(String(e))+'"';d+=" "+f}}var h="<div"+d;b==null?b=[]:Array.isArray(b)||(b=[b]);wz.div===!0?h+=">":(b=Bz(b),h+=">"+_.wc(b).toString()+"</div>");h=_.Vb(h);_.Ge(a,h);return _.Qs(a.firstChild,"transition")!=""});
_.Gz=function(){_.Fz="oauth2relay"+String(2147483647*(0,_.Th)()|0)};_.Hz=new _.qz;_.Iz=new _.pz("oauth");_.Gz();_.Sf("oauth-flow/client_id");var Jz=String(_.Sf("oauth-flow/redirectUri"));if(Jz)Jz.replace(/[#][\s\S]*/,"");else{var Kz=_.Lh.getOrigin(window.location.href);_.Sf("oauth-flow/callbackUrl");encodeURIComponent(Kz)}_.Lh.getOrigin(window.location.href);
var Mz,Nz,Oz,Pz,Qz,Rz,Sz,Tz,Uz,Vz,Wz,Yz,Zz,$z,aA,bA,cA,dA,eA,fA,gA,hA,iA,jA,kA,lA,mA,nA,oA,pA,qA,rA,sA,tA,uA,vA,wA,xA,yA,zA,CA,BA,DA,EA,FA,GA,HA,IA,JA,KA,LA,NA;_.Lz=function(a,b){if(_.Hi&&!b)return _.Sa.atob(a);var c="";_.Ki(a,function(d){c+=String.fromCharCode(d)});return c};Mz=function(a){var b=String(a("immediate")||"");a=String(a("prompt")||"");return b==="true"||a==="none"};Nz=function(a){return _.Zi("enableMultilogin")&&a("cookie_policy")&&!Mz(a)?!0:!1};
Qz=function(){var a,b=null;_.fz.iterate(function(c,d){c.indexOf("G_AUTHUSER_")===0&&(c=c.substring(11),c=_.Vy(c),!a||c.wf&&!a.wf||c.wf==a.wf&&c.cj>a.cj)&&(a=c,b=d)});return{g6:a,authuser:b}};Rz=[".APPS.GOOGLEUSERCONTENT.COM","@DEVELOPER.GSERVICEACCOUNT.COM"];Sz=function(a){a=a.toUpperCase();for(var b=0,c=Rz.length;b<c;++b){var d=a.split(Rz[b]);d.length==2&&d[1]===""&&(a=d[0])}a=a.replace(/-/g,"_").toUpperCase();a.length>40&&(b=new _.Sh,b.jx(a),a=b.Zi().toUpperCase());return a};
Tz=function(a){if(!a)return[];a=a.split("=");return a[1]?a[1].split("|"):[]};Uz=function(a){a=a.split(":");return{clientId:a[0].split("=")[1],Jea:Tz(a[1]),Bqa:Tz(a[2]),upa:Tz(a[3])}};Vz=function(a){var b=Qz(),c=b.g6;b=b.authuser;var d=a&&Sz(a);if(b!==null){var e;_.fz.iterate(function(h,k){(h=_.Xy(h))&&h.Ej&&(d&&h.E6!=d||h.wf==c.wf&&h.cj==c.cj&&(e=k))});if(e){var f=Uz(e);a=f&&f.Jea[Number(b)];f=f&&f.clientId;if(a)return{authuser:b,zra:a,clientId:f}}}return null};
Wz=function(a,b){a=_.tj(a);if(!a||!b&&a.error)return null;b=Math.floor((new Date).getTime()/1E3);return a.expires_at&&b>a.expires_at?null:a};_.Xz=function(a,b){if(b){var c=b;var d=a}else typeof a==="string"?d=a:c=a;c?_.Pw(c,d):_.Qw(d)};
Yz=function(a){if(!a)return null;a!=="single_host_origin"&&(a=_.Nh(a));var b=window.location.hostname,c=b,d=_.Wy;if(a!=="single_host_origin"){c=a.split("://");if(c.length==2)d=c.shift()==="https";else return _.Zg.log("WARNING invalid cookie_policy: "+a),null;c=c[0]}if(c.indexOf(":")!==-1)c=b="";else{a="."+c;if(b.lastIndexOf(a)!==b.length-a.length)return _.Zg.log("Invalid cookie_policy domain: "+c),null;c=a;b=c.split(".").length-1}return{domain:c,wf:d,cj:b}};
Zz=function(a){var b=Yz(a);if(!b)return new _.az("G_USERSTATE_");a=["G_USERSTATE_",_.Wy&&b.wf?"S":"H",b.cj].join("");var c=_.jz[a];c||(c={AI:63072E3},_.Af(_.mz(b),c),c=new _.Yy(a,c),_.jz[a]=c,b=c.read(),typeof b!=="undefined"&&b!==null&&(document.cookie=a+"=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/",c.write(b)));return c};$z=function(a){var b=Zz(a).read();a=_.wf();if(b){b=b.split(":");for(var c;c=b.shift();)c=c.split("="),a[c[0]]=c[1]}return a};
aA=function(a,b,c){var d=$z(b),e=d[a];d[a]="0";var f=[];_.vn(d,function(k,l){f.push(l+"="+k)});var h=f.join(":");b=Zz(b);h?b.write(h):b.clear();d[a]!==e&&c&&c()};bA=function(a){a=Yz(a.g_user_cookie_policy);if(!a||a.wf&&!_.Wy)a=null;else{var b=["G_AUTHUSER_",_.Wy&&a.wf?"S":"H",a.cj].join(""),c=_.iz[b];c||(c=new _.fz(b,_.mz(a)),_.iz[b]=c);a=c}_.Tf("googleapis.config/sessionIndex",null);a.clear()};cA=function(a){return Mz(function(b){return a[b]})};dA=0;eA=!1;fA=[];gA={};hA={};iA=null;
jA=function(a){var b=_.Fz;return function(c){if(this.f==b&&this.t==_.dh.ko(this.f)&&this.origin==_.dh.Do(this.f))return a.apply(this,arguments)}};kA=function(a){if(a&&!decodeURIComponent(a).startsWith("m;/_/scs/"))throw Error("Ga");};lA=function(a){var b=_.jg.kh,c=b(a).jsh;if(c!=null)return kA(c),a;if(b=String(b().jsh||_.If.h||""))kA(b),c=(a+"#").indexOf("#"),a=a.substr(0,c)+(a.substr(0,c).indexOf("?")!==-1?"&":"?")+"jsh="+encodeURIComponent(b)+a.substr(c);return a};mA=function(){return!!_.Sf("oauth-flow/usegapi")};
nA=function(a,b){mA()?iA.unregister(a):_.dh.unregister(a+":"+b)};oA=function(a,b,c){mA()?iA.register(a,c,_.Kn):_.dh.register(a+":"+b,jA(c))};pA=function(){Oz.parentNode.removeChild(Oz)};qA=function(a){var b=Oz;_.Dz(b,[{yp:"-webkit-transform",duration:1,timing:"ease",delay:0}]);_.Dz(b,[{yp:"transform",duration:1,timing:"ease",delay:0}]);_.Dy(function(){b.style.webkitTransform="translate3d(0px,"+a+"px,0px)";b.style.transform="translate3d(0px,"+a+"px,0px)"},0)};rA=function(){var a=Pz+88;qA(a);Pz=a};
sA=function(){var a=Pz-88;qA(a);Pz=a};tA=function(a){var b=a?rA:sA,c=a?sA:rA;a=a?"-":"";Pz=parseInt(a+88,10);Oz.style.webkitTransform="translate3d(0px,"+a+88+"px,0px)";Oz.style.transform="translate3d(0px,"+a+88+"px,0px)";Oz.style.display="";Oz.style.visibility="visible";b();_.Dy(c,4E3);_.Dy(pA,5E3)};
uA=function(a){var b=_.Sf("oauth-flow/toast/position");b!=="top"&&(b="bottom");var c=document.createElement("div");Oz=c;c.style.cssText="position:fixed;left:0px;z-index:1000;width:100%;";_.Os(c,"visibility","hidden");_.Os(c,b,"-40px");_.Os(c,"height","128px");var d=c;if(_.js()){d=document.createElement("div");d.style.cssText="float:left;position:relative;left:50%;";c.appendChild(d);var e=document.createElement("div");e.style.cssText="float:left;position:relative;left:-50%";d.appendChild(e);d=e}e=
b=="top"?"-":"";Pz=parseInt(e+88,10);Oz.style.webkitTransform="translate3d(0px,"+e+88+"px,0px)";Oz.style.transform="translate3d(0px,"+e+88+"px,0px)";e=window;try{for(;e.parent!=e&&e.parent.document;)e=e.parent}catch(f){}e=e.document.body;try{e.insertBefore(c,e.firstChild)}catch(f){}_.Hn.openChild({url:":socialhost:/:session_prefix:_/widget/oauthflow/toast",queryParams:{clientId:a.client_id,idToken:a.id_token},where:d,onRestyle:function(){b==="top"?tA(!0):tA(!1)}})};
vA=function(a){var b=_.op(),c=b&&b.scope;b=a&&a.scope;b=typeof b==="string"?b.split(" "):b||[];if(c){c=c.split(" ");for(var d=0;d<c.length;++d){var e=c[d];_.tn.call(b,e)==-1&&b.push(e)}b.length>0&&(a.scope=b.join(" "))}return a};
wA=function(a,b){var c=null;a&&b&&(c=b.client_id=b.client_id||a.client_id,b.scope=b.scope||a.scope,b.g_user_cookie_policy=a.cookie_policy,b.cookie_policy=b.cookie_policy||a.cookie_policy,b.response_type=b.response_type||a.response_type);if(b){b.issued_at||(b.issued_at=String(Math.floor((new Date).getTime()/1E3)));var d=parseInt(b.expires_in,10)||86400;b.error&&(d=_.Sf("oauth-flow/errorMaxAge")||86400);b.expires_in=String(d);b.expires_at||(b.expires_at=String(Math.floor((new Date).getTime()/1E3)+d));
b._aa||b.error||Vz(c)!=null||!cA(a)||(b._aa="1");a=b.status={};a.google_logged_in=!!b.session_state;c=a.signed_in=!!b.access_token;a.method=c?b["g-oauth-window"]?"PROMPT":"AUTO":null}return b};xA=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+)\.?\.?\.?$/,"$1");a=_.Ug(_.Lz(a,!0));if(a===!1)throw Error("Ha");return a};yA=function(a){return(a=xA(a))?a.sub:null};
zA=function(a){a&&fA.push(a);a=_.Fz;var b=document.getElementById(a),c=(new Date).getTime();if(b){if(dA&&c-dA<6E4)return;var d=_.dh.ko(a);d&&(nA("oauth2relayReady",d),nA("oauth2callback",d));b.parentNode.removeChild(b);if(/Firefox/.test(navigator.userAgent))try{window.frames[a]=void 0}catch(f){}_.Gz();a=_.Fz}dA=c;var e=String(2147483647*(0,_.Th)()|0);b=_.Sf("oauth-flow/proxyUrl")||_.Sf("oauth-flow/relayUrl");mA()?iA=_.Hn.openChild({where:_.jg.vS(),url:b,id:a,attributes:{style:{width:"1px",height:"1px",
position:"absolute",top:"-100px",display:"none"},"aria-hidden":"true"},dontclear:!0}):(b=[b,"?parent=",encodeURIComponent(_.Lh.getOrigin(window.location.href)),"#rpctoken=",e,"&forcesecure=1"].join(""),c=_.jg.vS(),d=_.jg.bQ({name:a,id:a}),d.src=lA(b),d.style.width="1px",d.style.height="1px",d.style.position="absolute",d.style.top="-100px",d.tabIndex=-1,d.setAttribute("aria-hidden","true"),c.appendChild(d),_.dh.xw(a));oA("oauth2relayReady",e,function(){nA("oauth2relayReady",e);var f=fA;if(f!==null){fA=
null;for(var h=f.length,k=0;k<h;++k)f[k]()}});oA("oauth2callback",e,function(f){var h=_.jg.kh;h=h(f);var k=h.state;f=k.replace(/\|.*$/,"");f={}.hasOwnProperty.call(hA,f)?hA[f]:null;h.state=f;if(h.state!=null){f=gA[k];delete gA[k];k=f&&f.key||"token";var l=h=wA(f&&f.params,h),m=yA(l);if(m){var n=$z(l.cookie_policy);m=n[m]=="0"||n[m]=="X"}else m=!1;!m&&l&&(" "+(l.scope||"")+" ").indexOf(" https://www.googleapis.com/auth/plus.login ")>=0&&_.Sf("isLoggedIn")&&(l&&l._aa)==="1"&&(l._aa="0",eA||(eA=!0,uA(l)));
_.Xz(k,h);h=Wz(k);if(f){k=f.popup;l=f.after_redirect;if(k&&"keep_open"!=l)try{k.close()}catch(p){}f.callback&&(f.callback(h),f.callback=null)}}})};_.AA=function(a){fA!==null?zA(a):a&&a()};CA=function(a,b){var c=BA,d=yA(a);d&&(bA(a),aA(d,b,function(){if(c){var e={error:"user_signed_out"};e.client_id=a.client_id;e.g_user_cookie_policy=a.g_user_cookie_policy;e.scope=a.scope;e.response_type=a.response_type;e.session_state=a.session_state;e=wA(null,e);c(e)}}))};
BA=function(a){a||(a=Wz(void 0,!0));a&&typeof a==="object"||(a={error:"invalid_request",error_description:"no callback data"});var b=a.error_description;b&&window.console&&(window.console.error(a.error),window.console.error(b));a.error||(_.If.drw=null);_.Xz(a);if(b=a.authuser)_.Sf("googleapis.config/sessionIndex"),_.Tf("googleapis.config/sessionIndex",b);_.Hz.zp(_.Iz,a);return a};DA=["client_id","cookie_policy","response_type"];EA="client_id response_type login_hint authuser prompt include_granted_scopes after_redirect access_type hl state".split(" ");
FA=function(a){var b=_.Nk(a);b.session_state&&b.session_state.extraQueryParams&&(b.authuser=b.session_state.extraQueryParams.authuser);b.session_state=null;a.expires_at&&(b.expires_at=parseInt(a.expires_at/1E3).toString());a.expires_in&&(b.expires_in=a.expires_in.toString());a.first_issued_at&&(b.issued_at=parseInt(a.first_issued_at/1E3).toString(),delete b.first_issued_at);_.Pw(b);return b};
GA=function(a){if(a.include_granted_scopes===void 0){var b=_.Sf("include_granted_scopes");a.include_granted_scopes=!!b}};HA=function(a){window.console&&(typeof window.console.warn==="function"?window.console.warn(a):typeof window.console.log==="function"&&window.console.log(a))};
IA=function(a){var b=a||{},c={};_.$c(EA,function(d){b[d]!=null&&(c[d]=b[d])});a=_.Sf("googleapis/overrideClientId");a!=null&&(c.client_id=a);GA(c);typeof b.scope==="string"?c.scope=b.scope:Array.isArray(b.scope)&&(c.scope=b.scope.join(" "));b["openid.realm"]!=null&&(c.openid_realm=b["openid.realm"]);b.cookie_policy!=null?c.cookie_policy=b.cookie_policy:b.cookiepolicy!=null&&(c.cookie_policy=b.cookiepolicy);c.login_hint==null&&b.user_id!=null&&(c.login_hint=b.user_id);try{_.Gx(c.cookie_policy)}catch(d){c.cookie_policy&&
HA("The cookie_policy configuration: '"+c.cookie_policy+"' is illegal, and thus ignored."),delete c.cookie_policy}b.hd!=null&&(c.hosted_domain=b.hd);c.prompt==null&&(b.immediate==1||b.immediate=="true"?c.prompt="none":b.approval_prompt=="force"&&(c.prompt="consent"));c.prompt=="none"&&(c.session_selection="first_valid");c.prompt=="none"&&c.access_type=="offline"&&delete c.access_type;typeof c.authuser==="undefined"&&(a=_.bj(),a!=null&&(c.authuser=a));a=b.redirect_uri||_.Sf("oauth-flow/redirectUri");
a!=null&&a!="postmessage"&&(c.redirect_uri=a);c.gsiwebsdk="shim";return c};
JA=function(a,b){var c=IA(a),d=new _.gl(function(e,f){_.ly(c,function(h){var k=h||{};_.$c(DA,function(l){k[l]==null&&(k[l]=c[l])});!c.include_granted_scopes&&a&&a.scope&&(k.scope=a.scope);a&&a.state!=null&&(k.state=a.state);k.error?(c.prompt=="none"&&k.error=="user_logged_out"&&(k.error="immediate_failed_user_logged_out"),f(k)):(h=FA(k),h.authuser!=null&&_.Tf("googleapis.config/sessionIndex",h.authuser),e(h))})});b&&d.then(b,b);return d};KA=_.rj.tS;LA=null;
_.OA=function(a,b){if(a.approvalprompt!=="force"){a=_.MA(a);a.prompt="none";delete a.redirect_uri;delete a.approval_prompt;delete a.immediate;if(b=!b)LA?(a.client_id!==LA.client_id&&window.console&&window.console.log&&window.console.log("Ignoring mismatched page-level auth param client_id="+a.client_id),b=!0):(LA=a,b=!1);b||NA(a)}};
_.MA=function(a){var b=a.redirecturi||"postmessage",c=(0,_.hd)((a.scope||"").replace(/[\s\xa0]+/g," "));b={client_id:a.clientid,redirect_uri:b,response_type:"code token id_token gsession",scope:c};a.approvalprompt&&(b.approval_prompt=a.approvalprompt);a.state&&(b.state=a.state);a.openidrealm&&(b["openid.realm"]=a.openidrealm);c=a.accesstype=="offline"?!0:(c=a.redirecturi)&&c!="postmessage";c&&(b.access_type="offline");a.requestvisibleactions&&(b.request_visible_actions=(0,_.hd)(a.requestvisibleactions.replace(/[\s\xa0]+/g,
" ")));a.after_redirect&&(b.after_redirect=a.after_redirect);a.cookiepolicy&&a.cookiepolicy!=="none"&&(b.cookie_policy=a.cookiepolicy);typeof a.includegrantedscopes!="undefined"&&(b.include_granted_scopes=a.includegrantedscopes);a.e&&(b.e=a.e);(a=a.authuser||_.Sf("googleapis.config/sessionIndex"))&&(b.authuser=a);(a=_.Sf("useoriginassocialhost"))&&(b.use_origin_as_socialhost=a);return b};NA=function(a){_.Yp("waaf0","signin","0");JA(a,function(b){_.Yp("waaf1","signin","0");BA(b)})};
_.PA=function(a){a=_.MA(a);_.Tf("oauth-flow/authWindowWidth",445);_.Tf("oauth-flow/authWindowHeight",615);NA(a)};_.QA=function(a){_.Hz.unsubscribe(_.Iz,a);_.Hz.subscribe(_.Iz,a)};var XA,$A;_.SA=function(a){return a.cookiepolicy?!0:(_.RA("cookiepolicy is a required field.  See https://developers.google.com/+/web/signin/#button_attr_cookiepolicy for more information."),!1)};_.RA=function(a){window.console&&(window.console.error?window.console.error(a):window.console.log&&window.console.log(a))};_.WA=function(a,b){var c=_.op();_.Af(a,c);c=vA(c);if(_.SA(c)){var d=_.TA();_.UA(c);b?_.Hf(b,"click",function(){_.VA(c,d)}):_.VA(c,d)}};
_.TA=function(){var a=new XA;_.QA(function(b){a.BI&&b&&(b.access_token&&_.Tf("isPlusUser",!0),b["g-oauth-window"]&&(a.BI=!1,_.Zg.warn("OTA app install is no longer supported.")))});return a};XA=function(){this.BI=!1};_.UA=function(a){a=_.YA(a);_.ZA(a.callback);_.AA(function(){_.OA(a)})};_.YA=function(a){$A(a);a.redirecturi&&delete a.redirecturi;Nz(function(b){return a[b]})||(a.authuser=0);return a};$A=function(a){/^\s*$/.test(a.scope||"")&&(a.scope="https://www.googleapis.com/auth/plus.login")};
_.ZA=function(a){if(typeof a==="string")if(window[a])a=window[a];else{_.RA('Callback function named "'+a+'" not found');return}a&&_.QA(a)};_.VA=function(a,b){b.BI=!0;a=_.YA(a);_.PA(a)};_.r("gapi.auth.authorize",JA);_.r("gapi.auth.checkSessionState",function(a,b){var c=_.wf();c.client_id=a.client_id;c.session_state=a.session_state;_.AA(function(){mA()?iA.send("check_session_state",c,function(d){b.call(null,d[0])},_.Kn):_.dh.call(_.Fz,"check_session_state",jA(function(d){b.call(null,d)}),c.session_state,c.client_id)})});_.r("gapi.auth.getAuthHeaderValueForFirstParty",KA);_.r("gapi.auth.getToken",Wz);
_.r("gapi.auth.getVersionInfo",function(a,b){_.AA(function(){var c=_.pj()||"",d=null,e=null;c&&(e=c.split(" "),e.length==2&&(d=e[1]));d?mA()?iA.send("get_versioninfo",{xapisidHash:d,sessionIndex:b},function(f){a(f[0])},_.Kn):_.dh.call(_.Fz,"get_versioninfo",jA(function(f){a(f)}),d,b):a()})});_.r("gapi.auth.init",_.AA);_.r("gapi.auth.setToken",_.Xz);_.r("gapi.auth.signIn",function(a){_.WA(a)});_.r("gapi.auth.signOut",function(){var a=Wz();a&&CA(a,a.cookie_policy)});
_.r("gapi.auth.unsafeUnpackIdToken",xA);_.r("gapi.auth._pimf",_.OA);_.r("gapi.auth._oart",uA);_.r("gapi.auth._guss",function(a){return Zz(a).read()});
var aB=_.op();aB.clientid&&aB.scope&&aB.callback&&!_.Sf("disableRealtimeCallback")&&_.UA(aB);
var zy=function(){};zy.prototype.sP=null;zy.prototype.getOptions=function(){var a;(a=this.sP)||(a=this.sP={});return a};var By;By=function(){};_.bb(By,zy);_.Ay=new By;
_.Li=function(a){return a==null?"":String(a)};_.Mi=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");_.Ni=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};
_.Oi=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Oi(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Pi=function(a){var b=[],c;for(c in a)_.Oi(c,a[c],b);return b.join("&")};_.Qi=function(a,b){b=_.Pi(b);return _.Ni(a,b)};
var Ej=function(a,b){a=_.jg.bQ({id:a,name:a});a.style.width="1px";a.style.height="1px";a.style.position="absolute";a.style.top="-100px";a.style.display="none";if(window.navigator){var c=window.navigator.userAgent||"";var d=window.navigator.product||"";c=c.indexOf("Opera")!=0&&c.indexOf("WebKit")==-1&&d=="Gecko"&&c.indexOf("rv:1.")>0}else c=!1;a.src=c?"about:blank":b;a.tabIndex=-1;typeof a.setAttribute==="function"?a.setAttribute("aria-hidden","true"):a["aria-hidden"]="true";document.body.appendChild(a);
c&&(a.src=b);return a};_.rj={UT:_.qj,Xaa:_.nj,TS:function(){var a=null;_.nj()&&(a=window.__PVT,a==null&&(a=(new _.cj(document)).get("BEAT")));return a},tS:_.pj};var Gj,Fj;Gj=function(){return!!Fj("auth/useFirstPartyAuthV2")};Fj=function(a){return _.Sf("googleapis.config/"+a)};
_.Hj=function(a,b,c){a=a===void 0?{}:a;b=b===void 0?window.location.href:b;c=c===void 0?"auto":c;if(c=="none")return a;var d=a.Authorization,e=a.OriginToken;if(!d&&!e){(e=_.tj())&&e.access_token&&(c=="oauth2"||c=="auto")&&(d=String(e.token_type||"Bearer")+" "+e.access_token);if(e=!d)e=(!!Fj("auth/useFirstPartyAuth")||c=="1p")&&c!="oauth2";if(e&&_.nj()){if(Gj()){d=Fj("primaryEmail");c=Fj("appDomain");e=Fj("fogId");var f=[];d&&f.push({key:"e",value:d});c&&f.push({key:"a",value:c});e&&f.push({key:"u",
value:e});d=_.pj(f)}else d=_.pj();d&&(c=a["X-Goog-AuthUser"],b=_.bj(b),b=c||b,_.gd(_.Li(b))&&(!Gj()||Gj()&&_.gd(_.Li(Fj("primaryEmail")))&&_.gd(_.Li(Fj("appDomain")))&&_.gd(_.Li(Fj("fogId"))))&&(b="0"),_.gd(_.Li(b))||(a["X-Goog-AuthUser"]=b))}d?a.Authorization=d:Fj("auth/useOriginToken")!==!1&&(e=_.rj.TS())&&(a.OriginToken=e)}return a};_.Ij=function(){function a(n,p,q,t,v){var u=f("proxy");if(t||!u){u=f("root");var w=f("root-1p")||u;u=u||"https://content.googleapis.com";w=w||"https://clients6.google.com";var y=f("xd3")||"/static/proxy.html";u=(t||String(p?w:u))+y}u=String(u);q&&(u+=(u.indexOf("?")>=0?"&":"?")+"usegapi=1");(p=_.jg.kh().jsh||_.If.h)&&(u+=(u.indexOf("?")>=0?"&":"?")+"jsh="+encodeURIComponent(p));u+="#parent="+encodeURIComponent(v!=null?String(v):_.Lh.getOrigin(document.location.href));return u+("&rpctoken="+n)}function b(n,
p,q,t,v){var u=d(q,t,v);k[u]||(q=Ej(u,p),_.dh.register("ready:"+n,function(){_.dh.unregister("ready:"+n);if(!l[u]){l[u]=!0;var w=m[u];m[u]=[];for(var y=0,D=w.length;y<D;++y){var C=w[y];e(C.rpc,C.Jda,C.callback)}}}),_.dh.xw(u,p),k[u]=q)}function c(n,p,q){var t=String(2147483647*_.Aj()|0),v=a(t,n,p,q);_.ah(function(){b(t,v,n,p,q)})}function d(n,p,q){n=a("",n,p,q,"");q=h[n+p];if(!q){q=new _.Sh;q.jx(n);q=q.Zi().toLowerCase();var t=_.Aj();q+=t;h[n+p]=q}return"apiproxy"+q}function e(n,p,q){var t=void 0,
v=!1;if(n!=="makeHttpRequests")throw'only "makeHttpRequests" RPCs are implemented';var u=function(I){if(I){if(typeof t!="undefined"&&typeof I.root!="undefined"&&t!=I.root)throw"all requests in a batch must have the same root URL";t=I.root||t;v=_.rj.UT(I.headers)}};if(p)for(var w=0,y=p.length;w<y;++w){var D=p[w];D&&u(D.params)}u=!!f("useGapiForXd3");var C=d(v,u,t);k[C]||c(v,u,t);l[C]?_.dh.call(C,n,function(I){if(this.f==C&&this.t==_.dh.ko(this.f)&&this.origin==_.dh.Do(this.f)){var L=_.Ug(I);q(L,I)}},
p):(m[C]||(m[C]=[]),m[C].push({rpc:n,Jda:p,callback:q}))}function f(n){return _.Sf("googleapis.config/"+n)}var h={},k={},l={},m={};return{Joa:function(n,p,q){return _.Hj(n,p,q)},tn:e}}();
var $h={dha:"Authorization",E1:"Content-ID",Bha:"Content-Transfer-Encoding",Cha:"Content-Type",iia:"Date",Zka:"OriginToken",xja:"hotrod-board-name",yja:"hotrod-chrome-cpu-model",zja:"hotrod-chrome-processors",Lna:"WWW-Authenticate",Nna:"X-Ad-Manager-Impersonation",Mna:"X-Ad-Manager-Debug-Info",Ona:"X-ClientDetails",Pna:"X-Compass-Routing-Destination",Sna:"X-Goog-AuthUser",Wna:"X-Goog-Encode-Response-If-Executable",Qna:"X-Google-Consent",Rna:"X-Google-EOM",Yna:"X-Goog-Meeting-ABR",Zna:"X-Goog-Meeting-Botguardid",
aoa:"X-Goog-Meeting-ClientInfo",boa:"X-Goog-Meeting-ClientVersion",coa:"X-Goog-Meeting-Debugid",doa:"X-Goog-Meeting-Identifier",eoa:"X-Goog-Meeting-Interop-Cohorts",foa:"X-Goog-Meeting-Interop-Type",goa:"X-Goog-Meeting-OidcIdToken",hoa:"X-Goog-Meeting-RtcClient",ioa:"X-Goog-Meeting-StartSource",joa:"X-Goog-Meeting-Token",koa:"X-Goog-Meeting-Viewer-Token",loa:"X-Goog-PageId",noa:"X-Goog-Safety-Content-Type",ooa:"X-Goog-Safety-Encoding",Una:"X-Goog-Drive-Client-Version",Vna:"X-Goog-Drive-Resource-Keys",
poa:"X-HTTP-Method-Override",qoa:"X-JavaScript-User-Agent",roa:"X-Origin",soa:"X-Referer",toa:"X-Requested-With",woa:"X-Use-HTTP-Status-Code-Override",uoa:"X-Server-Timeout",Xna:"X-Goog-First-Party-Reauth",voa:"X-Server-Token",Tna:"x-goog-chat-space-id",moa:"x-goog-pan-request-context"},ai="Accept Accept-Language Authorization Cache-Control cast-device-capabilities Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date developer-token EES-S7E-MODE financial-institution-id GData-Version google-cloud-resource-prefix hotrod-board-name hotrod-chrome-cpu-model hotrod-chrome-processors Host If-Match If-Modified-Since If-None-Match If-Unmodified-Since linked-customer-id login-customer-id MIME-Version Origin OriginToken Pragma Range request-id Slug Transfer-Encoding Want-Digest X-Ad-Manager-Impersonation X-Ad-Manager-Debug-Info x-alkali-account-key x-alkali-application-key x-alkali-auth-apps-namespace x-alkali-auth-entities-namespace x-alkali-auth-entity x-alkali-client-locale x-chrome-connected x-framework-xsrf-token X-Client-Data X-Client-Pctx X-ClientDetails X-Client-Version x-debug-settings-metadata X-Firebase-Locale X-GData-Client X-GData-Key X-Goog-AuthUser X-Goog-PageId X-Goog-Encode-Response-If-Executable X-GoogApps-Allowed-Domains X-Goog-AdX-Buyer-Impersonation X-Goog-Api-Client X-Goog-Api-Key X-Google-EOM X-Goog-Visibilities X-Goog-Correlation-Id X-Goog-Request-Info X-Goog-Request-Reason X-Goog-Request-Time X-Goog-Experiments x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin X-Goog-Firebase-Installations-Auth x-goog-greenenergyuserappservice-metadata X-Firebase-Client X-Firebase-Client-Log-Type X-Firebase-GMPID X-Firebase-Auth-Token X-Firebase-AppCheck X-Firebase-Token X-Goog-Drive-Client-Version X-Goog-Drive-Resource-Keys x-goog-iam-authority-selector x-goog-iam-authorization-token x-goog-request-params x-goog-sherlog-context X-Goog-Sn-Metadata X-Goog-Sn-PatientId X-Goog-Spatula X-Goog-Travel-Bgr X-Goog-Travel-Settings X-Goog-Upload-Command X-Goog-Upload-Content-Disposition X-Goog-Upload-Content-Length X-Goog-Upload-Content-Type X-Goog-Upload-File-Name X-Goog-Upload-Header-Content-Encoding X-Goog-Upload-Header-Content-Length X-Goog-Upload-Header-Content-Type X-Goog-Upload-Header-Transfer-Encoding X-Goog-Upload-Offset X-Goog-Upload-Protocol X-Goog-User-Project X-Goog-Visitor-Id X-Goog-FieldMask X-Google-Project-Override x-goog-maps-api-salt x-goog-maps-api-signature x-goog-maps-client-id x-goog-maps-channel-id x-goog-maps-solution-id x-goog-spanner-database-role X-HTTP-Method-Override X-JavaScript-User-Agent X-Pan-Versionid X-Proxied-User-IP X-Origin X-Referer X-Requested-With X-Stadia-Client-Context X-Upload-Content-Length X-Upload-Content-Type X-Use-Alt-Service X-Use-HTTP-Status-Code-Override X-Ios-Bundle-Identifier X-Android-Package X-Android-Cert X-Goog-Maps-Ios-Uuid X-Goog-Maps-Android-Uuid X-Ariane-Xsrf-Token X-Earth-Engine-App-ID-Token X-Earth-Engine-Computation-Profile X-Earth-Engine-Computation-Profiling X-Play-Console-Experiments-Override X-Play-Console-Session-Id X-YouTube-Bootstrap-Logged-In X-YouTube-VVT X-YouTube-Page-CL X-YouTube-Page-Timestamp X-Compass-Routing-Destination X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-Interop-Cohorts X-Goog-Meeting-Interop-Type X-Goog-Meeting-OidcIdToken X-Goog-Meeting-RtcClient X-Goog-Meeting-StartSource X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token x-sdm-id-token X-Sfdc-Authorization X-Server-Timeout x-foyer-client-environment X-Goog-First-Party-Reauth X-Server-Token x-rfui-request-context x-goog-chat-space-id x-goog-nest-jwt X-Cloud-Trace-Context traceparent x-goog-pan-request-context".split(" "),
bi="Digest Cache-Control Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date ETag Expires Last-Modified Location Pragma Range Server Transfer-Encoding WWW-Authenticate Vary Unzipped-Content-MD5 X-Correlation-ID X-Debug-Tracking-Id X-Google-Consent X-Google-EOM X-Goog-Generation X-Goog-Metageneration X-Goog-Safety-Content-Type X-Goog-Safety-Encoding X-Google-Trace X-Goog-Upload-Chunk-Granularity X-Goog-Upload-Control-URL X-Goog-Upload-Size-Received X-Goog-Upload-Status X-Goog-Upload-URL X-Goog-Diff-Download-Range X-Goog-Hash X-Goog-Updated-Authorization X-Server-Object-Version X-Guploader-Customer X-Guploader-Upload-Result X-Guploader-Uploadid X-Google-Gfe-Backend-Request-Cost X-Earth-Engine-Computation-Profile X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-RtcClient X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token X-Compass-Routing-Destination".split(" ");var ci,di,ei,fi,hi,ii,ji,ki,li,mi,ni,oi;ci=null;di=null;ei=null;fi=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);e>=65&&e<=90&&(e+=32);f>=65&&f<=90&&(f+=32);if(e!=f)return!1}return!0};
_.gi=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=a.length,e=0;e<d;++e){var f=a.charAt(e),h=a.charCodeAt(e);if(h>=55296&&h<=56319&&e+1<d){var k=a.charAt(e+1),l=a.charCodeAt(e+1);l>=56320&&l<=57343&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++e)}if(!(h>=0&&h<=1114109)||h>=55296&&h<=57343||h>=64976&&h<=65007||(h&65534)==65534)h=65533,f=String.fromCharCode(h);k=!(h>=32&&h<=126)||f==" "||c&&f==":"||f=="\\";!c||f!="/"&&f!="?"||(c=!1);f=="%"&&(e+2>=d?k=!0:(l=16*parseInt(a.charAt(e+
1),16)+parseInt(a.charAt(e+2),16),l>=0&&l<=255?(h=l,f=h==0?"":"%"+(256+l).toString(16).toUpperCase().substr(1),e+=2):k=!0));k&&(f=encodeURIComponent(f),f.length<=1&&(h>=0&&h<=127?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=encodeURIComponent(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=b.length;for(e=0;e<d;++e)f=b[e],h=f.split("%2E").join("."),h=h.split(encodeURIComponent("\uff0e")).join("."),h=="."?e+1==d&&c.push(""):
h==".."?(c.length>0&&c.pop(),e+1==d&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&a.charAt(0)=="/";)a=a.substr(1);return"/"+a};hi={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
ii={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
ji=function(a){if(!_.Gc(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if(typeof d==="string"&&d){var e=d.toLowerCase();fi(d,e)&&(b[e]=d)}}for(var f in $h)Object.prototype.hasOwnProperty.call($h,f)&&(a=$h[f],c=a.toLowerCase(),fi(a,c)&&Object.prototype.hasOwnProperty.call(b,c)&&(b[c]=a));return b};ki=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");li=/[ \t]*(\r?\n[ \t]+)+/g;mi=/^[ \t]+|[ \t]+$/g;
ni=function(a,b){if(!b&&typeof a==="object"&&a&&typeof a.length==="number"){b=a;a="";for(var c=b.length,d=0;d<c;++d){var e=ni(b[d],!0);e&&(a&&(e=a+", "+e),a=e)}}if(typeof a==="string"&&(a=a.replace(li," "),a=a.replace(mi,""),a.replace(ki,"")==""&&a))return a};oi=/^[-0-9A-Za-z!#\$%&'\*\+\.\^_`\|~]+$/g;
_.pi=function(a){if(typeof a!=="string"||!a||!a.match(oi))return null;a=a.toLowerCase();if(ei==null){var b=[],c=_.Sf("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Sf("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(bi);(c=_.Sf("googleapis/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Sf("client/headers/request"))&&
typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(ai);for(var d in $h)Object.prototype.hasOwnProperty.call($h,d)&&b.push($h[d]);ei=ji(b)}return ei!=null&&ei.hasOwnProperty(a)?ei[a]:a};
_.qi=function(a,b){if(!_.pi(a)||!ni(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||ii[a])return null;if(ci==null){b=[];var c=_.Sf("googleapis/headers/request");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Sf("client/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(ai);ci=ji(b)}return ci!=null&&ci.hasOwnProperty(a)?ci[a]:null};
_.ri=function(a,b){if(!_.pi(a)||!ni(b))return null;a=a.toLowerCase();if(hi[a])return null;if(di==null){b=[];var c=_.Sf("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Sf("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(bi);di=ji(b)}return di!=null&&di.hasOwnProperty(a)?a:null};
_.si=function(a,b){if(_.pi(b)&&a!=null&&typeof a==="object"){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&fi(d,b)){var e=ni(a[d]);e&&(c!==void 0&&(e=c+", "+e),c=e)}return c}};_.ti=function(a,b,c,d){var e=_.pi(b);if(e){c&&(c=ni(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&fi(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.ui=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=a.length,e=0;e<d;++e){var f=a[e];if(!f)break;var h=f.indexOf(":");if(!(h<=0)){var k=f.substring(0,h);if(k=_.pi(k)){for(f=f.substring(h+1);e+1<d&&a[e+1].match(/^[ \t]/);)f+="\r\n"+a[e+1],++e;if(f=ni(f))if(k=_.ri(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.si(c,k),h!==void 0&&(f=h+", "+f),_.ti(c,k,f,!0)}}}return c};
/\uffff/.test("\uffff");
var Fy;Fy=function(a,b){var c=[];for(b=b||0;b<a.length;b+=2)_.Oi(a[b],a[b+1],c);return c.join("&")};_.Gy=function(a,b){var c=arguments.length==2?Fy(arguments[1],0):Fy(arguments,1);return _.Ni(a,c)};_.Hy=function(a,b){_.Wj(a,"/")&&(a=a.slice(0,-1));_.fd(b,"/")&&(b=b.slice(1));return a+"/"+b};_.Iy=function(a){switch(a){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:return!0;default:return!1}};var Ky,Ly,My;_.Jy=function(a){_.Ak.call(this);this.headers=new Map;this.Z0=a||null;this.mg=!1;this.FD=this.Ya=null;this.QA="";this.xr=0;this.Ro=this.EH=this.pA=this.DF=!1;this.Ms=0;this.Pd=null;this.ln="";this.Gh=!1;this.sE=this.aN=null};_.bb(_.Jy,_.Ak);_.Jy.prototype.Cb=null;Ky=/^https?$/i;Ly=["POST","PUT"];My=[];_.Ny=function(a,b,c,d,e,f,h){var k=new _.Jy;My.push(k);b&&k.qa("complete",b);k.Dr("ready",k.y6);f&&k.YC(f);h&&(k.Gh=h);k.send(a,c,d,e)};_.g=_.Jy.prototype;
_.g.y6=function(){this.dispose();_.ub(My,this)};_.g.YC=function(a){this.Ms=Math.max(0,a)};_.g.setTrustToken=function(a){this.aN=a};_.g.setAttributionReporting=function(a){this.sE=a};
_.g.send=function(a,b,c,d){if(this.Ya)throw Error("Da`"+this.QA+"`"+a);b=b?b.toUpperCase():"GET";this.QA=a;this.xr=0;this.DF=!1;this.mg=!0;this.Ya=new XMLHttpRequest;this.FD=this.Z0?this.Z0.getOptions():_.Ay.getOptions();this.Ya.onreadystatechange=(0,_.z)(this.UW,this);try{this.EH=!0,this.Ya.open(b,String(a),!0),this.EH=!1}catch(h){this.Xy(5,h);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&
typeof d.get==="function"){e=_.sa(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Ea`"+String(d));d=Array.from(c.keys()).find(function(h){return"content-type"==h.toLowerCase()});e=_.Sa.FormData&&a instanceof _.Sa.FormData;!_.tb(Ly,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=_.sa(c);for(d=b.next();!d.done;d=b.next())c=_.sa(d.value),d=c.next().value,c=c.next().value,this.Ya.setRequestHeader(d,c);this.ln&&(this.Ya.responseType=
this.ln);"withCredentials"in this.Ya&&this.Ya.withCredentials!==this.Gh&&(this.Ya.withCredentials=this.Gh);if("setTrustToken"in this.Ya&&this.aN)try{this.Ya.setTrustToken(this.aN)}catch(h){}if("setAttributionReporting"in this.Ya&&this.sE)try{this.Ya.setAttributionReporting(this.sE)}catch(h){}try{Oy(this),this.Ms>0&&(this.Pd=_.Dy(this.Oi,this.Ms,this)),this.pA=!0,this.Ya.send(a),this.pA=!1}catch(h){this.Xy(5,h)}};
_.g.Oi=function(){typeof _.Ra!="undefined"&&this.Ya&&(this.xr=8,this.dispatchEvent("timeout"),this.abort(8))};_.g.Xy=function(a){this.mg=!1;this.Ya&&(this.Ro=!0,this.Ya.abort(),this.Ro=!1);this.xr=a;Py(this);Qy(this)};var Py=function(a){a.DF||(a.DF=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};_.Jy.prototype.abort=function(a){this.Ya&&this.mg&&(this.mg=!1,this.Ro=!0,this.Ya.abort(),this.Ro=!1,this.xr=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Qy(this))};
_.Jy.prototype.va=function(){this.Ya&&(this.mg&&(this.mg=!1,this.Ro=!0,this.Ya.abort(),this.Ro=!1),Qy(this,!0));_.Jy.N.va.call(this)};_.Jy.prototype.UW=function(){this.isDisposed()||(this.EH||this.pA||this.Ro?Ry(this):this.gJ())};_.Jy.prototype.gJ=function(){Ry(this)};
var Ry=function(a){if(a.mg&&typeof _.Ra!="undefined"&&(!a.FD[1]||_.Sy(a)!=4||a.getStatus()!=2))if(a.pA&&_.Sy(a)==4)_.Dy(a.UW,0,a);else if(a.dispatchEvent("readystatechange"),_.Sy(a)==4){a.mg=!1;try{a.ur()?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.xr=6,a.getStatus(),Py(a))}finally{Qy(a)}}},Qy=function(a,b){if(a.Ya){Oy(a);var c=a.Ya,d=a.FD[0]?function(){}:null;a.Ya=null;a.FD=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=d}catch(e){}}},Oy=function(a){a.Pd&&(_.Ey(a.Pd),a.Pd=
null)};_.Jy.prototype.isActive=function(){return!!this.Ya};_.Jy.prototype.ur=function(){var a=this.getStatus(),b;if(!(b=_.Iy(a))){if(a=a===0)a=String(this.QA).match(_.Mi)[1]||null,!a&&_.Sa.self&&_.Sa.self.location&&(a=_.Sa.self.location.protocol.slice(0,-1)),a=!Ky.test(a?a.toLowerCase():"");b=a}return b};_.Sy=function(a){return a.Ya?a.Ya.readyState:0};_.Jy.prototype.getStatus=function(){try{return _.Sy(this)>2?this.Ya.status:-1}catch(a){return-1}};
_.Ty=function(a){try{return a.Ya?a.Ya.responseText:""}catch(b){return""}};_.Uy=function(a){try{if(!a.Ya)return null;if("response"in a.Ya)return a.Ya.response;switch(a.ln){case "":case "text":return a.Ya.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in a.Ya)return a.Ya.mozResponseArrayBuffer}return null}catch(b){return null}};_.Jy.prototype.getResponseHeader=function(a){if(this.Ya&&_.Sy(this)==4)return a=this.Ya.getResponseHeader(a),a===null?void 0:a};
_.Jy.prototype.getAllResponseHeaders=function(){return this.Ya&&_.Sy(this)>=2?this.Ya.getAllResponseHeaders()||"":""};_.Uj(function(a){_.Jy.prototype.gJ=a(_.Jy.prototype.gJ)});
_.Au=function(a){var b=0,c;for(c in a)b++;return b};_.Bu=function(a){if(a.Hd&&typeof a.Hd=="function")return a.Hd();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(_.Gc(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return _.xb(a)};
_.Cu=function(a){if(a.Cg&&typeof a.Cg=="function")return a.Cg();if(!a.Hd||typeof a.Hd!="function"){if(typeof Map!=="undefined"&&a instanceof Map)return Array.from(a.keys());if(!(typeof Set!=="undefined"&&a instanceof Set)){if(_.Gc(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}return _.yb(a)}}};
var Eu,Iu,Vu,Qu,Zu,Ru,Tu,Su,Wu,Uu,$u;_.Du=function(){return Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^_.Jc()).toString(36)};Eu=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};
_.Fu=function(a,b,c){for(var d=0,e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)return d;d+=e+1}return-1};_.Gu=/#|$/;_.Hu=function(a,b){var c=a.search(_.Gu),d=_.Fu(a,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};
Iu=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(_.Gc(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=_.Cu(a),e=_.Bu(a),f=e.length,h=0;h<f;h++)b.call(c,e[h],d&&d[h],a)};
_.Ju=function(a,b){this.ke=this.Fh=this.Ji="";this.Qg=null;this.ZF=this.Xm="";this.lh=!1;var c;a instanceof _.Ju?(this.lh=b!==void 0?b:a.lh,_.Ku(this,a.Ji),_.Lu(this,a.Fh),_.Mu(this,a.hh()),_.Nu(this,a.Qg),this.setPath(a.getPath()),_.Ou(this,a.Zd.clone()),this.ql(a.wz())):a&&(c=String(a).match(_.Mi))?(this.lh=!!b,_.Ku(this,c[1]||"",!0),_.Lu(this,c[2]||"",!0),_.Mu(this,c[3]||"",!0),_.Nu(this,c[4]),this.setPath(c[5]||"",!0),_.Ou(this,c[6]||"",!0),this.ql(c[7]||"",!0)):(this.lh=!!b,this.Zd=new _.Pu(null,
this.lh))};_.Ju.prototype.toString=function(){var a=[],b=this.Ji;b&&a.push(Qu(b,Ru,!0),":");var c=this.hh();if(c||b=="file")a.push("//"),(b=this.Fh)&&a.push(Qu(b,Ru,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.Qg,c!=null&&a.push(":",String(c));if(c=this.getPath())this.ke&&c.charAt(0)!="/"&&a.push("/"),a.push(Qu(c,c.charAt(0)=="/"?Su:Tu,!0));(c=this.Zd.toString())&&a.push("?",c);(c=this.wz())&&a.push("#",Qu(c,Uu));return a.join("")};
_.Ju.prototype.resolve=function(a){var b=this.clone(),c=!!a.Ji;c?_.Ku(b,a.Ji):c=!!a.Fh;c?_.Lu(b,a.Fh):c=!!a.ke;c?_.Mu(b,a.hh()):c=a.Qg!=null;var d=a.getPath();if(c)_.Nu(b,a.Qg);else if(c=!!a.Xm){if(d.charAt(0)!="/")if(this.ke&&!this.Xm)d="/"+d;else{var e=b.getPath().lastIndexOf("/");e!=-1&&(d=b.getPath().slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(_.$b(e,"./")||_.$b(e,"/.")){d=_.fd(e,"/");e=e.split("/");for(var f=[],h=0;h<e.length;){var k=e[h++];k=="."?d&&h==e.length&&f.push(""):k==".."?((f.length>
1||f.length==1&&f[0]!="")&&f.pop(),d&&h==e.length&&f.push("")):(f.push(k),d=!0)}d=f.join("/")}else d=e}c?b.setPath(d):c=a.gr();c?_.Ou(b,a.Zd.clone()):c=!!a.ZF;c&&b.ql(a.wz());return b};_.Ju.prototype.clone=function(){return new _.Ju(this)};_.Ku=function(a,b,c){a.Ji=c?Vu(b,!0):b;a.Ji&&(a.Ji=a.Ji.replace(/:$/,""));return a};_.Lu=function(a,b,c){a.Fh=c?Vu(b):b;return a};_.Ju.prototype.hh=function(){return this.ke};_.Mu=function(a,b,c){a.ke=c?Vu(b,!0):b;return a};
_.Nu=function(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("T`"+b);a.Qg=b}else a.Qg=null;return a};_.Ju.prototype.getPath=function(){return this.Xm};_.Ju.prototype.setPath=function(a,b){this.Xm=b?Vu(a,!0):a;return this};_.Ju.prototype.gr=function(){return this.Zd.toString()!==""};_.Ou=function(a,b,c){b instanceof _.Pu?(a.Zd=b,a.Zd.hL(a.lh)):(c||(b=Qu(b,Wu)),a.Zd=new _.Pu(b,a.lh));return a};_.Ju.prototype.nb=function(a,b){return _.Ou(this,a,b)};_.Ju.prototype.getQuery=function(){return this.Zd.toString()};
_.Xu=function(a,b,c){a.Zd.set(b,c);return a};_.g=_.Ju.prototype;_.g.ih=function(a){return this.Zd.get(a)};_.g.wz=function(){return this.ZF};_.g.ql=function(a,b){this.ZF=b?Vu(a):a;return this};_.g.removeParameter=function(a){this.Zd.remove(a);return this};_.g.hL=function(a){this.lh=a;this.Zd&&this.Zd.hL(a)};_.Yu=function(a,b){return a instanceof _.Ju?a.clone():new _.Ju(a,b)};Vu=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};
Qu=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,Zu),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null};Zu=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};Ru=/[#\/\?@]/g;Tu=/[#\?:]/g;Su=/[#\?]/g;Wu=/[#\?@]/g;Uu=/#/g;_.Pu=function(a,b){this.Je=this.Rc=null;this.Ag=a||null;this.lh=!!b};$u=function(a){a.Rc||(a.Rc=new Map,a.Je=0,a.Ag&&Eu(a.Ag,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};_.g=_.Pu.prototype;
_.g.Sb=function(){$u(this);return this.Je};_.g.add=function(a,b){$u(this);this.Ag=null;a=av(this,a);var c=this.Rc.get(a);c||this.Rc.set(a,c=[]);c.push(b);this.Je+=1;return this};_.g.remove=function(a){$u(this);a=av(this,a);return this.Rc.has(a)?(this.Ag=null,this.Je-=this.Rc.get(a).length,this.Rc.delete(a)):!1};_.g.clear=function(){this.Rc=this.Ag=null;this.Je=0};_.g.isEmpty=function(){$u(this);return this.Je==0};_.g.Yi=function(a){$u(this);a=av(this,a);return this.Rc.has(a)};
_.g.uk=function(a){var b=this.Hd();return _.tb(b,a)};_.g.forEach=function(a,b){$u(this);this.Rc.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};_.g.Cg=function(){$u(this);for(var a=Array.from(this.Rc.values()),b=Array.from(this.Rc.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};
_.g.Hd=function(a){$u(this);var b=[];if(typeof a==="string")this.Yi(a)&&(b=b.concat(this.Rc.get(av(this,a))));else{a=Array.from(this.Rc.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};_.g.set=function(a,b){$u(this);this.Ag=null;a=av(this,a);this.Yi(a)&&(this.Je-=this.Rc.get(a).length);this.Rc.set(a,[b]);this.Je+=1;return this};_.g.get=function(a,b){if(!a)return b;a=this.Hd(a);return a.length>0?String(a[0]):b};
_.g.setValues=function(a,b){this.remove(a);b.length>0&&(this.Ag=null,this.Rc.set(av(this,a),_.vb(b)),this.Je+=b.length)};_.g.toString=function(){if(this.Ag)return this.Ag;if(!this.Rc)return"";for(var a=[],b=Array.from(this.Rc.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Hd(d);for(var f=0;f<d.length;f++){var h=e;d[f]!==""&&(h+="="+encodeURIComponent(String(d[f])));a.push(h)}}return this.Ag=a.join("&")};
_.g.clone=function(){var a=new _.Pu;a.Ag=this.Ag;this.Rc&&(a.Rc=new Map(this.Rc),a.Je=this.Je);return a};var av=function(a,b){b=String(b);a.lh&&(b=b.toLowerCase());return b};_.Pu.prototype.hL=function(a){a&&!this.lh&&($u(this),this.Ag=null,this.Rc.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.setValues(d,b))},this));this.lh=a};_.Pu.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Iu(arguments[b],function(c,d){this.add(d,c)},this)};
var cB=function(a){if(!a||typeof a!=="function")throw new bB("Must provide a function.");this.Rg=null;this.B7=a},dB=!1;dB=!1;
var eB=function(){var a=_.If.ms||_.If.u;if(a)return(new URL(a)).origin},fB=function(a){return new _.gl(function(b){var c=a.length,d=[];if(c)for(var e=function(k,l,m){c--;d[k]=l?{jz:!0,value:m}:{jz:!1,reason:m};c==0&&b(d)},f=0,h;f<a.length;f++)h=a[f],_.nl(h,_.$a(e,f,!0),_.$a(e,f,!1));else b(d)})},gB,hB,iB,jB={iP:function(a){gB=a;try{delete jB.iP}catch(b){}},jP:function(a){hB=a;try{delete jB.jP}catch(b){}},kP:function(a){iB=a;try{delete jB.kP}catch(b){}}},kB=function(a){return _.Iy(a.status)},lB=function(){var a=
!0,b=new XMLHttpRequest;b&&b.withCredentials!==void 0||(a=!1);return a},mB=function(a,b){if(b==null)return b;b=String(b);b.match(/^\/\/.*/)&&(b=(window.location.protocol=="http:"?"http:":"https:")+b);b.match(/^\/([^\/].*)?$/)&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=window.location.protocol+"//"+window.location.host+b);var c=b.match(/^(https?:)(\/\/)?(\/([^\/].*)?)?$/i);c&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=c[1]+"//"+
window.location.host+(c[3]||""));b=b.replace(/^(https?:\/\/[^\/?#@]*)\/$/i,"$1");b=b.replace(/^(http:\/\/[-_a-z0-9.]+):0*80([\/?#].*)?$/i,"$1$2");b=b.replace(/^(https:\/\/[-_a-z0-9.]+):0*443([\/?#].*)?$/i,"$1$2");b.match(/^https?:\/\/[-_a-z0-9.]*[-_a-z][-_a-z0-9.]*$/i)&&(b=b.toLowerCase());c=_.Sf("client/rewrite");_.yc(c)&&Object.prototype.hasOwnProperty.call(c,b)?b=String(c[b]||b):(b=b.replace(/^(https?):\/\/www\.googleapis\.com$/,"$1://content.googleapis.com"),b=b.replace(/^(https?):\/\/www-(googleapis-[-_a-z0-9]+\.[-_a-z0-9]+\.google\.com)$/,
"$1://content-$2"),b.match(/^https?:\/\/content(-[-_a-z0-9.]+)?\.googleapis\.com$/)||(b=b.replace(/^(https?):\/\/([-_a-z0-9]+(\.[-_a-z0-9]+)?\.googleapis\.com)$/,"$1://content-$2")));a&&(a=_.Sf("client/firstPartyRewrite"),_.yc(a)&&Object.prototype.hasOwnProperty.call(a,b)?b=String(a[b]||b):(b=b.replace(/^(https?):\/\/content\.googleapis\.com$/,"$1://clients6.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.([-a-z0-9]+)\.googleapis\.com$/,"$1://$2-googleapis.$3.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.googleapis\.com$/,
"$1://$2.clients6.google.com"),b=b.replace(/^(https?):\/\/([-a-z0-9]+)-www-googleapis\.([-a-z0-9]+).google.com$/,"$1://content-googleapis-$2.$3.google.com")));return b},bB=function(a){_.rb.call(this,a)};_.A(bB,_.rb);bB.prototype.name="gapix.client.GapiClientError";cB.prototype.then=function(a,b,c){this.Rg||(this.Rg=this.B7());return this.Rg.then(a,b,c)};cB.prototype.OC=function(a){this.Rg||(this.Rg=a)};var nB=function(a){this.aS=a;this.count=this.count=0};
nB.prototype.Hb=function(a){a?this.count+=a:this.count++;this.aS&&this.aS()};nB.prototype.get=function(){return this.count};nB.prototype.reset=function(){this.count=0};
var qB=function(){var a=!0,b=this;a=a===void 0?!0:a;this.Ay=new Map;this.yE=!1;var c=eB();c&&(this.url=c+"/js/gen_204",c=_.Zi("gen204logger")||{},this.ku=c.interval,this.bS=c.rate,this.yE=c.Roa,a&&this.url&&oB(this),document.addEventListener("visibilitychange",this.flush),pB(this),this.flush(),document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&b.flush()}),document.addEventListener("pagehide",this.flush.bind(this)))},pB=function(a){var b=_.If.dm||[];if(b&&
b.length!==0){b=_.sa(b);for(var c=b.next();!c.done;c=b.next())rB(a,c.value).Hb();delete _.If.dm}},rB=function(a,b){a.Ay.has(b)||a.Ay.set(b,new nB(a.yE?void 0:function(){return a.flush()}));return a.Ay.get(b)};
qB.prototype.flush=function(){var a=this;if(this.url&&this.bS){for(var b="",c=_.sa(this.Ay),d=c.next();!d.done;d=c.next()){var e=_.sa(d.value);d=e.next().value;e=e.next().value;var f=e.get();f>0&&(b+=b.length>0?"&":"",b+="c=",b+=encodeURIComponent(d+":"+f),e.reset());if(b.length>1E3)break}b!==""&&Math.random()<this.bS&&fetch(this.url+"?"+b,{method:"GET",mode:"no-cors",signal:AbortSignal.timeout(3E4)}).catch(function(){}).finally(function(){oB(a)})}};qB.prototype.setInterval=function(a){this.ku=a};
var oB=function(a){a.ku&&a.yE&&setTimeout(function(){a.flush()},a.ku)},sB=function(){qB.UV||(qB.UV=new qB);return qB.UV},tB=function(a){var b={},c;for(c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var d=_.si(a,c);d&&(c=_.ri(c,d))&&_.ti(b,c,d,!0)}return b},uB={error:{code:-1,message:"A network error occurred and the request could not be completed."}},vB=function(a,b,c,d){_.Jy.call(this);this.Qd=a;this.FI=b;this.Td=c;a={};if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(b=_.si(d,
e),b!==void 0&&(e=_.qi(e,b))&&_.ti(a,e,b));d={};for(e in a)Object.prototype.hasOwnProperty.call(a,e)&&(d[unescape(encodeURIComponent(e))]=unescape(encodeURIComponent(a[e])));this.Tu=d;this.Rg=null};_.A(vB,_.Jy);
vB.prototype.then=function(a){this.Rg||(this.Rg=(new _.gl(function(b,c){this.qa("error",(0,_.z)(function(){c(wB(this))},this));this.qa("success",(0,_.z)(function(){b(wB(this))},this));this.send(this.Qd,this.FI,this.Td,this.Tu)},this)).then(function(b){b.headers=tB(b.headers);return b},function(b){return b.status?(b.headers=tB(b.headers),_.ll(b)):_.ll({result:uB,body:'{"error":{"code":-1,"message":"A network error occurred and the request could not be completed."}}',headers:null,status:null,statusText:null})}));
return this.Rg.then.apply(this.Rg,arguments)};
var wB=function(a){var b=a.getStatus(),c=_.Ty(a);var d=b==204?!1:a.ln==""?_.Ug(c):_.Uy(a);var e=a.getAllResponseHeaders();e=_.ui(e,!1);try{var f=_.Sy(a)>2?a.Ya.statusText:""}catch(h){f=""}return{result:d,body:c,headers:e,status:b,statusText:f}},xB=/;\s*charset\s*=\s*("utf-?8"|utf-?8)\s*(;|$)/i,yB=/^(text\/[^\s;\/""]+|application\/(json(\+[^\s;\/""]*)?|([^\s;\/""]*\+)?xml))\s*(;|$)/i,zB=/;\s*charset\s*=/i,AB=/(([\r\n]{0,2}[A-Za-z0-9+\/]){4,4}){0,1024}([\r\n]{0,2}[A-Za-z0-9+\/][\r\n]{0,2}[AQgw]([\r\n]{0,2}=){2,2}|([\r\n]{0,2}[A-Za-z0-9+\/]){2,2}[\r\n]{0,2}[AEIMQUYcgkosw048][\r\n]{0,2}=|([\r\n]{0,2}[A-Za-z0-9+\/]){4,4})[\r\n]{0,2}/g,BB=
function(a){var b=[];a=a.replace(AB,function(c){b.push(_.Lz(c));return""});if(a.length)throw Error("Fa");return b.join("")},CB=function(a){var b=a.headers;if(b&&_.si(b,"X-Goog-Safety-Encoding")==="base64"){var c=BB(a.body),d=_.si(b,"X-Goog-Safety-Content-Type");b["Content-Type"]=d;if(d.match(xB)||d.match(yB)&&!d.match(zB))c=_.Nw(_.Ei(c));_.ti(b,"X-Goog-Safety-Encoding");_.ti(b,"X-Goog-Safety-Content-Type");a.body=c}},DB=function(a,b,c){c||((c=_.Sf("googleapis.config/proxy"))&&(c=String(c).replace(/\/static\/proxy\.html$/,
"")||"/"),c=String(c||""));c||(c=_.Sf("googleapis.config/root"),b&&(c=_.Sf("googleapis.config/root-1p")||c),c=String(c||""));c=String(mB(b,c)||c);return a=_.Hy(c,a)},EB=function(a,b){var c=a.params||_.wf();c.url=c.path;var d=c.root;d=DB("/",_.qj(c.headers),d);d.match(/^(.*[^\/])?\/$/)&&(d=d.substr(0,d.length-1));c.root=d;a.params=c;_.Ij.tn("makeHttpRequests",[a],function(e,f){e&&e.gapiRequest?(e.gapiRequest.data?CB(e.gapiRequest.data):CB(e),b(e,_.Vg(e))):b(e,f)})},FB=function(a){var b=_.fj(a,"params",
"headers");b&&typeof b==="object"||(b={});a={};for(var c in b)if(Object.prototype.hasOwnProperty.call(b,c)){var d=_.si(b,c);d&&(_.qi(c,d),_.ti(a,c,d))}c=(window.location.href.match(_.Mi)[1]||null)=="chrome-extension";a=_.qj(a);return!(c&&a)&&lB()},GB=function(a){return new _.gl(function(b,c){var d=function(e){e&&e.gapiRequest?e=e.gapiRequest.data||e:c(e);e={result:e.status!=204&&_.Ug(e.body),body:e.body,headers:e.headers||null,status:e.status||null,statusText:e.statusText||null};kB(e)?b(e):c(e)};
try{EB(a,d)}catch(e){c(e)}})},HB=function(a){var b=!_.Sf("client/cors")||!!_.Sf("client/xd4"),c={};_.vn(a,function(d,e){(d=_.qi(e,d))||b||(d=_.pi(e));d&&(e=_.si(a,d))&&_.ti(c,d,e)});return c},IB=function(a){var b=a.params||_.wf();a=_.Nk(b.headers||{});var c=b.httpMethod||"GET",d=String(b.url||""),e=encodeURIComponent("$unique");if(!(c==="POST"||_.Fu(d,"$unique",d.search(_.Gu))>=0||_.Fu(d,e,d.search(_.Gu))>=0)){var f=[];for(h in a)Object.prototype.hasOwnProperty.call(a,h)&&f.push(h.toLowerCase());
f.sort();f.push(_.Nh(location.href));var h=f.join(":");f=_.hj();f.update(h);h=f.Zi().toLowerCase().substr(0,7);h=String(parseInt(h,16)%1E3+1E3).substr(1);d=_.Gy(d,e,"gc"+h)}e=b.body||null;h=b.responseType||null;b=_.qj(a)||b.authType=="1p";f=!!_.Sf("googleapis.config/auth/useUberProxyAuth")||!!_.Sf("client/withCredentials");_.ti(a,"X-Referer");a=HB(a);var k=new vB(d,c,e,a);k.Gh=b||f;h&&(k.ln=h);return new _.gl(function(l,m){k.then(function(n){CB(n);l(n)},function(n){m(n)})})},JB=function(a,b){var c=
function(d){d=_.Nk(d);delete d.result;d={gapiRequest:{data:d}};b&&b(d,_.Vg(d))};IB(a).then(c,c)},KB=function(a,b){(_.Sf("client/cors")||_.Sf("client/xd4"))&&FB(a)?(rB(sB(),12).Hb(),JB(a,b)):(rB(sB(),11).Hb(),EB(a,b))},LB=function(a){this.jn=a;this.mg=!1;this.promise={then:(0,_.z)(function(b,c,d){this.mg||(this.mg=!0);this.Yv&&!this.Wv?this.jn.resolve(this.Yv):this.Wv&&!this.Yv&&this.jn.reject(this.Wv);return this.jn.promise.then(b,c,d)},this)}};
LB.prototype.resolve=function(a){this.mg?this.jn.resolve(a):this.Yv||this.Wv||(this.Yv=a)};LB.prototype.reject=function(a){this.mg?this.jn.reject(a):this.Yv||this.Wv||(this.Wv=a)};var MB=function(a){a=_.gj(a.error);return{code:a.code,data:a.errors,message:a.message}},NB=function(a){throw Error("Ia`"+a);};var OB=function(a){cB.call(this,OB.prototype.xp);if(!a||typeof a!="object"&&typeof a!="string")throw new bB("Missing required parameters");if(typeof a==="string"){var b={};b.path=a}else b=a;if(!b.path)throw new bB('Missing required parameter: "path"');this.Ii={};this.Ii.path=b.path;this.Ii.method=b.method||"GET";this.Ii.params=b.params||{};this.Ii.headers=b.headers||{};this.Ii.body=b.body;this.Ii.root=b.root;this.Ii.responseType=b.responseType;this.Ii.apiId=b.apiId;this.Kn=b.authType||"auto";this.fba=
!!b.isXd4;this.ZU=!1;this.Tj(this.Kn);this.wY=!1};_.A(OB,cB);OB.prototype.qf=function(){return this.Ii};OB.prototype.Tj=function(a){this.Kn=a;this.ZU=this.Kn==="1p"};OB.prototype.Fu=function(){return this.ZU};
OB.prototype.Xk=function(){if(!this.wY){this.wY=!0;var a=this.Ii,b=a.headers=a.headers||{},c=[],d=[];for(h in b)if(Object.prototype.hasOwnProperty.call(b,h)){c.push(h);var e=h,f=_.si(b,e);f&&(e=_.qi(e,f)||_.pi(e))&&d.push([e,f])}var h=0;for(e=c.length;h<e;++h)delete b[c[h]];c=0;for(h=d.length;c<h;++c)_.ti(b,d[c][0],d[c][1]);if(this.fba)d=this.Kn=="1p";else{d=b;c=String(_.Sf("client/version","1.1.0"));h=String(_.Sf("client/name","google-api-javascript-client"));h=PB[h]===!0?h:"google-api-javascript-client";
e=String(_.Sf("client/appName",""));f=[];e&&(f.push(e),f.push(" "));f.push(h);c&&(f.push("/"),f.push(c));_.ti(d,"X-JavaScript-User-Agent",f.join(""));_.ti(b,"X-Requested-With","XMLHttpRequest");d=_.si(b,"Content-Type");a.body&&!d&&_.ti(b,"Content-Type","application/json");_.Sf("client/allowExecutableResponse")||_.ti(b,"X-Goog-Encode-Response-If-Executable","base64");(d=_.si(b,"Content-Type"))&&d.toLowerCase()=="application/json"&&!a.params.alt&&(a.params.alt="json");(d=a.body||null)&&_.yc(d)&&(a.body=
_.Vg(d));a.key=a.id;b=_.Hj(b,void 0,this.Kn);d=_.qj(b);if((c=b)&&window.navigator){h=[];for(e=0;e<QB.length;e++)(f=window.navigator[QB[e]])&&h.push(encodeURIComponent(QB[e])+"="+encodeURIComponent(f));_.ti(c,"X-ClientDetails",h.join("&"))}(c=_.Sf("client/apiKey"))&&a.params.key===void 0&&(a.params.key=c);(c=_.Sf("client/trace"))&&!a.params.trace&&(a.params.trace=c)}this.Kn=="auto"&&(d?this.Tj("1p"):(b=_.si(b,"Authorization"))&&String(b).match(/^(Bearer|MAC)[ \t]/i)?this.Tj("oauth2"):this.Tj("none"));
if((b=String(a.path||"").match(/^(https?:\/\/[^\/?#]+)([\/?#].*)?$/i))&&!a.root)if(a.root=String(b[1]),a.path=String(b[2]||"/"),a.path.match(/^\/_ah\/api(\/.*)?$/))a.root+="/_ah/api",a.path=a.path.substr(8);else{b=_.Sf("googleapis.config/root");d&&(b=_.Sf("googleapis.config/root-1p")||b);b=String(b||"");c=a.root+a.path;if(h=b&&c.substr(0,b.length)===b)h=_.Yu(b),e=_.Yu(c),h=(!h.ke&&!e.ke||h.hh()==e.hh())&&(h.Qg==null&&e.Qg==null||h.Qg==e.Qg);h&&(a.path=c.substr(b.length),a.root=b)}b=a.params;c=_.gi(a.path);
h=String(_.Sf("googleapis.config/xd3")||"");h.length>=18&&h.substring(h.length-18)=="/static/proxy.html"&&(h=h.substring(0,h.length-18));h||(h="/");e=_.gi(h);if(h!=e)throw Error("F");h.charAt(h.length-1)!="/"&&(h+="/");c=_.Hy(h,c);_.Wj(c,"/")&&(c=c.substring(0,c.length-1));h=_.wf();for(var k in b)Object.prototype.hasOwnProperty.call(b,k)&&(e=encodeURIComponent(k),h[e]=b[k]);c=_.Qi(c,h);a.path=c;a.root=mB(!!d,a.root);a.url=DB(a.path,!!d,a.root)}};
var RB=function(a){a.Xk();var b=a.Ii;return{key:"gapiRequest",params:{id:b.id,key:b.key,url:b.url,path:b.path,httpMethod:b.method,body:b.body||"",headers:b.headers||{},urlParams:{},root:b.root,authType:a.Kn}}};OB.prototype.execute=function(a){var b=RB(this);KB(b,function(c,d){var e=c;c.gapiRequest&&(e=c.gapiRequest);e&&e.data&&(e=e.data);c=e;c=c instanceof Array?c[0]:c;if(c.status!=204&&c.body)try{var f=_.Ug(c.body)}catch(h){}a&&a(f,d)})};
OB.prototype.xp=function(){var a=RB(this);(_.Sf("client/cors")||_.Sf("client/xd4"))&&FB(a)?(rB(sB(),15).Hb(),a=IB(a)):(rB(sB(),14).Hb(),a=GB(a));return a};OB.prototype.tj=function(){return this.xp()};var QB=["appVersion","platform","userAgent"],PB={"google-api-gwt-client":!0,"google-api-javascript-client":!0};OB.prototype.execute=OB.prototype.execute;OB.prototype.then=OB.prototype.then;OB.prototype.getPromise=OB.prototype.tj;var SB=function(a){if(!a||typeof a!="object")throw new bB("Missing rpc parameters");if(!a.method)throw new bB("Missing rpc method");this.fC=a};SB.prototype.xe=function(){var a=this.fC.transport;return a?a.root||null:null};SB.prototype.execute=function(a){var b=hB();b.add(this,{id:"gapiRpc",callback:this.xv(a)});b.execute()};
SB.prototype.XA=function(a){var b=this.fC.method,c=String,d;(d=this.fC.apiVersion)||(d=String(b).split(".")[0],d=_.Sf("googleapis.config/versions/"+b)||_.Sf("googleapis.config/versions/"+d)||"v1",d=String(d));a={jsonrpc:"2.0",id:a,method:b,apiVersion:c(d)};(b=this.fC.rpcParams)&&(a.params=b);return a};
SB.prototype.xv=function(a){return function(b,c){if(b)if(b.error){var d=b.error;d.error==null&&(d.error=_.Nk(b.error))}else d=b.result||b.data,_.yc(d)&&d.result==null&&(d.result=_.Nk(b.result||b.data));else d=!1;a(d,c)}};SB.prototype.execute=SB.prototype.execute;var UB=function(a,b){this.Le=b||0;this.Le==2?(b=null,a!=null&&_.yc(a)&&(b={},b.method=a.method,b.rpcParams=a.rpcParams,b.transport=a.transport,b.root=a.root,b.apiVersion=a.apiVersion,b.authType=a.authType),this.Db=new SB(b)):(this.Le==0&&(b=a&&a.callback)&&(a.callback=TB(b)),b=null,a!=null&&(_.yc(a)?(b={},b.path=a.path,b.method=a.method,b.params=a.params,b.headers=a.headers,b.body=a.body,b.root=a.root,b.responseType=a.responseType,b.authType=a.authType,b.apiId=a.apiId):typeof a==="string"&&(b=a)),
this.Db=new OB(b))},TB=function(a){return function(b){if(b!=null&&_.yc(b)&&b.error){var c=MB(b);b=_.Vg([{id:"gapiRpc",error:c}]);c.error=_.gj(c)}else b==null&&(b={}),c=_.gj(b),c.result=_.gj(b),b=_.Vg([{id:"gapiRpc",result:b}]);a(c,b)}};_.g=UB.prototype;_.g.getFormat=function(){return this.Le};_.g.execute=function(a){this.Db.execute(a&&this.Le==1?TB(a):a)};_.g.then=function(a,b,c){this.Le==2&&NB('The "then" method is not available on this object.');return this.Db.then(a,b,c)};
_.g.OC=function(a){this.Db.OC&&this.Db.OC(a)};_.g.qf=function(){if(this.Db.qf)return this.Db.qf()};_.g.Xk=function(){this.Db.qf&&this.Db.Xk()};_.g.xe=function(){if(this.Db.xe)return this.Db.xe()};_.g.XA=function(a){if(this.Db.XA)return this.Db.XA(a)};_.g.Tj=function(a){this.Db.Tj&&this.Db.Tj(a)};_.g.Fu=function(){return this.Db.Fu()};_.g.tj=function(){if(this.Db.tj)return this.Db.tj()};UB.prototype.execute=UB.prototype.execute;UB.prototype.then=UB.prototype.then;UB.prototype.getPromise=UB.prototype.tj;var VB=/<response-(.*)>/,WB=/^application\/http(;.+$|$)/,XB=["clients6.google.com","content.googleapis.com","www.googleapis.com"],YB=function(a,b){a=_.si(a,b);if(!a)throw new bB("Unable to retrieve header.");return a},ZB=function(a){var b=void 0;a=_.sa(a);for(var c=a.next();!c.done;c=a.next()){c=c.value.qf().apiId;if(typeof c!=="string")return"batch";if(b===void 0)b=c;else if(b!=c)return"batch"}b=_.Sf("client/batchPath/"+b)||"batch/"+b.split(":").join("/");return String(b)},$B=function(a){a=a.map(function(b){return b.request});
return ZB(a)},aC=function(a,b){var c=[];a=a.qf();var d=function(f,h){_.vn(f,function(k,l){h.push(l+": "+k)})},e={"Content-Type":"application/http","Content-Transfer-Encoding":"binary"};e["Content-ID"]="<"+b+">";d(e,c);c.push("");c.push(a.method+" "+a.path);d(a.headers,c);c.push("");a.body&&c.push(a.body);return c.join("\r\n")},dC=function(a,b){a=bC(a,b);var c={};_.wb(a,function(d,e){c[e]=cC(d,e)});return c},cC=function(a,b){return{result:a.result||a.body,rawResult:_.Vg({id:b,result:a.result||a.body}),
id:b}},bC=function(a,b){a=(0,_.hd)(a);_.Wj(a,"--")&&(a=a.substring(0,a.length-2));a=a.split(b);b=_.wf();for(var c=0;c<a.length;c++)if(a[c]){var d;if(d=a[c]){_.Wj(d,"\r\n")&&(d=d.substring(0,d.length-2));if(d){d=d.split("\r\n");for(var e=0,f={headers:{},body:""};e<d.length&&d[e]=="";)e++;for(f.outerHeaders=eC(d,e);e<d.length&&d[e]!="";)e++;e++;var h=d[e++].split(" ");f.status=Number(h[1]);f.statusText=h.slice(2).join(" ");for(f.headers=eC(d,e);e<d.length&&d[e]!="";)e++;e++;f.body=d.slice(e).join("\r\n");
CB(f);d=f}else d=null;e=_.wf();f=YB(d.outerHeaders,"Content-Type");if(WB.exec(f)==null)throw new bB("Unexpected Content-Type <"+f+">");f=YB(d.outerHeaders,"Content-ID");f=VB.exec(f);if(!f)throw new bB("Unable to recognize Content-Id.");e.id=decodeURIComponent(f[1].split("@")[0].replace(/^.*[+]/,""));e.response={status:d.status,statusText:d.statusText,headers:d.headers};d.status!=204&&(e.response.body=d.body,e.response.result=_.Ug(d.body));d=e}else d=null;d&&d.id&&(b[d.id]=d.response)}return b},eC=
function(a,b){for(var c=[];b<a.length&&a[b];b++)c.push(a[b]);return _.ui(c.join("\r\n"),!1)},fC=function(a,b,c){a=a||b;if(!a||_.Yu(a).Ji!=="https")if(a=c?_.Sf("googleapis.config/root-1p"):_.Sf("googleapis.config/root"),!a)return!1;a=mB(c,String(a))||a;return XB.includes(_.Yu(a).hh())};var gC=function(a){cB.call(this,gC.prototype.xp);this.Ck={};this.Vx={};this.fn=[];this.Ef=a;this.Eba=!!a;this.WT=this.fA=!1};_.A(gC,cB);var hC=function(a,b){a=_.sa(Object.values(a.Ck));for(var c=a.next();!c.done;c=a.next())if(c.value.map(function(d){return d.id}).includes(b))return!0;return!1};gC.prototype.nq=function(a){(function(b){setTimeout(function(){throw b;})})(a)};
gC.prototype.add=function(a,b){var c=b||_.wf();b=_.wf();if(!a)throw new bB("Batch entry "+(_.xf(c,"id")?'"'+c.id+'" ':"")+"is missing a request method");a.Xk();b.request=a;var d=_.ql();d=new LB(d);b.ZB=d;a.OC(b.ZB.promise);d=a.qf().headers;_.qj(d)&&(this.fA=!0);(d=String((d||{}).Authorization||"")||null)&&d.match(/^Bearer|MAC[ \t]/i)&&(this.WT=!0);d=a.qf().root;if(!this.Eba){if(d&&this.Ef&&d!=this.Ef)throw new bB('The "root" provided in this request is not consistent with that of existing requests in the batch.');
this.Ef=d||this.Ef}if(_.xf(c,"id")){d=c.id;if(hC(this,d))throw new bB('Batch ID "'+d+'" already in use, please use another.');b.id=d}else{do b.id=String(Math.round(2147483647*_.Aj()));while(hC(this,b.id))}b.callback=c.callback;c="batch";fC(this.Ef,a.qf().path,this.fA)&&(c=$B([b]));this.Ck[c]=this.Ck[c]||[];this.Ck[c].push(b);this.Vx[b.id]=b;return b.id};
var iC=function(a){var b=[],c=fC(a.Ef,void 0,a.fA);Object.entries(a.Ck).length>1&&_.Zg.warn("Heterogeneous batch requests are deprecated. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");for(var d=_.sa(Object.entries(a.Ck)),e=d.next();!e.done;e=d.next()){e=_.sa(e.value);var f=e.next().value;e=e.next().value;for(var h=!0,k=_.sa(e),l=k.next();!l.done;l=k.next())l=l.value,l.request.Xk(),f==="batch"&&c&&(h=!1,l.Yaa=!0,l.request.qf.root=a.Ef,b.push(l.request),
a.fn.push([l]));if(h){var m=e;f=a.Ef;h=a.fA;k=a.WT;l="batch"+String(Math.round(2147483647*_.Aj()))+String(Math.round(2147483647*_.Aj()));var n="--"+l;l="multipart/mixed; boundary="+l;for(var p={path:$B(m),method:"POST"},q=[],t=0;t<m.length;t++)q.push(aC(m[t].request,[n.substr(n.indexOf("--")+2),"+",encodeURIComponent(m[t].id).split("(").join("%28").split(")").join("%29").split(".").join("%2E"),"@googleapis.com"].join("")));p.body=[n,q.join("\r\n"+n+"\r\n"),n+"--"].join("\r\n")+"\r\n";p.root=f||null;
_.Sf("client/xd4")&&lB()?(p.isXd4=!0,p.params={$ct:l},p.headers={},_.ti(p.headers,"Content-Type","text/plain; charset=UTF-8"),h?p.authType="1p":k&&(p.authType="oauth2"),f=new OB(p)):(p.headers={},_.ti(p.headers,"Content-Type",l),f=iB(p));b.push(f);a.fn.push(e)}}return b};
gC.prototype.execute=function(a){if(!(Object.keys(this.Ck).length<1)){var b=this.xv(a);a=iC(this);var c=[],d=a.map(function(e){return new _.gl(function(f){try{e.execute(function(h,k){return f({eP:h,oda:k})})}catch(h){c.push(h),f({eP:{jz:!1,reason:h}})}})});if(c.length>0&&c.length===a.length)throw c[0];_.ol(d).then(function(e){var f=e.map(function(h){return h.oda});e=e.map(function(h){return h.eP});b(e,f)})}};
gC.prototype.xp=function(){var a=this;if(Object.keys(this.Ck).length<1)return _.kl({});var b=iC(this).map(function(c){return new _.gl(function(d,e){return c.tj().then(d,e)})});return fB(b).then(function(c){c=c.map(function(d){return d.jz?d.value:d});return jC(a,c,!0)})};
gC.prototype.uX=function(a,b,c,d){var e={};if(c){e=b?bC:dC;b=YB(a.headers,"Content-Type").split("boundary=")[1];if(!b)throw new bB("Boundary not indicated in response.");e=e(a.body,"--"+b)}else b?(a.result=_.Ug(a.body),e[d]=a):e[d]=cC(a,d);a={};e=_.sa(Object.entries(e));for(b=e.next();!b.done;b=e.next())if(c=_.sa(b.value),b=c.next().value,c=c.next().value,a[b]=c,!this.Vx[b])throw new bB("Could not find batch entry for id "+b+".");return a};
var jC=function(a,b,c,d,e){for(var f=!1,h={},k,l=0,m=0;m<b.length;m++){var n=b[m];if(n.jz===!1){l++;b[m]=n.reason;n=kC([b[m]]);for(var p=_.sa(a.fn[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=n}else{if(a.fn[m].length<1)throw new bB("Error processing batch responses.");try{var t=!(a.fn[m].length===1&&a.fn[m][0].Yaa),v=a.fn[m][0].id;if(!c){p=n;q=t;var u=d[m],w=p;if(u&&(!w||!q)){var y=_.Ug(u);y&&(w=y.gapiRequest?y.gapiRequest.data:y,!q&&p&&(w.body=p))}if(!w)throw new bB("The batch response is missing.");
n=w}p=void 0;if(q=n){var D=q.headers;if(D){var C=_.wf();for(p in D)if(Object.prototype.hasOwnProperty.call(D,p)){var I=_.si(D,p);_.ti(C,p,I,!0)}q.headers=C}}if(t&&YB(n.headers,"Content-Type").indexOf("multipart/mixed")!=0)throw new bB("The response's Content-Type is not multipart/mixed.");k=k||_.gj(n);var L=kB(n);L&&!kB(k)&&(k.status=n.status,k.statusText=n.statusText);if(L||c||!t)f=!0,h=Object.assign(h,a.uX(n,c,t,v))}catch(P){for(l++,b[m]=P,n=kC([P]),p=_.sa(a.fn[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=
n}}}if(l===b.length){d=kC(b);h=_.Vg(d);k=0;a=Array.from(Object.values(a.Ck)).flat();f=_.sa(a);for(l=f.next();!l.done;l=f.next())if(l=l.value,c)l.ZB.reject(d);else if(l.callback)try{k++,l.callback(d,h)}catch(P){gC.prototype.nq(P)}if(e)try{e(d,h)}catch(P){gC.prototype.nq(P)}else if(k!==a.length)throw b.length===1?b[0]:d;}else{if(f)for(f=_.sa(Object.entries(h)),l=f.next();!l.done;l=f.next())if(l=_.sa(l.value),m=l.next().value,l=l.next().value,c)m=a.Vx[m],l&&kB(l)?m.ZB.resolve(l):m.ZB.reject(l);else if(m=
a.Vx[m],m.callback){if(l&&l.rawResult)try{delete l.rawResult}catch(P){}try{m.callback(l||!1,_.Vg(l))}catch(P){gC.prototype.nq(P)}}k.result=h||{};k.body=b.length===1?k.body:"";if(e)try{e(h||null,d.length===1?d[0]:null)}catch(P){gC.prototype.nq(P)}return k}},kC=function(a){var b={error:{code:0,message:"The batch request could not be fulfilled.  "}};a=_.sa(a);for(var c=a.next();!c.done;c=a.next())(c=c.value)&&c.message||c instanceof Error&&c.message?b.error.message+=(c.message||c instanceof Error&&c.message)+
"  ":c&&c.error&&c.error.message&&(b.error.message+=c.error.message+"  ",b.error.code=c.error.code||b.error.code||0);b.error.message=b.error.message.trim();return{result:b,body:_.Vg(b),headers:null,status:null,statusText:null}};gC.prototype.xv=function(a){var b=this;return function(c,d){b.xE(c,d,a)}};gC.prototype.xE=function(a,b,c){jC(this,a,!1,b,c)};gC.prototype.add=gC.prototype.add;gC.prototype.execute=gC.prototype.execute;gC.prototype.then=gC.prototype.then;var lC=function(){this.Yl=[];this.Ef=this.Bf=null};
lC.prototype.add=function(a,b){b=b||{};var c={},d=Object.prototype.hasOwnProperty;if(a)c.rpc=a;else throw new bB("Batch entry "+(d.call(b,"id")?'"'+b.id+'" ':"")+"is missing a request method");if(d.call(b,"id")){a=b.id;for(d=0;d<this.Yl.length;d++)if(this.Yl[d].id==a)throw new bB('Batch ID "'+a+'" already in use, please use another.');c.id=a}else{do c.id=String(2147483647*_.Aj()|0);while(d.call(this.Yl,c.id))}c.callback=b.callback;this.Yl.push(c);return c.id};
var mC=function(a){return function(b){var c=b.body;if(b=b.result){for(var d={},e=0,f=b.length;e<f;++e)d[b[e].id]=b[e];a(d,c)}else a(b,c)}};
lC.prototype.execute=function(a){this.Bf=[];for(var b,c,d=0;d<this.Yl.length;d++)b=this.Yl[d],c=b.rpc,this.Bf.push(c.XA(b.id)),this.Ef=c.xe()||this.Ef;c=this.xv(a);a={requests:this.Bf,root:this.Ef};b={};d=a.headers||{};for(var e in d){var f=e;if(Object.prototype.hasOwnProperty.call(d,f)){var h=_.si(d,f);h&&(f=_.qi(f,h)||_.pi(f))&&_.ti(b,f,h)}}_.ti(b,"Content-Type","application/json");e=mC(c);iB({method:"POST",root:a.root||void 0,path:"/rpc",params:a.urlParams,headers:b,body:a.requests||[]}).then(e,
e)};lC.prototype.xv=function(a){var b=this;return function(c,d){b.xE(c,d,a)}};lC.prototype.xE=function(a,b,c){a||(a={});for(var d=0;d<this.Yl.length;d++){var e=this.Yl[d];e.callback&&e.callback(a[e.id]||!1,b)}c&&c(a,b)};jB.jP(function(){return new lC});lC.prototype.add=lC.prototype.add;lC.prototype.execute=lC.prototype.execute;var nC=function(a,b){this.Jca=a;this.Le=b||null;this.Kf=null};nC.prototype.JH=function(a){this.Le=a;this.Kf=this.Le==2?new lC:new gC(this.Jca)};nC.prototype.add=function(a,b){if(!a)throw a=b||_.wf(),new bB("Batch entry "+(_.xf(a,"id")?'"'+a.id+'" ':"")+"is missing a request method");this.Le===null&&this.JH(a.getFormat());this.Le!==a.getFormat()&&NB("Unable to add item to batch.");var c=b&&b.callback;this.Le==1&&c&&(b.callback=function(d){d=oC(d);var e=_.Vg([d]);c(d,e)});return this.Kf.add(a,b)};
nC.prototype.execute=function(a){var b=a&&this.Le==1?function(c){var d=[];_.vn(c,function(f,h){f=oC(f);c[h]=f;d.push(f)});var e=_.Vg(d);a(c,e)}:a;this.Kf&&this.Kf.execute(b)};var oC=function(a){var b=a?_.fj(a,"result"):null;_.yc(b)&&b.error!=null&&(b=MB(b),a={id:a.id,error:b});return a};nC.prototype.then=function(a,b,c){this.Le==2&&NB('The "then" method is not available on this object.');return this.Kf.then(a,b,c)};nC.prototype.add=nC.prototype.add;nC.prototype.execute=nC.prototype.execute;
nC.prototype.then=nC.prototype.then;var pC=function(a){cB.call(this,pC.prototype.xp);this.Db=a;this.AP=!1};_.A(pC,cB);var qC=function(a){a.Db.Xk();var b=a.Db,c=b.qf();return!(fC(c.root,c.path,a.Db.Fu())?ZB([b])!=="batch":1)};_.g=pC.prototype;
_.g.execute=function(a){var b=this;this.AP=!0;if(qC(this))this.Db.execute(a);else{rB(sB(),13).Hb();var c=function(d){if(typeof a==="function"){var e={gapiRequest:{data:{status:d&&d.status,statusText:d&&d.statusText,headers:d&&d.headers,body:d&&d.body}}};if(b.getFormat()===1){a=TB(a);var f={}}var h=d?d.result:!1;d&&d.status==204&&(h=f,delete e.gapiRequest.data.body);a(h,_.Vg(e))}};this.tj().then(c,c)}};
_.g.xp=function(){if(qC(this))return this.Db.tj();this.AP||rB(sB(),16).Hb();return new _.gl(function(a,b){var c=gB(),d=c.add(this.Db,{id:"gapiRequest"});c.then(function(e){var f=e.result;if(f&&(f=f[d])){Object.prototype.hasOwnProperty.call(f,"result")||(f.result=!1);Object.prototype.hasOwnProperty.call(f,"body")||(f.body="");kB(f)?a(f):b(f);return}b(e)},b)},this)};_.g.qf=function(){if(this.Db.qf)return this.Db.qf()};_.g.Xk=function(){this.Db.Xk&&this.Db.Xk()};_.g.xe=function(){if(this.Db.xe)return this.Db.xe()};
_.g.Tj=function(a){this.Db.Tj&&this.Db.Tj(a)};_.g.Fu=function(){return this.Db.Fu()};_.g.getFormat=function(){return this.Db.getFormat?this.Db.getFormat():0};_.g.tj=function(){return this.xp()};pC.prototype.execute=pC.prototype.execute;pC.prototype.then=pC.prototype.then;pC.prototype.getPromise=pC.prototype.tj;var rC="/rest?fields="+encodeURIComponent("kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id")+"&pp=0",sC=function(a,b){return"/discovery/v1/apis/"+(encodeURIComponent(a)+"/"+encodeURIComponent(b)+rC)},uC=function(a,b,c,d){if(_.yc(a)){var e=a;var f=a.name;a=a.version||"v1"}else f=a,a=b;if(!f||!a)throw new bB("Missing required parameters.");var h=c||function(){},k=_.yc(d)?d:{};c=function(l){var m=l&&l.result;if(!m||m.error||!m.name||!l||l.error||l.message||l.message)h(m&&
m.error?m:l&&(l.error||l.message||l.message)?l:new bB("API discovery response missing required fields."));else{l=k.root;l=m.rootUrl!=null?String(m.rootUrl):l;l=typeof l==="string"?l.replace(/([^\/])\/$/,"$1"):void 0;k.root=l;m.name&&m.version&&!m.id&&(m.id=[m.name,m.version].join(":"));m.id&&(k.apiId=m.id,l="client/batchPath/"+m.id,m.batchPath&&!_.Sf(l)&&_.Tf(l,m.batchPath));var n=m.servicePath,p=m.parameters,q=function(v){_.vn(v,function(u){if(!(u&&u.id&&u.path&&u.httpMethod))throw new bB("Missing required parameters");
var w=u.id.split("."),y=window.gapi.client,D;for(D=0;D<w.length-1;D++){var C=w[D];y[C]=y[C]||{};y=y[C]}var I,L;k&&(k.hasOwnProperty("root")&&(I=k.root),k.hasOwnProperty("apiId")&&(L=k.apiId));C=window.gapi.client[w[0]];C.zN||(C.zN={servicePath:n||"",parameters:p,apiId:L});w=w[D];y[w]||(y[w]=_.$a(tC,{path:typeof u.path==="string"?u.path:null,httpMethod:typeof u.httpMethod==="string"?u.httpMethod:null,parameters:u.parameters,parameterName:(u.request||{}).parameterName||"",request:u.request,root:I},
C.zN))})},t=function(v){_.vn(v,function(u){q(u.methods);t(u.resources)})};t(m.resources);q(m.methods);h.call()}};e?c({result:e}):f.indexOf("://")>0?iB({path:f,params:{pp:0,fields:("/"+f).indexOf("/discovery/v1/apis/")>=0?"kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id":'fields["kind"],fields["name"],fields["version"],fields["rootUrl"],fields["servicePath"],fields["resources"],fields["parameters"],fields["methods"],fields["batchPath"],fields["id"]'}}).then(c,c):iB({path:sC(f,
a),root:d&&d.root}).then(c,c)},tC=function(a,b,c,d,e){e=e===void 0?{}:e;var f=b.servicePath||"";_.fd(f,"/")||(f="/"+f);var h=vC(a.path,[a.parameters,b.parameters],c||{});c=h.Ld;var k=h.tga;f=_.Hy(f,h.path);h=k.root;delete k.root;var l=a.parameterName;!l&&_.Au(k)==1&&k.hasOwnProperty("resource")&&(l="resource");if(l){var m=k[l];delete k[l]}m==null&&(m=d);m==null&&a.request&&(_.Di(k)&&(k=void 0),m=k);e=e||{};l=a.httpMethod;l=="GET"&&m!==void 0&&String(m)!=""&&(_.ti(e,"X-HTTP-Method-Override",l),l="POST");
if((m==null||d!=null)&&k)for(var n in k)typeof k[n]==="string"&&(c[n]=k[n]);return iB({path:f,method:l,params:c,headers:e,body:m,root:h||a.root,apiId:b.apiId},1)},vC=function(a,b,c){c=_.Nk(c);var d={};_.un(b,function(e){_.vn(e,function(f,h){var k=f.required;if(f.location=="path")if(Object.prototype.hasOwnProperty.call(c,h))_.$b(a,"{"+h+"}")?(f=encodeURIComponent(String(c[h])),a=a.replace("{"+h+"}",f)):_.$b(a,"{+"+h+"}")&&(f=encodeURI(String(c[h])),a=a.replace("{+"+h+"}",f)),delete c[h];else{if(k)throw new bB("Required path parameter "+
h+" is missing.");}else f.location=="query"&&Object.prototype.hasOwnProperty.call(c,h)&&(d[h]=c[h],delete c[h])})});if(b=c.trace)d.trace=b,delete c.trace;return{path:a,Ld:d,tga:c}};var wC=function(a,b,c,d){var e=b||"v1",f=_.yc(d)?d:{root:d};if(c)uC(a,e,function(h){if(h)if(h.error)c(h);else{var k="API discovery was unsuccessful.";if(h.message||h.message)k=h.message||h.message;c({error:k,code:0})}else c()},f);else return new _.gl(function(h,k){var l=function(m){m?k(m):h()};try{uC(a,e,l,f)}catch(m){k(m)}})},xC=new RegExp(/^((([Hh][Tt][Tt][Pp][Ss]?:)?\/\/[^\/?#]*)?\/)?/.source+/(_ah\/api\/)?(batch|rpc)(\/|\?|#|$)/.source),yC=function(a,b){if(!a)throw new bB("Missing required parameters");
var c=typeof a==="object"?a:{path:a};a=c.callback;delete c.callback;b=new UB(c,b);if(c=!!_.Sf("client/xd4")&&lB()){var d=b.qf();c=d.path;(d=d.root)&&d.charAt(d.length-1)!=="/"&&(d+="/");d&&c&&c.substr(0,d.length)===d&&(c=c.substr(d.length));c=!c.match(xC)}c&&(b=new pC(b));return a?(b.execute(a),null):b};jB.kP(function(a){return yC.apply(null,arguments)});
var zC=function(a,b){if(!a)throw new bB("Missing required parameters");for(var c=a.split("."),d=window.gapi.client,e=0;e<c.length-1;e++){var f=c[e];d[f]=d[f]||{};d=d[f]}c=c[c.length-1];if(!d[c]){var h=b||{};d[c]=function(k){var l=typeof h=="string"?h:h.root;k&&k.root&&(l=k.root);return new UB({method:a,apiVersion:h.apiVersion,rpcParams:k,transport:{name:"googleapis",root:l}},2)}}},AC=function(a){return new nC(a)};jB.iP(function(a){return AC.apply(null,arguments)});
var BC=function(a){if(_.lj.JSONRPC_ERROR_MOD)throw new bB(a+" is discontinued. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");_.Zg.log(a+" is deprecated. See https://developers.google.com/api-client-library/javascript/reference/referencedocs")};_.r("gapi.client.init",function(a){a.apiKey&&_.Tf("client/apiKey",a.apiKey);var b=_.bd(a.discoveryDocs||[],function(d){return wC(d)});if((a.clientId||a.client_id)&&a.scope){var c=new _.gl(function(d,e){var f=function(){_.Sa.gapi.auth2.init.call(_.Sa.gapi.auth2,a).then(function(){d()},e)};dB?f():_.Sa.gapi.load("auth2",{callback:function(){f()},onerror:function(h){e(h||Error("Ja"))}})});b.push(c)}else(a.clientId||a.client_id||a.scope)&&_.Zg.log("client_id and scope must both be provided to initialize OAuth.");
return _.ol(b).then(function(){})});_.r("gapi.client.load",wC);_.r("gapi.client.newBatch",AC);_.r("gapi.client.newRpcBatch",function(){BC("gapi.client.newRpcBatch");return AC()});_.r("gapi.client.newHttpBatch",function(a){BC("gapi.client.newHttpBatch");return new nC(a,0)});_.r("gapi.client.register",function(a,b){BC("gapi.client.register");var c;b&&(c={apiVersion:b.apiVersion,root:b.root});zC(a,c)});_.r("gapi.client.request",yC);
_.r("gapi.client.rpcRequest",function(a,b,c){BC("gapi.client.rpcRequest");if(!a)throw new bB('Missing required parameter "method".');return new UB({method:a,apiVersion:b,rpcParams:c,transport:{name:"googleapis",root:c&&c.root||""}},2)});_.r("gapi.client.setApiKey",function(a){_.Tf("client/apiKey",a);_.Tf("googleapis.config/developerKey",a)});_.r("gapi.client.setApiVersions",function(a){BC("gapi.client.setApiVersions");_.Tf("googleapis.config/versions",a)});_.r("gapi.client.getToken",function(a){return _.tj(a)});
_.r("gapi.client.setToken",function(a,b){a?_.Pw(a,b):_.Qw(b)});_.r("gapi.client.AuthType",{eha:"auto",NONE:"none",Ska:"oauth2",aja:"1p"});_.r("gapi.client.AuthType.AUTO","auto");_.r("gapi.client.AuthType.NONE","none");_.r("gapi.client.AuthType.OAUTH2","oauth2");_.r("gapi.client.AuthType.FIRST_PARTY","1p");
_.lj.FPA_SAMESITE_PHASE2_MOD=!0;
});
// Google Inc.
